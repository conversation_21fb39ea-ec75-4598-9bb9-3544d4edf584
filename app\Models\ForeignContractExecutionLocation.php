<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ForeignContractExecutionLocation extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'foreign_country_location',
        'foreign_city_location'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function foreign_contract_execution_verification(){
        return $this->hasOne('NPA\ACPMS\Models\ForeignContractExecutionVerification','foreign_exec_id');
    }
}
