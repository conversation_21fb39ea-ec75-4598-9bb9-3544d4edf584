<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ForeignContractExecutionVerification;


class ForeignContractExecutionVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['foreign_exec_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $foreignContractExecutionVerification = ForeignContractExecutionVerification::Create(array_only($data, ForeignContractExecutionVerification::$fields));
            return response()->json($foreignContractExecutionVerification, 201, [
                'location' => $foreignContractExecutionVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $foreignContractExecutionId
     * @return \Illuminate\Http\Response
     */
    public function show($foreignContractExecutionId)
    {
        $foreignContractExecutionVerification = ForeignContractExecutionVerification::where('foreign_exec_id', $foreignContractExecutionId)->first();
        return response()->json($foreignContractExecutionVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\ForeignContractExecutionVerification $foreignContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ForeignContractExecutionVerification $foreignContractExecutionVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\ForeignContractExecutionVerification $foreignContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['foreign_exec_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $foreignContractExecutionVerification = ForeignContractExecutionVerification::where('foreign_exec_id', $data['foreign_exec_id'])->first();
            $foreignContractExecutionVerification->update(array_only($data, ForeignContractExecutionVerification::$fields));
            return response()->json([], 204, [
                'location' => $foreignContractExecutionVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\ForeignContractExecutionVerification $foreignContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ForeignContractExecutionVerification $foreignContractExecutionVerification)
    {
        //
    }
}
