<?php

namespace NPA\ACPMS\Types;


use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\API\Internal\Daemons\CommonValue;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\ContractStatusLog;
use NPA\ACPMS\Models\ContractStatusVerification;

class SyncWithAETS
{
  public static function operate()
  {
    try {
      $lastSyncQuery = DB::select('
              select last_update_timestamp from op_sync_aets_data_logs
              order by last_update_timestamp desc
              limit 1
            ');
      $response = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/acpms-sync',
        ['Content-Type' => 'application/json'],
        json_encode([
          'last_sync_timestamp' =>
            $lastSyncQuery && $lastSyncQuery[0] && $lastSyncQuery[0]->last_update_timestamp
              ? $lastSyncQuery[0]->last_update_timestamp : null
        ])
      );

      if ($response->status_code >= 300) {
        throw new Error($response->body);
      }

      $syncableAETSData = json_decode($response->body, true);
      $syncableAETSData = $syncableAETSData == null ? [] : $syncableAETSData;
      $contractStatusPending = DropDowns::getBySlug(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus', 'pending');
      $currentDate = Carbon::now()->toDateTimeString();

      DB::beginTransaction();
      foreach ($syncableAETSData as $contract) {
        $maxPublishTimestamp = $contract['max_created_at_timestamp'];

        // $sectorId = DropDowns::getBySlug(
        //   config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/sector',
        //   $contract['sector_id'])['id'];
        $procurementEntityId = DropDowns::getBySlug(
          config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity',
          $contract['procurement_entity_id'])['id'];
        $procurementTypeId = DropDowns::getBySlug(
          config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType',
          $contract['procurement_type_id'])['id'];
        $procurementMethodId = DropDowns::getBySlug(
          config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementMethod',
          $contract['procurement_method_id'])['id'];

        $donorId = DropDowns::getBySlug(
          config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sourceDonor',
          $contract['donor_slug_id'])['id'];

        $currencyId = DropDowns::getBySlug(
          config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency',
          $contract['currency_slug'])['id'];
        $publishDate = $contract['contract_signing_date'];

        /* contract or general list part data insertion */
        $isExist = DB::select('
          select c.id
          from contracts as c
          where c.contract_number = ?
        ', [$contract['contract_number']]);

        if (count($isExist) == 0) {
          $contractId = DB::table('contracts')
            ->insertGetId([
              'project_id' => $contract['project_id'],
              'npa_identification_number' => $contract['npa_identification_number'],
              'sector_id' => NULL,
              'procurement_entity_id' => $procurementEntityId,
              'contract_number' => $contract['contract_number'],
              'contract_title' => $contract['contract_title'],
              'procurement_type_id' => $procurementTypeId,
              'currency_id' => $currencyId,
              'is_above_threshold' => $contract['is_above_threshold'],
              'procurement_method_id' => $procurementMethodId,
              'published_date' => $publishDate,
              'created_at' => Carbon::now(),
            ]);

          DB::table('contract_details')
            ->updateOrInsert([
              'contract_id' => $contractId,
              'agreement_signature_date' => $publishDate,
              'planned_start_date' => $contract['contract_start_date'],
              'planned_end_date' => $contract['contract_end_date'],
              'estimated_value' => $contract['estimated_cost'],
              'actual_value' => $contract['contract_cost'],
              'created_at' => Carbon::now(),
            ]);

          DB::table('contract_donors')
            ->updateOrInsert([
              'contract_id' => $contractId,
              'donor_id' => $donorId,
              'created_at' => Carbon::now(),
            ]);

          if (isset($contract['selection_method_id'])) {
            $selectionMethodId = DropDowns::getBySlug(
              config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/selectionMethod',
              $contract['selection_method_id'])['id'];

            DB::table('consultancy_contracts_selection_methods')
              ->updateOrInsert([
                'contract_id' => $contractId,
                'selection_method_id' => $selectionMethodId,
                'created_at' => Carbon::now(),
              ]);
          }

          $contractStatusCreated = ContractStatus::create([
            'status_id' => $contractStatusPending['id'],
            'date' => $publishDate,
            'remarks' => '',
            'contract_id' => $contractId
          ]);

          ContractStatusVerification::create([
            'contract_status_id' => $contractStatusCreated['id'],
            'published_date' => $currentDate
          ]);

          ContractStatusLog::create([
            'status_id' => $contractStatusPending['id'],
            'date' => $currentDate,
            'remarks' => '',
            'contract_id' => $contractId,
            'contract_status_id' => $contractStatusCreated['id']

          ]);

          /*other parts data insertion data on it */
          if (isset($contract['npcApproval'])) {
            foreach ($contract['npcApproval'] as $npcApproval) {
              DB::table('contract_approvals')
                ->updateOrInsert([
                  'contract_id' => $contractId,
                  'award_number' => $npcApproval['decision_number'],
                  'award_date' => $npcApproval['decision_date'],
                  'approval_type' => 'npc',
                  'description' => '',
                  'created_at' => Carbon::now(),
                ]);
            }
          }
          $companyGeneralInformationId = DB::table('company_general_informations')
            ->insertGetId([
              'contract_id' => $contractId,
              'is_joint_venture' => $contract['is_joint_venture'],
              'is_confirmed' => null,
              'is_approved' => null,
              'created_at' => Carbon::now(),
            ]);

          $companyId = DB::table('companies')
            ->insertGetId([
              'company_general_information_id' => $companyGeneralInformationId,
              'licence_end_date' => null,
              'address' => $contract['vendor_province_slug'] . $contract['vendor_state'],
              'license_number' => $contract['license_number'],
              'remarks' => '',
              'joint_venture_company_role_id' => null,
              'is_confirmed' => null,
              'is_approved' => null,
              'created_at' => Carbon::now(),
            ]);

          if (isset($contract['bank_slug'])) {
            $bankId = DropDowns::getBySlug(
              config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/acpms/bank/get-by-slug',
              $contract['bank_slug'])['id'];

            DB::table('company_bank_informations')
              ->updateOrInsert([
                'company_id' => $companyId,
                'account_name' => $contract['bank_name_prs'],
                'account_number' => '',
                'swift_code' => '',
                'email' => '',
                'phone_number' => '',
                'bank_id' => $bankId,
                'is_confirmed' => null,
                'is_approved' => null,
                'created_at' => Carbon::now(),
              ]);
          }
        } else {
          Contract::where('contract_number', $contract['contract_number'])->update([
            'contract_title' => $contract['contract_title'],
            'updated_at' => Carbon::now(),
          ]);
        }

      }

      if (!empty($maxPublishTimestamp)) {
        DB::table('op_sync_aets_data_logs')
          ->updateOrInsert([
            'last_update_timestamp' => !empty($lastSyncQuery) ? $lastSyncQuery[0]->last_update_timestamp : $maxPublishTimestamp
          ], [
            'last_update_timestamp' => $maxPublishTimestamp
          ]);
      }
      DB::commit();

    } catch (\Exception $e) {
\Log::info($e);
      DB::rollBack();
      \Log::info($e->getMessage());
    }
  }
}
