<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\models\ContractPerformanceSecurity;
use Illuminate\Http\Request;

class ContractPerformanceSecurityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\models\ContractPerformanceSecurity  $contractPerformanceSecurity
     * @return \Illuminate\Http\Response
     */
    public function show(ContractPerformanceSecurity $contractPerformanceSecurity)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\models\ContractPerformanceSecurity  $contractPerformanceSecurity
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractPerformanceSecurity $contractPerformanceSecurity)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \NPA\ACPMS\models\ContractPerformanceSecurity  $contractPerformanceSecurity
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ContractPerformanceSecurity $contractPerformanceSecurity)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\models\ContractPerformanceSecurity  $contractPerformanceSecurity
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractPerformanceSecurity $contractPerformanceSecurity)
    {
        //
    }
}
