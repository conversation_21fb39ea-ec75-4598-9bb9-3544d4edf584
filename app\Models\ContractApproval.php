<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractApproval extends Model
{
    protected $guarded = ['id'];
    public  static $fields = [
        'award_number',
        'award_date',
        'description',
        'approval_type',
        'contract_id',
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract_approval_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractApprovalVerification');
    }

}
