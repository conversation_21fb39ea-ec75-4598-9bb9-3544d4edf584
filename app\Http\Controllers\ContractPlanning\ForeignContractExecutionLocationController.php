<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ForeignContractExecutionLocation;
use Illuminate\Http\Request;

class ForeignContractExecutionLocationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return void
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return void
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $query = $request->all();
            $createdId = Contract
                ::find($request->input('contract_id'))
                ->foreign_contract_execution_location()
                ->create(array_only($query, ForeignContractExecutionLocation::$fields))->id;
            return response()->json([], 201, [
                'location' => $createdId
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $foreignContractExecutionLocation
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($foreignContractExecutionLocation)
    {
        try {
            $foreignContractLocationData = ForeignContractExecutionLocation::where('contract_id', $foreignContractExecutionLocation)->first();
            if (!$foreignContractLocationData) {
                return response()->json([], 200);
            }
            return response()->json($foreignContractLocationData, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ForeignContractExecutionLocation $foreignContractExecutionLocation
     * @return void
     */
    public function edit(ForeignContractExecutionLocation $foreignContractExecutionLocation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ForeignContractExecutionLocation $foreignContractExecutionLocation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $foreignContractExecutionLocation)
    {
        try {
            ForeignContractExecutionLocation::where('id', $foreignContractExecutionLocation)
                ->update(array_only($request->all(), ForeignContractExecutionLocation::$fields));
            return response()->json([], 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return void
     */
    public function destroy($id)
    {
        try {
            ForeignContractExecutionLocation::where('id', $id)->delete();
            return response()->json([], 204);
        } catch (\Throwable $st) {
            return Error::composeResponse($st);
        }
    }
}
