<?php

use Illuminate\Database\Migrations\Migration;

class VerificationTriggerBeforeUnapprove extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_status_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_status_trigger_before_un_approve
        before update on contract_status_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_status_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_approval_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_approval_trigger_before_un_approve
        before update on contract_approval_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_approval_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_details_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_details_trigger_before_un_approve
        before update on contract_details_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_details_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_general_info_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_general_info_trigger_before_un_approve
        before update on contract_general_information_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_general_information_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_planning_f_a_a_p_p_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_planning_f_a_a_p_p_trigger_before_un_approve
        before update on contract_planning_f_a_a_p_p_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_planning_f_a_a_p_p_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_progress_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_progress_trigger_before_un_approve
        before update on contract_progress_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_progress_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_re_planning_date_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_re_planning_date_trigger_before_un_approve
        before update on contract_re_planning_date_f_a_a_p_p_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contract_re_planning_date_f_a_a_p_p_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `domestic_contract_execution_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger domestic_contract_execution_trigger_before_un_approve
        before update on domestic_contract_execution_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from domestic_contract_execution_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `foreign_contract_execution_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger foreign_contract_execution_trigger_before_un_approve
        before update on foreign_contract_execution_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from foreign_contract_execution_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `liquidated_damages_delivery_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger liquidated_damages_delivery_trigger_before_un_approve
        before update on liquidated_damages_delivery_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from liquidated_damages_delivery_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `p_e_contact_person_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger p_e_contact_person_trigger_before_un_approve
        before update on p_e_contact_person_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from p_e_contact_person_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `subcontract_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger subcontract_verification_trigger_before_un_approve
        before update on subcontract_verifications
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from subcontract_verifications as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `contracts_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contracts_verification_trigger_before_un_approve
        before update on contracts
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from contracts as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `company_general_informations_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger company_general_informations_trigger_before_un_approve
        before update on company_general_informations
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from company_general_informations as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `amendments_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger amendments_verification_trigger_before_un_approve
        before update on amendments
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from amendments as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `npa_other_comments_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger npa_other_comments_trigger_before_un_approve
        before update on npa_other_comments
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from npa_other_comments as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");
        DB::connection()->getpdo()->exec('drop trigger if exists `challenges_and_remarks_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec("
        create trigger challenges_and_remarks_trigger_before_un_approve
        before update on challenges_and_remarks
        for each row
        exit_label: begin
            declare is_confirmed integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            
            select count(*) into is_confirmed from challenges_and_remarks as csv
            WHERE csv.id = new.id and csv.is_confirmed = true;
            
            if is_confirmed = 0 or is_confirmed is null THEN
                signal sqlstate  '45000' set message_text = '|||NPA-AVRCS-0004|||';
            end if;
            set is_confirmed = null;
        end");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_status_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_approval_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_details_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_general_info_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_planning_f_a_a_p_p_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_progress_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contract_re_planning_date_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `domestic_contract_execution_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `foreign_contract_execution_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `liquidated_damages_delivery_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `p_e_contact_person_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `subcontract_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `contracts_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `company_general_informations_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `amendments_verification_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `npa_other_comments_trigger_before_un_approve`');
        DB::connection()->getpdo()->exec('DROP TRIGGER `challenges_and_remarks_trigger_before_un_approve`');

    }
}
