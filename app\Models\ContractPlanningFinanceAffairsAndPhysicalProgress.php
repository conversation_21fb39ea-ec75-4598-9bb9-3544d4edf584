<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractPlanningFinanceAffairsAndPhysicalProgress extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'status_id',
        'month_id',
        'year',
        'amount',
        'physical_progress_percentage',
        'major_activities',
        'remarks',
    ];

    public function contract(){
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

}
