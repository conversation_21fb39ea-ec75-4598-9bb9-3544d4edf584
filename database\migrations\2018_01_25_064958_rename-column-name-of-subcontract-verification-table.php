<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameColumnNameOfSubcontractVerificationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subcontract_verifications', function (Blueprint $table) {
            $table->dropForeign('subcontract_verifications_subcontract_id_foreign');
            $table->dropColumn('subcontract_id');
            $table->unsignedInteger('contract_id')->after('id');
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subcontract_verifications', function (Blueprint $table) {
            $table->dropForeign('subcontract_verifications_contract_id_foreign');
            $table->dropColumn('contract_id');
            $table->unsignedInteger('subcontract_id')->after('id');
            $table->foreign('subcontract_id')
                ->references('id')
                ->on('subcontracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }
}
