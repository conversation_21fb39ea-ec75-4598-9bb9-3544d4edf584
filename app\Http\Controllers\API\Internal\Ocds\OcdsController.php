<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\Ocds;

use function GuzzleHttp\Promise\queue;
use function GuzzleHttp\Psr7\copy_to_string;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\OcdsSyncContractDataOnPublishedLog;

class OcdsController extends Controller
{
    public function release(Request $request)
    {
        if (!$request->npaIds) {
            return response('Npa id is required');
        }
        $npaIds = $request->npaIds;
        $allData = DB::select('
            select 
                    c.id as contract_id, c.contract_number, c.npa_identification_number, c.procurement_type_id, c.project_id,
                    cd.agreement_signature_date as signed_date, c.currency_id, c.procurement_entity_id, c.published_date,
                    ca.award_number,
                    cp.actual_start_date,
                    ifnull(cd.id, 0) as contract_details_id, cd.planned_end_date, cd.actual_value,
                    ifnull(cs.id, 0) as contract_status_id, cs.status_id, cs.date as contract_status_date,
                    ifnull(cgi.id,0) as company_general_information_id,
                        (
                    select 
                        (ifnull(cd.actual_value, 0) +  ifnull(amendment.amount, 0) + ifnull(psc.provisional_sum_and_contingency, 0)) as total_contract_value 
                    from contract_details as cd
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                    
                    ) as amendment
                          on cd.contract_id = amendment.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    where cd.contract_id = c.id
                ) as total_amount
                FROM contracts c
                left join contract_approvals ca on c.id = ca.contract_id 
                left join contract_progresses cp on c.id = cp.contract_id 
                left join contract_details cd on c.id = cd.contract_id
                left join contract_statuses cs on c.id = cs.contract_id
                left join company_general_informations cgi on c.id = cgi.contract_id
                where c.is_published  = true and c.npa_identification_number in (' . $npaIds . ')
                order by c.id ASC
                ');
        foreach ($allData as &$data) {
            $data->contract_tags = DB::select('
                select tag_id
                from contract_tags
                where contract_id = ' . $data->contract_id . '
            ');
            $data->company = DB::select('
                select license_number, joint_venture_company_role_id
                from companies
                where company_general_information_id = ' . $data->company_general_information_id . '
            ');
            $procurementEntityContactPerson = DB::select('
                select 
                    pecp.name,
                    pecp.last_name,
                    pecp.job,
                    pecp.email,
                    pecp.office_contact_number,
                    pecp.personal_contact_number
                from procurement_entity_contact_people pecp 
                where pecp.contract_id = ' . $data->contract_id . '
                ');
            if (isset($procurementEntityContactPerson[0])) {
                $data->procurement_entity_contact_person = $procurementEntityContactPerson[0];
            }
            unset($data->company_general_information_id);
            $subContractId = DB::select('
                    select id from subcontracts where contract_id = ' . $data->contract_id . '
                ');
            if (isset($subContractId[0])) {
                $subContractId = $subContractId[0];
                $subContractId = $subContractId->id;
            } else {
                $subContractId = 0;
            }
            $data->contract_documents = DB::select('
                select id,
                    field_name,
                    format,
                    stored_path,
                    language_id,
                    updated_at
                from attachments
                where field_name in (\'contract_termination_approval\',
                \'agreement\', \'contract_special_conditions\', \'action_plan\',
                 \'subcontract\', \'drawings\', \'contract_signature_authorization_letter\') and 
                table_name in (\'contract_statuses\', \'contract_details\', \'subcontracts\') and 
                foreign_key in (' . $data->contract_status_id . ', ' . $data->contract_details_id . ', ' . $subContractId . ')
            ');
            $data->amendments = DB::select('
                    select
                        a.id,
                        a.amendment_start_date,
                        a.type_id,
                        ifnull(ca.amendment_amount, tca.amendment_amount) as amendment_amount,
                        ifnull(ca.amended_end_date, ifnull(ta.amended_end_date, ifnull(tca.amended_end_date, act.amended_end_date)))  as amended_end_date,
                        ifnull(ca.amendment_reasons,ifnull(ta.amendment_reasons, ifnull(tca.amendment_reasons, act.amendment_reasons))) as amendment_reasons,
                        ifnull(act.terms_in_contract_amendment, \'\') as terms_in_contract_amendment
                    from amendments a
                    left join cost_amendments ca on a.id = ca.amendment_id
                    left join time_amendments ta on a.id = ta.amendment_id
                    left join time_and_cost_amendments tca on a.id = tca.amendment_id
                    left join amendment_in_contract_terms act on a.id = act.amendment_id
                    where a.is_published = true and a.contract_id = ' . $data->contract_id . '
                    order by a.id Asc
            ');
            $contractProgressPaymentsId = DB::select('
              select id from contract_progress_payments where contract_id = ' . $data->contract_id . '
            ');
            $temp = '0';
            for ($i = 0; $i < sizeof($contractProgressPaymentsId); $i++) {
                $temp .= ',' . $contractProgressPaymentsId[$i]->id;
            }
            $contractProgressPaymentsId = $temp;

            $data->implementation_documents = DB::select('
                select id,
                    field_name,
                    format,
                    stored_path,
                    language_id,
                    updated_at
                from attachments
                where field_name in (\'contract_closeout_certificate\',
                \'final_audit_document\', \'contract_termination_approval\', \'contract_M16_payment_form\',
                 \'physical_progress_report\', \'subcontract\') 
                 and table_name in (\'contract_statuses\', \'subcontracts\', \'contract_progress_payments\') 
                and foreign_key in (' . $data->contract_status_id . ', ' . $subContractId . ', ' . $contractProgressPaymentsId . ')
            ');

            $data->transactions = DB::select('
             select 
                cpp.id,
                cpp.year,
                cpp.month_id,
                cpp.amount,
                cpp.updated_at
            from contract_progress_payments cpp
            where cpp.is_published = true and cpp.amount != 0 and cpp.contract_id = ' . $data->contract_id . '
            ');

            $data->milestone = DB::select(
                'SELECT  concat(cpfapp.contract_id, \'-\', cpfapp.id ) as milestone_id,
                    cpfapp.major_activities,
                    cpfapp.remarks,
                    cpfapp.month_id as planned_month, cpfapp.`year` as planned_year,
                    (select inner_cpp.`year` from contract_progress_payments as inner_cpp where inner_cpp.contract_id  = cpfapp.contract_id
                     and inner_cpp.is_published = true and inner_cpp.amount != 0
                     order by inner_cpp.`year` DESC, inner_cpp.month_id DESC LIMIT 1) as financing_actual_year,
                     (select inner_cpp.`year` from contract_progress_payments as inner_cpp where inner_cpp.contract_id  = cpfapp.contract_id 
                     and inner_cpp.is_published = true and inner_cpp.physical_progress_percentage != 0
                     order by inner_cpp.`year` DESC, inner_cpp.month_id DESC LIMIT 1) as delivery_actual_year,
                    (select inner_cpp.month_id from contract_progress_payments as inner_cpp where inner_cpp.contract_id  = cpfapp.contract_id 
                     and inner_cpp.is_published = true and inner_cpp.amount != 0 order by inner_cpp.`year` DESC, inner_cpp.month_id DESC LIMIT 1) as financing_actual_month_id,	
                    (select inner_cpp.month_id from contract_progress_payments as inner_cpp where inner_cpp.contract_id  = cpfapp.contract_id 
                     and inner_cpp.is_published = true and inner_cpp.physical_progress_percentage != 0 order by inner_cpp.`year` DESC, inner_cpp.month_id DESC LIMIT 1) as delivery_actual_month_id,	
                    (select sum(inner_cpfapp.amount) from contract_planning_finance_affairs_and_physical_progresses as inner_cpfapp where inner_cpfapp.contract_id  = cpfapp.contract_id) as totalPlanPayment,
                    (select sum(inner_cpfapp.physical_progress_percentage) from contract_planning_finance_affairs_and_physical_progresses as inner_cpfapp where inner_cpfapp.contract_id  = cpfapp.contract_id) as totalPlanPhysicalProgress,
                    ifnull(cpptotal.amount, 0) as totalActualPayment,
                    ifnull(cpptotal.percentage, 0) as totalActualPhysicalProgress
                    FROM contract_planning_finance_affairs_and_physical_progresses as cpfapp
                    left join contract_progress_payments as cpp 
                                            on cpfapp.contract_id = cpp.contract_id 
                                            and cpfapp.`year` = cpp.`year`
                                            and cpfapp.month_id = cpp.month_id
                                            and cpp.is_published = true
                                            left join (
                                                select 
                                                    p.contract_id,
                                                    sum(ifnull(p.amount, 0)) as amount,
                                                    sum(ifnull(p.physical_progress_percentage, 0)) as percentage
                                                from contract_progress_payments as p
                                                where  p.is_published = true
                                                group by p.contract_id
                                            ) as cpptotal
                                                on cpptotal.contract_id = cpfapp.contract_id
                    where cpfapp.contract_id =  ' . $data->contract_id . '
                    order by cpfapp.`year` DESC, cpfapp.month_id DESC
                    LIMIT 1'
            );
            $data->planning_payments = DB::select('
                    select 
                    month_id,
                    year,
                    amount,
                    physical_progress_percentage,
                    id
                    from contract_planning_finance_affairs_and_physical_progresses
                    where contract_id = ' . $data->contract_id . '
                    order by year, month_id
            
            ');
            $data->actual_payments = DB::select('
                    select 
                    month_id,
                    year,
                    amount,
                    physical_progress_percentage,
                    id
                    from contract_progress_payments
                    where contract_id = ' . $data->contract_id . '
                    order by year, month_id
            
            ');
            $data->re_planning_payments = DB::select('
                    select 
                    crp.month_id,
                    crp.year,
                    crp.amount,
                    crp.physical_progress_percentage,
                    crp.id
                    from contract_re_planning_finance_affairs_and_physical_progresses as crp
                    join contract_re_planning_date_f_a_a_p_ps as cr on cr.id = crp.contract_re_planning_date_f_a_a_p_p_id
                    where cr.id = (
                        select id from contract_re_planning_date_f_a_a_p_ps where contract_id = ' . $data->contract_id . ' order by id desc limit 1
                    )
                    order by crp.year, crp.month_id
            
            ');
//            $data->milestones = DB::select('
//                    select
//                        cpfapp.contract_id,
//                        cpfapp.id,
//                        cpp.id as payment_id,
//                        cpfapp.major_activities,
//                        cpfapp.remarks,
//                        cpfapp.year,
//                        cpfapp.month_id,
//                        cpp.year as actual_year,
//                        cpp.month_id as actual_month_id,
//                        ifnull(cpp.amount, 0) as actual_payment,
//                        ifnull(cpp.physical_progress_percentage, 0) as actual_progress_percentage,
//                        ifnull(cpptotal.amount, 0) as actualTotalAmount,
//                        ifnull(cpptotal.percentage, 0) as actualTotalPercentage,
//                        if(re_plan.amount, re_plan.amount, ifnull(cpfapp.amount, 0)) as planned_payment,
//                        if(re_plan.physical_progress_percentage, re_plan.physical_progress_percentage, ifnull(cpfapp.physical_progress_percentage, 0))
//                         as planned_progress_percentage
//                    from contract_planning_finance_affairs_and_physical_progresses as cpfapp
//                    left join contract_progress_payments as cpp
//                        on cpfapp.contract_id = cpp.contract_id
//                        and cpfapp.year = cpp.year
//                        and cpfapp.month_id = cpp.month_id
//                        and cpp.is_published = true
//                        left join (
//                            select
//                                p.contract_id,
//                                sum(ifnull(p.amount, 0)) as amount,
//                                sum(ifnull(p.physical_progress_percentage, 0)) as percentage
//                            from contract_progress_payments as p
//                            where  p.is_published = true
//                            group by p.contract_id
//                        ) as cpptotal
//                            on cpptotal.contract_id = cpfapp.contract_id
//                    left join (
//                        select
//                            crpp.amount,
//                            crpp.physical_progress_percentage,
//                            crpp.year,
//                            crpp.month_id,
//                            crpp.major_activities,
//                            crpp.remarks,
//                            crp.contract_id
//                        from (
//                            select
//                                max(id) as id,
//                                contract_id
//                            from contract_re_planning_date_f_a_a_p_ps
//                            group by contract_id
//                        ) as crp
//                       left join contract_re_planning_finance_affairs_and_physical_progresses as crpp
//                            on crpp.contract_re_planning_date_f_a_a_p_p_id = crp.id
//                        left join contract_re_planning_date_f_a_a_p_p_verifications as crpv
//                            on crpv.contract_re_planning_date_f_a_a_p_p_id = crp.id
//                            where crpv.is_published = true
//                    ) as re_plan
//                        on re_plan.contract_id = cpfapp.contract_id
//                        and re_plan.year = cpp.year
//                        and re_plan.month_id = cpp.month_id
//                       where cpfapp.contract_id = ' . $data->contract_id . '
//                    union
//                    select
//                        crpd.contract_id,
//                        concat(crpp.id , \'-re\') as id,
//                        cpp.id as payment_id,
//                        crpp.major_activities,
//                        crpp.remarks,
//                        crpp.year,
//                        crpp.month_id,
//                        cpp.year as actual_year,
//                        cpp.month_id as actual_month_id,
//                        ifnull(cpp.amount, 0) as actual_payment,
//                        ifnull(cpp.physical_progress_percentage, 0) as actual_progress_percentage,
//                        ifnull(cpptotal.amount, 0) as actualTotalAmount,
//                        ifnull(cpptotal.percentage, 0) as actualTotalPercentage,
//                        ifnull(crpp.amount, 0) as planned_payment,
//                        ifnull(crpp.physical_progress_percentage, 0) as planned_progress_percentage
//                     from contract_re_planning_finance_affairs_and_physical_progresses as crpp
//                     join contract_re_planning_date_f_a_a_p_ps as crpd on
//                     crpp.contract_re_planning_date_f_a_a_p_p_id =
//                     (select max(id) as id from contract_re_planning_date_f_a_a_p_ps where contract_id = ' . $data->contract_id . ')
//                     left join contract_progress_payments as cpp
//                        on crpd.contract_id = cpp.contract_id
//                        and crpp.year = cpp.year
//                        and crpp.month_id = cpp.month_id
//                        and cpp.is_published = true
//                        left join (
//                            select
//                                p.contract_id,
//                                sum(ifnull(p.amount, 0)) as amount,
//                                sum(ifnull(p.physical_progress_percentage, 0)) as percentage
//                            from contract_progress_payments as p
//                            where p.is_published = true
//                            group by p.contract_id
//                        ) as cpptotal
//                            on cpptotal.contract_id = crpd.contract_id
//                    left join contract_re_planning_date_f_a_a_p_p_verifications as crpv
//                        on crpv.contract_re_planning_date_f_a_a_p_p_id = crpd.id
//                        and crpv.is_published = true
//                     where crpd.contract_id = ' . $data->contract_id . '
//                     and concat(crpp.year, crpp.month_id) not in
//                     (select concat(year, month_id) from contract_planning_finance_affairs_and_physical_progresses where contract_id = ' . $data->contract_id . ')
//                      order by id
//            ');
        }
        return response()->json($allData);
    }

    public function getNpaIds(Request $request)
    {
        $limit = $request->query('limit');
        try {
            $contracts = OcdsSyncContractDataOnPublishedLog::select('npa_identification_number')->limit($limit)->get();
            return response()->json($contracts->pluck('npa_identification_number')->toArray());
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function deleteContractIds(Request $request)
    {
        $ids = $request->query('contract_ids');
        try {
            OcdsSyncContractDataOnPublishedLog::whereIn('contract_id', $ids)->delete();
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function releaseBeforeAmendment(Request $request)
    {
        if (!$request->npaIds) {
            return response('Contract id is required');
        }

        $npaIds = $request->npaIds;
        $allData = DB::select('
            SELECT 
                c.id as contract_id, c.contract_number,c.npa_identification_number, c.project_id,
                cd.agreement_signature_date as signed_date, c.currency_id,c.procurement_entity_id, c.published_date,
                ca.award_number,
                cp.actual_start_date,
                ifnull(cd.id, 0) as contract_details_id, cd.planned_end_date, cd.actual_value,
                ifnull(cs.id, 0) as contract_status_id, cs.status_id,
                ifnull(cgi.id,0) as company_general_information_id
            FROM contracts c
            left join contract_approvals ca on c.id = ca.contract_id 
            left join contract_progresses cp on c.id = cp.contract_id 
            left join contract_details cd on c.id = cd.contract_id
            left join contract_statuses cs on c.id = cs.contract_id
            left join company_general_informations cgi on c.id = cgi.contract_id
            where c.is_published  = true and c.npa_identification_number in (' . $npaIds . ')
            order by c.id ASC
                ');
        foreach ($allData as &$data) {
            $data->company = DB::select('
                select license_number, joint_venture_company_role_id
                from companies
                where company_general_information_id = ' . $data->company_general_information_id . '
            ');
            $procurementEntityContactPerson = DB::select('
                select 
                    pecp.name,
                    pecp.last_name,
                    pecp.job,
                    pecp.email,
                    pecp.office_contact_number,
                    pecp.personal_contact_number
                from procurement_entity_contact_people pecp 
                where pecp.contract_id = ' . $data->contract_id . '
                ');
            if (isset($procurementEntityContactPerson[0])) {
                $data->procurement_entity_contact_person = $procurementEntityContactPerson[0];
            }
            unset($data->company_general_information_id);
            $subContractId = DB::select('
                    select id from subcontracts where contract_id = ' . $data->contract_id . '
                ');
            if (isset($subContractId[0])) {
                $subContractId = $subContractId[0];
                $subContractId = $subContractId->id;
            } else {
                $subContractId = 0;
            }
            $data->contract_documents = DB::select('
                select id,
                    field_name,
                    format,
                    stored_path,
                    language_id,
                    updated_at
                from attachments
                where field_name in (\'contract_termination_approval\',
                \'agreement\', \'contract_special_conditions\', \'action_plan\',
                 \'subcontract\', \'drawings\', \'contract_signature_authorization_letter\') and 
                table_name in (\'contract_statuses\', \'contract_details\', \'subcontracts\') and 
                foreign_key in (' . $data->contract_status_id . ', ' . $data->contract_details_id . ', ' . $subContractId . ')
            ');
            $contractProgressPaymentsId = DB::select('
              select id from contract_progress_payments where contract_id = ' . $data->contract_id . '
            ');
            $temp = '0';
            for ($i = 0; $i < sizeof($contractProgressPaymentsId); $i++) {
                $temp .= ',' . $contractProgressPaymentsId[$i]->id;
            }
            $contractProgressPaymentsId = $temp;

            $data->implementation_documents = DB::select('
                select id,
                    field_name,
                    format,
                    stored_path,
                    language_id,
                    updated_at
                from attachments
                where field_name in (\'contract_closeout_certificate\',
                \'final_audit_document\', \'contract_termination_approval\', \'contract_M16_payment_form\',
                 \'physical_progress_report\', \'subcontract\') 
                 and table_name in (\'contract_statuses\', \'subcontracts\', \'contract_progress_payments\') 
                and foreign_key in (' . $data->contract_status_id . ', ' . $subContractId . ', ' . $contractProgressPaymentsId . ')
            ');
            $data->amendments = DB::select('
                  select a.id, a.contract_id, a.amendment_start_date from amendments a
                    inner join (
                        select min(id) as id from amendments
                        group by contract_id
                    )as max_id on a.id = max_id.id
                    where a.is_published = true and a.contract_id = ' . $data->contract_id . '
            ');
            if (isset($data->amendments[0])) {
                $data->amendments = $data->amendments[0];
            }
            $data->transactions = DB::select('
             select 
                cpp.id,
                cpp.year,
                cpp.month_id,
                cpp.amount,
                cpp.updated_at
            from contract_progress_payments cpp
            where cpp.is_published = true and cpp.amount != 0 and cpp.contract_id = ' . $data->contract_id . '
            ');

            $data->milestones = DB::select('
                    select
                        cpfapp.contract_id,
                        cpfapp.id,
                        cpp.id as payment_id,
                        cpfapp.major_activities,
                        cpfapp.remarks,
                        cpfapp.year,
                        cpfapp.month_id,
                        cpp.year as actual_year,
                        cpp.month_id as actual_month_id,		
                        ifnull(cpp.amount, 0) as actual_payment,
                        ifnull(cpp.physical_progress_percentage, 0) as actual_progress_percentage,
                        ifnull(cpptotal.amount, 0) as actualTotalAmount,
                        ifnull(cpptotal.percentage, 0) as actualTotalPercentage,
                        ifnull(cpfapp.amount, 0) as planned_payment,
                        ifnull(cpfapp.physical_progress_percentage, 0) as planned_progress_percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cpfapp
                    join contracts c on cpfapp.contract_id = c.id and c.is_published = true
                    left join contract_progress_payments as cpp 
                        on cpfapp.contract_id = cpp.contract_id 
                        and cpfapp.year = cpp.year 
                        and cpfapp.month_id = cpp.month_id
                        and cpp.is_published = true
                        left join (
                            select 
                                p.contract_id,
                                sum(ifnull(p.amount, 0)) as amount,
                                sum(ifnull(p.physical_progress_percentage, 0)) as percentage
                            from contract_progress_payments as p
                            where  p.is_published = true
                            group by p.contract_id
                        ) as cpptotal
                            on cpptotal.contract_id = cpfapp.contract_id
                             where cpfapp.contract_id = ' . $data->contract_id . '  
                      order by id
            ');
        }
        return response()->json($allData);
    }

    public function contractBeforeAmendmentIds(Request $request)
    {
        $limit = $request->query('limit');
        try {
            $contracts = OcdsSyncContractDataOnPublishedLog::select('contract_id')
                ->whereIn('contract_id', Amendment::select('contract_id')->where('is_published', true)->get())->limit($limit)->get();
            return response()->json($contracts->pluck('contract_id')->toArray());
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

//        try {
//            $contracts = contract::select('id')
//                ->whereIn('id', Amendment::select('contract_id')->where('is_published', true)->get())->limit($limit)->get();
//            return response()->json($contracts->pluck('id')->toArray());
//        } catch (\Throwable $t) {
//            return Error::composeResponse($t);
//        }
    }

    public function updateOCDSSyncTable(Request $request) {

        $ids = $request->all()['ids'];
        $ids = urldecode($ids);
        $data = DB::select('
                    insert into ocds_sync_contract_data_on_published_logs (contract_id, npa_identification_number)
                    select id, npa_identification_number from contracts where npa_identification_number in ('.$ids.');
            ');
        return response()->json($data);
    }
}
