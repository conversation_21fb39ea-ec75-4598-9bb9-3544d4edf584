<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class NotAllowInvalidDateFormatTrigger extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_approvals_trigger_check_date_format
        before update on contract_approvals
        for each row
        begin
            if (YEAR(new.award_date) < 1900) or
               (MONTH(new.award_date) = 00) or 
               (DAY(new.award_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_approvals_trigger_check_date_format
        before insert on contract_approvals
        for each row
        begin
            if (YEAR(new.award_date) < 1900) or
               (MONTH(new.award_date) = 00) or 
               (DAY(new.award_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_after_delivery_service_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_after_delivery_service_periods_trigger_check_date_format
        before update on after_delivery_service_periods
        for each row
        begin
            if (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_after_delivery_service_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_after_delivery_service_periods_trigger_check_date_format
        before insert on after_delivery_service_periods
        for each row
        begin
            if (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_amendment_in_contract_terms_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_amendment_in_contract_terms_trigger_check_date_format
        before update on amendment_in_contract_terms
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.amended_end_date) = 00) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_amendment_in_contract_terms_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_amendment_in_contract_terms_trigger_check_date_format
        before insert on amendment_in_contract_terms
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.amended_end_date) = 00) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_amendments_trigger_check_date_format
        before update on amendments
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (YEAR(new.amendment_start_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.amendment_start_date) = 00) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.amendment_start_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_amendments_trigger_check_date_format
        before insert on amendments
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (YEAR(new.amendment_start_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.amendment_start_date) = 00) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.amendment_start_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_challenges_and_remarks_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_challenges_and_remarks_trigger_check_date_format
        before update on challenges_and_remarks
        for each row
        begin
            if (YEAR(new.start_date) < 1900) or
               (YEAR(new.end_date) < 1900) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.start_date) = 00) or 
               (MONTH(new.end_date) = 00) or 
               (MONTH(new.published_date) = 00) or 
               (DAY(new.start_date) = 00) or
               (DAY(new.end_date) = 00) or
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_challenges_and_remarks_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_challenges_and_remarks_trigger_check_date_format
        before insert on challenges_and_remarks
        for each row
        begin
            if (YEAR(new.start_date) < 1900) or
               (YEAR(new.end_date) < 1900) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.start_date) = 00) or 
               (MONTH(new.end_date) = 00) or 
               (MONTH(new.published_date) = 00) or 
               (DAY(new.start_date) = 00) or
               (DAY(new.end_date) = 00) or
               (DAY(new.published_date) = 00)
              
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_companies_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_companies_trigger_check_date_format
        before update on companies
        for each row
        begin
            if (YEAR(new.licence_end_date) < 1900) or
               (MONTH(new.licence_end_date) = 00) or 
               (DAY(new.licence_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_companies_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_companies_trigger_check_date_format
        before insert on companies
        for each row
        begin
            if (YEAR(new.licence_end_date) < 1900) or
               (MONTH(new.licence_end_date) = 00) or 
               (DAY(new.licence_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_approvals_trigger_check_date_format
        before update on contract_approvals
        for each row
        begin
            if (YEAR(new.award_date) < 1900) or
               (MONTH(new.award_date) = 00) or 
               (DAY(new.award_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_approvals_trigger_check_date_format
        before insert on contract_approvals
        for each row
        begin
            if (YEAR(new.award_date) < 1900) or
               (MONTH(new.award_date) = 00) or 
               (DAY(new.award_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_details_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_details_trigger_check_date_format
        before update on contract_details
        for each row
        begin
            if (YEAR(new.agreement_signature_date) < 1900) or
               (MONTH(new.agreement_signature_date) = 00) or 
               (DAY(new.agreement_signature_date) = 00) or
               (YEAR(new.planned_start_date) < 1900) or
               (MONTH(new.planned_start_date) = 00) or 
               (DAY(new.planned_start_date) = 00) or
               (YEAR(new.planned_end_date) < 1900) or
               (MONTH(new.planned_end_date) = 00) or 
               (DAY(new.planned_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_details_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_details_trigger_check_date_format
        before insert on contract_details
        for each row
        begin
            if (YEAR(new.agreement_signature_date) < 1900) or
               (MONTH(new.agreement_signature_date) = 00) or 
               (DAY(new.agreement_signature_date) = 00) or
               (YEAR(new.planned_start_date) < 1900) or
               (MONTH(new.planned_start_date) = 00) or 
               (DAY(new.planned_start_date) = 00) or
               (YEAR(new.planned_end_date) < 1900) or
               (MONTH(new.planned_end_date) = 00) or 
               (DAY(new.planned_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_performance_securities_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_performance_securities_trigger_check_date_format
        before update on contract_performance_securities
        for each row
        begin
            if (YEAR(new.start_date) < 1900) or
               (MONTH(new.start_date) = 00) or 
               (DAY(new.start_date) = 00) or
               (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_performance_securities_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_performance_securities_trigger_check_date_format
        before insert on contract_performance_securities
        for each row
        begin
            if (YEAR(new.start_date) < 1900) or
               (MONTH(new.start_date) = 00) or 
               (DAY(new.start_date) = 00) or
               (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_planning_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_planning_advances_trigger_check_date_format
        before update on contract_planning_advances
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_planning_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_planning_advances_trigger_check_date_format
        before insert on contract_planning_advances
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progress_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_progress_advances_trigger_check_date_format
        before update on contract_progress_advances
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progress_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_progress_advances_trigger_check_date_format
        before insert on contract_progress_advances
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_def_liab_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_prog_def_liab_trigger_check_date_format
        before update on contract_progress_defect_liabilities
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_def_liab_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_prog_def_liab_trigger_check_date_format
        before insert on contract_progress_defect_liabilities
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_delay_penalties_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_prog_delay_penalties_trigger_check_date_format
        before update on contract_progress_delay_penalties
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_delay_penalties_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_prog_delay_penalties_trigger_check_date_format
        before insert on contract_progress_delay_penalties
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00) or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_pay_aft_due_dates_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_prog_pay_aft_due_dates_trigger_check_date_format
        before update on contract_progress_payment_after_due_dates
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_pay_aft_due_dates_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_prog_pay_aft_due_dates_trigger_check_date_format
        before insert on contract_progress_payment_after_due_dates
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progress_payments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_progress_payments_trigger_check_date_format
        before update on contract_progress_payments
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progress_payments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_progress_payments_trigger_check_date_format
        before insert on contract_progress_payments
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progress_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_progress_verifications_trigger_check_date_format
        before update on contract_progress_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progress_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_progress_verifications_trigger_check_date_format
        before insert on contract_progress_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progresses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_progresses_trigger_check_date_format
        before update on contract_progresses
        for each row
        begin
            if (YEAR(new.actual_start_date) < 1900) or
               (MONTH(new.actual_start_date) = 00) or 
               (DAY(new.actual_start_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progresses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_progresses_trigger_check_date_format
        before insert on contract_progresses
        for each row
        begin
            if (YEAR(new.actual_start_date) < 1900) or
               (MONTH(new.actual_start_date) = 00) or 
               (DAY(new.actual_start_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format
        before update on contract_re_planning_date_f_a_a_p_p_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format
        before insert on contract_re_planning_date_f_a_a_p_p_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format
        before update on contract_re_planning_date_f_a_a_p_ps
        for each row
        begin
            if (YEAR(new.re_planning_date) < 1900) or
               (MONTH(new.re_planning_date) = 00) or 
               (DAY(new.re_planning_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format
        before insert on contract_re_planning_date_f_a_a_p_ps
        for each row
        begin
            if (YEAR(new.re_planning_date) < 1900) or
               (MONTH(new.re_planning_date) = 00) or 
               (DAY(new.re_planning_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_reten_retu_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_reten_retu_logs_trigger_check_date_format
        before update on contract_retention_returned_logs
        for each row
        begin
            if (YEAR(new.returned_date) < 1900) or
               (MONTH(new.returned_date) = 00) or 
               (DAY(new.returned_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_reten_retu_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_reten_retu_logs_trigger_check_date_format
        before insert on contract_retention_returned_logs
        for each row
        begin
            if (YEAR(new.returned_date) < 1900) or
               (MONTH(new.returned_date) = 00) or 
               (DAY(new.returned_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_retention_returneds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_retention_returneds_trigger_check_date_format
        before update on contract_retention_returneds
        for each row
        begin
            if (YEAR(new.returned_date) < 1900) or
               (MONTH(new.returned_date) = 00) or 
               (DAY(new.returned_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_retention_returneds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_retention_returneds_trigger_check_date_format
        before insert on contract_retention_returneds
        for each row
        begin
            if (YEAR(new.returned_date) < 1900) or
               (MONTH(new.returned_date) = 00) or 
               (DAY(new.returned_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_status_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_status_logs_trigger_check_date_format
        before update on contract_status_logs
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_status_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_status_logs_trigger_check_date_format
        before insert on contract_status_logs
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_status_verif_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cont_status_verif_logs_trigger_check_date_format
        before update on contract_status_verification_logs
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_status_verif_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cont_status_verif_logs_trigger_check_date_format
        before insert on contract_status_verification_logs
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_status_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_status_verifications_trigger_check_date_format
        before update on contract_status_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_status_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_status_verifications_trigger_check_date_format
        before insert on contract_status_verifications
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_statuses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contract_statuses_trigger_check_date_format
        before update on contract_statuses
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_statuses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contract_statuses_trigger_check_date_format
        before insert on contract_statuses
        for each row
        begin
            if (YEAR(new.date) < 1900) or
               (MONTH(new.date) = 00) or 
               (DAY(new.date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contracts_trigger_check_date_format
        before update on contracts
        for each row
        begin
            if (YEAR(new.signed_date) < 1900) or
               (MONTH(new.signed_date) = 00) or 
               (DAY(new.signed_date) = 00)  or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contracts_trigger_check_date_format
        before insert on contracts
        for each row
        begin
            if (YEAR(new.signed_date) < 1900) or
               (MONTH(new.signed_date) = 00) or 
               (DAY(new.signed_date) = 00)  or
               (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_cost_amendments_trigger_check_date_format
        before update on cost_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00)  or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_cost_amendments_trigger_check_date_format
        before insert on cost_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00)  or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_liquidated_damages_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_liquidated_damages_periods_trigger_check_date_format
        before update on liquidated_damages_periods
        for each row
        begin
            if (YEAR(new.expiration_date) < 1900) or
               (MONTH(new.expiration_date) = 00) or 
               (DAY(new.expiration_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_liquidated_damages_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_liquidated_damages_periods_trigger_check_date_format
        before insert on liquidated_damages_periods
        for each row
        begin
            if (YEAR(new.expiration_date) < 1900) or
               (MONTH(new.expiration_date) = 00) or 
               (DAY(new.expiration_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_npa_other_comments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_npa_other_comments_trigger_check_date_format
        before update on npa_other_comments
        for each row
        begin
            if (YEAR(new.report_date) < 1900) or
               (MONTH(new.report_date) = 00) or 
               (DAY(new.report_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_npa_other_comments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_npa_other_comments_trigger_check_date_format
        before insert on npa_other_comments
        for each row
        begin
            if (YEAR(new.report_date) < 1900) or
               (MONTH(new.report_date) = 00) or 
               (DAY(new.report_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_subcontract_signeds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_subcontract_signeds_trigger_check_date_format
        before update on subcontract_signeds
        for each row
        begin
            if (YEAR(new.approval_date) < 1900) or
               (MONTH(new.approval_date) = 00) or 
               (DAY(new.approval_date) = 00) or
               (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00) or
               (YEAR(new.company_licence_end_date) < 1900) or
               (MONTH(new.company_licence_end_date) = 00) or 
               (DAY(new.company_licence_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_subcontract_signeds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_subcontract_signeds_trigger_check_date_format
        before insert on subcontract_signeds
        for each row
        begin
            if (YEAR(new.approval_date) < 1900) or
               (MONTH(new.approval_date) = 00) or 
               (DAY(new.approval_date) = 00) or
               (YEAR(new.end_date) < 1900) or
               (MONTH(new.end_date) = 00) or 
               (DAY(new.end_date) = 00) or
               (YEAR(new.company_licence_end_date) < 1900) or
               (MONTH(new.company_licence_end_date) = 00) or 
               (DAY(new.company_licence_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_time_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_time_amendments_trigger_check_date_format
        before update on time_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00) or
               (YEAR(new.amended_performance_security_end_date) < 1900) or
               (MONTH(new.amended_performance_security_end_date) = 00) or 
               (DAY(new.amended_performance_security_end_date) = 00) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_time_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_time_amendments_trigger_check_date_format
        before insert on time_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00) or
               (YEAR(new.amended_performance_security_end_date) < 1900) or
               (MONTH(new.amended_performance_security_end_date) = 00) or 
               (DAY(new.amended_performance_security_end_date) = 00) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `update_time_and_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_time_and_cost_amendments_trigger_check_date_format
        before update on time_and_cost_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00) or
               (YEAR(new.amended_performance_security_end_date) < 1900) or
               (MONTH(new.amended_performance_security_end_date) = 00) or 
               (DAY(new.amended_performance_security_end_date) = 00) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_time_and_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_time_and_cost_amendments_trigger_check_date_format
        before insert on time_and_cost_amendments
        for each row
        begin
            if (YEAR(new.replanning_start_date) < 1900) or
               (MONTH(new.replanning_start_date) = 00) or 
               (DAY(new.replanning_start_date) = 00) or
               (YEAR(new.amended_performance_security_end_date) < 1900) or
               (MONTH(new.amended_performance_security_end_date) = 00) or 
               (DAY(new.amended_performance_security_end_date) = 00) or
               (YEAR(new.amended_end_date) < 1900) or
               (MONTH(new.amended_end_date) = 00) or 
               (DAY(new.amended_end_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_after_delivery_service_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_after_delivery_service_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_amendment_in_contract_terms_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_amendment_in_contract_terms_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_challenges_and_remarks_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_challenges_and_remarks_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_companies_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_companies_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_approvals_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_details_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_details_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_performance_securities_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_performance_securities_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_planning_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_planning_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progress_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progress_advances_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_def_liab_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_def_liab_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_delay_penalties_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_delay_penalties_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_prog_pay_aft_due_dates_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_prog_pay_aft_due_dates_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progress_payments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progress_payments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_progresses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_progresses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_con_re_pla_dat_f_a_a_p_p_ver_trigger_chek_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_re_plan_date_f_a_a_p_ps_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_reten_retu_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_reten_retu_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_retention_returneds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_retention_returneds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_status_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_status_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cont_status_verif_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cont_status_verif_logs_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_status_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_status_verifications_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contract_statuses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contract_statuses_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_liquidated_damages_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_liquidated_damages_periods_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_npa_other_comments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_npa_other_comments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_subcontract_signeds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_subcontract_signeds_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_time_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_time_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `update_time_and_cost_amendments_trigger_check_date_format`');
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_time_and_cost_amendments_trigger_check_date_format`');
    }
}
