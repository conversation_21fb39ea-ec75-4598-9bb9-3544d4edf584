<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Illuminate\Http\Request;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;

class ContractProgressController extends Controller
{
    public function contractProgressPayments(Request $request)
    {
        $exchangeRate = Contract::find($request->query('contract_id'))['exchange_rate'];
        $contract_id = $request->query('contract_id');
        $contract = Contract::where('id', $contract_id)
            ->where('is_published', 1)
            ->first();
        $contractProgressPayments = $contract->
        contract_progress_payments()
            ->select('id', 'amount', 'month_id', 'year', 'created_at as date', 'major_activities', 'remarks')
            ->get();
        foreach ($contractProgressPayments as &$contractProgressPayment) {
            $contractProgressPayment['amount'] = $contractProgressPayment['amount'] * $exchangeRate;
            $gregorianDate = jDateTime::toGregorian($contractProgressPayment['year'], $contractProgressPayment['month_id'], 1);
            $dateWithLastDayOfMonth = jDateTime::strftime('Y-m-t', strtotime(implode('-', $gregorianDate)));
            $dateWithLastDayOfMonthArray = explode('-', $dateWithLastDayOfMonth);
            $jalaliDate = jDateTime::toGregorian($dateWithLastDayOfMonthArray[0], $dateWithLastDayOfMonthArray[1], $dateWithLastDayOfMonthArray[2]);
            $contractProgressPayment['delivery_date'] = implode('-', $jalaliDate);
        }

        return response()->json($contractProgressPayments, 200);

    }

    public function contractPhysicalProgress(Request $request)
    {
        $contract_id = $request->query('contract_id');
        $contract = Contract::where('id', $contract_id)
            ->where('is_published', 1)
            ->first();
        $contractProgressPayments = $contract->
        contract_progress_payments()
            ->select('id', 'physical_progress_percentage', 'month_id', 'year', 'created_at as date', 'major_activities', 'remarks', 'status_id')
            ->get();
        foreach ($contractProgressPayments as &$contractProgressPayment) {
            $gregorianDate = jDateTime::toGregorian($contractProgressPayment['year'], $contractProgressPayment['month_id'], 1);
            $dateWithLastDayOfMonth = jDateTime::strftime('Y-m-t', strtotime(implode('-', $gregorianDate)));
            $dateWithLastDayOfMonthArray = explode('-', $dateWithLastDayOfMonth);
            $jalaliDate = jDateTime::toGregorian($dateWithLastDayOfMonthArray[0], $dateWithLastDayOfMonthArray[1], $dateWithLastDayOfMonthArray[2]);
            $contractProgressPayment['delivery_date'] = implode('-', $jalaliDate);
        }

        return response()->json($contractProgressPayments, 200);
    }
}
