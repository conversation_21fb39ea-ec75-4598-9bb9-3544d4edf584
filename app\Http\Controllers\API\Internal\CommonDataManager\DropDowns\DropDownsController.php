<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\CommonDataManager\DropDowns;

use Exception;
use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use Throwable;


class DropDownsController extends Controller
{
    public function all(Request $request)
    {
        try {
            $path = urldecode($request->query('path'));
            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
            if ($request->status_code >= 300) {
                throw new Exception('Problem fetching drop down');
            }
            $dropDowns = json_decode($request->body, true);

            return response()->json($dropDowns, 200);
        } catch (Throwable $t) {
            return Error::composeResponse($t, null, $request->body);
        }
    }

    public function district(Request $request)
    {
        $provinceId = $request['province_id'];
        $districtName = $request['name'];
        $provinceSearchParams = $provinceId ? "&search[province_id][match]=full&search[province_id][value]=$provinceId" : '';
        $searchParameters = "&search[name_da][match]=partial&search[name_da][value]=$districtName" . $provinceSearchParams;
        try {
            $remote_request = json_decode(Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/district?name=' . $searchParameters)->body);
            return response()->json($remote_request);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function company(Request $request)
    {
        try {
            $remote_request = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company');
            return response()->json($remote_request);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

}
