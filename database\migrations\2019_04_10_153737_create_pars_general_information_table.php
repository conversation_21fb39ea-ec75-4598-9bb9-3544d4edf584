<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsGeneralInformationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_general_informations', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('progress_analysis_reports_id');
            $table->foreign('progress_analysis_reports_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->text('contract_name')->nullable();
            $table->string('contract_number')->nullable();
            $table->string('procurement_method')->nullable();
            $table->string('selection_method')->nullable();
            $table->string('contract_type')->nullable();
            $table->string('procurement_type')->nullable();
            $table->string('liquidated_damages_period')->nullable();
            $table->string('after_delivery_service_period')->nullable();
            $table->string('contract_value_with_amendments')->nullable();
            $table->string('contract_durations_with_amendments')->nullable();
            $table->string('sector')->nullable();
            $table->string('procurement_entity')->nullable();
            $table->string('award_number')->nullable();
            $table->date('award_date')->nullable();
            $table->date('agreement_signature_date')->nullable();
            $table->text('progress_cancelled_reasons')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_general_informations');
    }
}
