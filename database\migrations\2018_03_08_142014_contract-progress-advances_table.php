<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ContractProgressAdvancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_progress_advances', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('contract_id')->unique();
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->integer('amount');
            $table->string('date')->nullable()->change();
            $table->string('remarks');
            $table->unsignedInteger('guarantee_type_id');
            $table->date('date');
            $table->boolean('is_approved')->nullable();
            $table->boolean('is_confirmed')->nullable();
            $table->boolean('is_published')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_progress_advances');
    }
}
