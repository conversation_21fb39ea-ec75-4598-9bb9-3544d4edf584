<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateParsCostAmendments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pars_cost_amendments', function (Blueprint $table) {
            $table->date('amendment_start_date')->after('amended_performance_security_amount')->nullable();
            $table->date('amended_end_date')->after('amendment_start_date')->nullable();
            $table->dropColumn('amended_performance_security_end_date');
            $table->dropColumn('is_performance_guaranty_renewal_is_legal');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pars_cost_amendments', function (Blueprint $table) {
            $table->dropColumn('amendment_start_date');
            $table->dropColumn('amended_end_date');
            $table->boolean('amended_performance_security_end_date')->after('contract_value_after_amend')->nullable();
            $table->integer('is_performance_guaranty_renewal_is_legal')->after('amended_performance_security_end_date')->nullable();
        });
    }
}
