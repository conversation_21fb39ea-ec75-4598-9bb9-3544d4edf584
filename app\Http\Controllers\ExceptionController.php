<?php

namespace NPA\ACPMS\Http\Controllers;

use Illuminate\Http\Request;
use NPA\ACPMS\Models\Exception as ContractException;
use NPA\ACPMS\Helpers\Error;

class ExceptionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $query = $request->all();
            ContractException::create(array_only($query, ContractException::$fields));
            return response()->json(true, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $data = ContractException::where('contract_id', $id)->first();
            if (!$data) {
                return response()->json([], 404);
            }
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $query = $request->all();
            ContractException::where('id', $id)->update(array_only($query, ContractException::$fields));
            return response()->json(true, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * @param Request $request
     * @param $parentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateVerification(Request $request, $parentId)
    {
        try {
            $verification = $request->all();
            $data = ContractException::where('contract_id', $parentId)->first();
            if ($data) {
                $data->is_confirmed = $verification['is_confirmed'];
                $data->is_approved = $verification['is_approved'];
                $data->save();
            }
            return response()->json(true, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }
}
