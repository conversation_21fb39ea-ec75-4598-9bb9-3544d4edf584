<?php

namespace NPA\ACPMS\Http\Controllers\ContractorPerformanceReport;

use Carbon\Carbon;
use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use MongoDB\Client as Mongo;
use MongoDB\BSON\ObjectId;

class ContractorPerformanceReportSnapshotController extends Controller
{

    public function createSnapshot(Request $request)
    {
        try {
            $collection = (new Mongo)->snapshots->contract_performance_report;
            $contractId = $request->get('contract_id');
            $snapshot = ContractorPerformanceReportController::aggregateReportData($contractId);
            $timestamp = Carbon::now()->toDateTimeString();
            $data = [
                'contract_id' => $snapshot['general_information']['contract_id'],
                'contract_name' => $snapshot['general_information']['contract_name'],
                'contract_number' => $snapshot['general_information']['contract_number'],
                'procurement_entity' => $snapshot['general_information']['procurement_entity'],
                'currency' => $snapshot['general_information']['currency'],
                'actual_value' => $snapshot['general_information']['actual_value'],
                'contract_donor' => $snapshot['general_information']['contract_donor'],
                'vendors' => $snapshot['general_information']['vendors'],
                'total_contract_value_with_amendments' => $snapshot['general_information']['total_contract_value_with_amendments'],
                'approval_date' => $snapshot['general_information']['approval_date'],
                'contract_award_date' => $snapshot['general_information']['contract_award_date'],
                'durations' => $snapshot['general_information']['durations'],
                'progress' => $snapshot['general_information']['progress'],
                'timestamp' => $timestamp,
                'creator_user_id' => $request['user_id'],
                'creator_username' => $request['username'],
                'awarded_contract' => $snapshot['general_information']['awarded_contract'],
                'physical_work_start' => $snapshot['general_information']['physical_work_start'],
                'cost_level_amendment' => $snapshot['general_information']['cost_level_amendment']
            ];
            $collection->insertOne($data);
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            $t->getMessage() === "|||NPA-ACPMS-0010|||" ? $code = "|||NPA-ACPMS-0010|||" : $code = null;
            return Error::composeResponse($t, $code);
        }
    }

    public function index(Request $request)
    {
        try {
            $contractId = $request->query('contract_id');
            $collection = (new Mongo)->snapshots->contract_performance_report;
            $data = $collection->aggregate([
                ['$match' => ['contract_id' => (int)$contractId]]
            ])->toArray();

            return response()->json($data, 200, ['charset' => 'utf-8']);
        } catch
        (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

//    public function showSnapshot($id)
//    {
//        try {
//            $collection = (new Mongo)->snapshots->contract_performance_report;
//            $data = $collection->aggregate([
//                ['$match' => ['contract_id' => $id]]
//            ])->toArray();
//
//            return response()->json($data, 200, ['Content-Type' => 'application/json; charset=utf-8']);
//        } catch
//        (\Throwable $t) {
//            return Error::composeResponse($t);
//        }
//    }
//
    public function destroySnapshot($id)
    {
        $collection = (new Mongo)->snapshots->contract_performance_report;
        $isDeleted = $collection->deleteOne(['_id' => new ObjectId($id)]);
        if (!$isDeleted) {
            return response()->json([], 404);
        }
        return response()->json([], 204);
    }
}
