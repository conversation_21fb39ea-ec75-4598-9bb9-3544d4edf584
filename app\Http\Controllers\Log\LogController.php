<?php

namespace NPA\ACPMS\Http\Controllers\Log;

use Carbon\Carbon;
use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\MongoService;
use NPA\ACPMS\Http\Controllers\Controller;
use MongoDB\Client as Mongo;
Use MongoDB\BSON\Regex;

class LogController extends Controller
{

    public function index(Request $request)
    {
        $aggregate = [];
        if (!empty($request->query())) {
            $match = [];
            $q = $request->query();
            $date_from = '';
            $date_to = '';
            if (isset($q['date_from'])) {
                $date_from = Carbon::parse(str_replace('0430 (Afghanistan Time)', '', $q['date_from']))->toDateTimeString();
            }
            if (isset($q['date_to'])) {
                $date_to = Carbon::parse(str_replace('0430 (Afghanistan Time)', '', $q['date_to']))->toDateTimeString();
            }
            foreach ($request->query() as $key => $value) {
                if ($key === 'page_index' || $key === 'page_size') {
                    continue;
                }
                if ($key === 'url') {
                    if (strpos($value, '?') !== false) {
                        $splitValue = explode('?', $value);
                        $finalValue = $splitValue[0] . '\\?' . $splitValue[1];
                        $match[$key] = new Regex($finalValue, 'i');
                    } else {
                        $match[$key] = new Regex($value, 'i');
                    }
                } elseif ($key === 'sector_id') {
                    $match['sector_id'] = (int)$value;
                } elseif ($key === 'procurement_entity_id') {
                    $match['procurement_entity_id'] = (int)$value;
                } elseif ($key === 'date_from' || $key === 'date_to') {
                    $match['timestamp'] = ['$gte' => $date_from, '$lte' => $date_to];
                } else {
                    $match[$key] = $value;
                }

            }
            if (!empty($match)) {
                $aggregate[] = ['$match' => $match];
            }
        }
        $aggregate [] = [
            '$facet' => [
                'logs' => [
                    ['$sort' => ['_id' => -1]],
                    ['$skip' => (int)$request->query('page_index')],
                    ['$limit' => (int)$request->query('page_size')],
                    ['$project' => ['request_body' => 0, 'response_content' => 0]]
                ],
                'pagination' => [
                    ['$group' => ['_id' => null, 'count' => ['$sum' => 1]]],
                ],

            ],

        ];
        try {
            $collection = MongoService::getInstance()->log->apiLogs;
            $data = $collection->aggregate($aggregate, ['allowDiskUse' => true])->toArray();

            return response()->json($data && $data[0] ? $data[0] : [], 200, ['Content-Type' => 'application/json; charset=utf-8']);
        } catch
        (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
