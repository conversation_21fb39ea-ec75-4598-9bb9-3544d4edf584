<?php

namespace NPA\ACPMS\Helpers;

use Exception;

class Helper
{
    public static function assignContractToUser($contractId, $userId)
    {
        $data['record_id'] = $contractId;
        $data['user_role_id'] = $userId;
        $response = Http::post(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/system-specific/acpms/assign-contract-to-user', [], $data);
        $createdUserRoleRecordId = json_decode($response->body, true);
        if ($response->status_code === 201) {
            return;
        } else {
            throw new Exception($createdUserRoleRecordId);
        }
    }

    public static function assignOrRemoveContractAccessToUser($data)
    {
        $response = Http::post(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/system-specific/acpms/assign-or-remove-contract-access', [], $data);
        $createdUserRoleRecordId = json_decode($response->body, true);
        if ($response->status_code === 200 || $response->status_code === 201) {
            return;
        } else {
            throw new Exception(json_encode($createdUserRoleRecordId));
        }

    }

    public static function getSpecialists($sectorId = null)
    {
        try {
            $url = $sectorId ? ('api/system-specific/acpms/get-specialists?sector_id=' . $sectorId) :
                'api/system-specific/acpms/get-specialists';
            return get($url);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getContractSpecialist($contractId)
    {
        try {
            return get('api/system-specific/acpms/get-contract-specialist?contract_id=' . $contractId);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getContractManagers($procurementEntityId = null)
    {
        try {
            $url = $procurementEntityId ? ('api/system-specific/acpms/get-contract-managers?procurement_entity_id=' . $procurementEntityId) :
                'api/system-specific/acpms/get-contract-managers';
            return get($url);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getUsersByIds($ids)
    {
        try {
//            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
//                'api/system-specific/acpms/get-users-by-ids?ids[]=' . implode(",", $ids)
//                , []
//                , ['timeout' => 120]);
            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
                'api/system-specific/acpms/get-users-by-ids', [], $ids);
            $decodedResponseBody = json_decode($request->body);
            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $decodedResponseBody);
            }
            return $decodedResponseBody;
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getRolesByIds($ids)
    {
        try {
            if (count($ids) === 0) {
                return [];
            }
            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
                'api/system-specific/acpms/get-roles-by-ids?ids[]=' . implode(",", $ids), [], ['timeout' => 120]);
            $roleList = json_decode($request->body, true);

            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $roleList);
            }
            return $roleList;
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getContractContractManager($contractId)
    {
        try {
            return get('api/system-specific/acpms/get-contract-contract-manager?contract_id=' . $contractId);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getAwardAuthority($procurementEntityId = null)
    {
        try {
            $url = $procurementEntityId ? ('api/system-specific/acpms/get-award-authority?procurement_entity_id=' . $procurementEntityId) :
                'api/system-specific/acpms/get-award-authority';
            return get($url);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getCpmManager($sectorId = null)
    {
        try {
            $url = $sectorId ? ('api/system-specific/acpms/get-cpm-manager?sector_id=' . $sectorId) :
                'api/system-specific/acpms/get-cpm-manager';
            return get($url);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    // public static function getCpmDirector()
    // {
    //     try {
    //         return get('api/system-specific/acpms/get-cpm-director');
    //     } catch (\Throwable $t) {
    //         return Error::composeResponse($t);

    //     }

    // }

    public static function getResourceAssigned($userId)
    {
        try {

            //todo send session to usm
            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/resources/assigned?user_role_id=' . $userId, [], ['timeout' => 120]);
            $resourcesAssigned = json_decode($request->body);
            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $resourcesAssigned);
            }
            return $resourcesAssigned;
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }

    }

    public static function getUserContextAccessList($userId)
    {
        try {

            //todo send session to usm
            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
                'api/contexts/dashboards?user_role_id=' . $userId, [], ['timeout' => 120]);
            $list = json_decode($request->body);
            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $list);
            }
            return $list;
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }
    }

}

function get($url)
{
    $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
        $url, [], ['timeout' => 120]);
    $decodedResponseBody = json_decode($request->body);

    if ($request->status_code == 404) {
        return null;
    }

    if ($request->status_code >= 300) {
        throw new Error('|||NPA-USM-0004|||', $decodedResponseBody);
    }

    return $decodedResponseBody;
}
