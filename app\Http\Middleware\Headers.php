<?php

namespace NPA\ACPMS\Http\Middleware;

use Closure;

class Headers
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        $headers = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, PUT, POST, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, x-xsrf-token, Authorization, Accept, X-Requested-With, Application',
            'Access-Control-Expose-Headers' =>  'Authorization, location, x-pagination-size'
        ];

        foreach ($headers as $key => $value) {
            $response->headers->set($key, $value);
        }


        return $response;
    }
}
