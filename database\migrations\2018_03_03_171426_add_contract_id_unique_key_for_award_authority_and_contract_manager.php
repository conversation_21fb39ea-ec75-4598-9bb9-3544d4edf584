<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddContractIdUniqueKeyForAwardAuthorityAndContractManager extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::select('
        DELETE c1 FROM 
        user_award_authorities as c1
        inner join user_award_authorities as c2
        where c1.id < c2.id and c1.user_id = c2.user_id
        ');
        DB::select('
        DELETE c1 FROM 
        user_contract_managers as c1
        inner join user_contract_managers as c2
        where c1.id < c2.id and c1.user_id = c2.user_id
        ');

        Schema::table('user_award_authorities', function (Blueprint $table) {
            $table->unique([
                'user_id'
            ]);
        });
        Schema::table('user_contract_managers', function (Blueprint $table) {
            $table->unique([
                'user_id'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_award_authorities', function (Blueprint $table) {
            $table->dropUnique('user_award_authorities_user_id_unique');
        });
        Schema::table('user_contract_managers', function (Blueprint $table) {
            $table->dropUnique('user_contract_managers_user_id_unique');
        });
    }
}
