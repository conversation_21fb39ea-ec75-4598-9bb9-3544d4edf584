<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingCreateLiquidatedDamagesAfterDeliveryServicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('liquidated_damages_after_delivery_services', function (Blueprint $table) {
            $table->dropForeign('liquidated_damages_after_delivery_services_contract_id_foreign');
            $table->foreign('contract_id')->references('id')->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('liquidated_damages_after_delivery_services', function (Blueprint $table) {
            $table->dropForeign('liquidated_damages_after_delivery_services_contract_id_foreign');
            $table->foreign('contract_id')->references('id')->on('contracts');
        });
    }
}
