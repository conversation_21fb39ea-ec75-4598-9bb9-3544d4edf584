<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class DomesticContractExecutionLocation extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'district_id',
        'village',
        'climate_situation_id',
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
