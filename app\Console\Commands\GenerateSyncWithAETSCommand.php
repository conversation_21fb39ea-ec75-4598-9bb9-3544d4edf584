<?php

namespace NPA\ACPMS\Console\Commands;



use Illuminate\Console\Command;
use NPA\ACPMS\Types\SyncWithAETS;

class GenerateSyncWithAETSCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ageops:generate-sync-with-aets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs the internal list of aets data in ACPMS that comes from AETS';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
       SyncWithAETS::operate();
    }
}
