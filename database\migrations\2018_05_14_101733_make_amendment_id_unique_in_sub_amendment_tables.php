<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakeAmendmentIdUniqueInSubAmendmentTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->unique([
                'amendment_id'
            ]);
        });
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->unique([
                'amendment_id'
            ]);
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->unique([
                'amendment_id'
            ]);
        });
        Schema::table('amendment_in_contract_terms', function (Blueprint $table) {
            $table->unique([
                'amendment_id'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->dropUnique('time_amendments_amendment_id_unique');
        });
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->dropUnique('cost_amendments_amendment_id_unique');
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->dropUnique('time_and_cost_amendments_amendment_id_unique');
        });
        Schema::table('amendment_in_contract_terms', function (Blueprint $table) {
            $table->dropUnique('amendment_in_contract_terms_amendment_id_unique');
        });
    }
}
