<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingCreateContractApprovalVerificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_approval_verifications', function (Blueprint $table) {
            $table->dropForeign('contract_approval_verifications_contract_approval_id_foreign');
            $table->foreign('contract_approval_id')->references('id')->on('contract_approvals')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_approval_verifications', function (Blueprint $table) {
            $table->dropForeign('contract_approval_verifications_contract_approval_id_foreign');
            $table->foreign('contract_approval_id')->references('id')->on('contract_approvals');
        });
    }
}
