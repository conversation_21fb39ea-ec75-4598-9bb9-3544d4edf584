<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsContractValue extends Model
{
    public static $fields = [
        'p_a_r_id',
        'estimated_value',
        'actual_value',
        'value_difference',
        'percentage_difference',
        'provisional_sum_and_contingency_value',
        'is_provisional_sum_and_contingency_applicable'
    ];

    protected $guarded = ['id'];
}
