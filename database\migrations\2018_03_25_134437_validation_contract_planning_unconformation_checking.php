<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ValidationContractPlanningUnconformationChecking extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // DB::connection()->getPdo()->exec("
        // create trigger validation_contract_approval_verifications_enforce_checking
        //     before update
        //     on contract_approval_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select ca.contract_id
        //         into contract_id
        //         from contract_approvals as ca
        //         join contract_approval_verifications as cav
        //             on ca.id = cav.contract_approval_id 
        //             and cav.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_p_e_contact_person_verifications_enforce_checking
        //     before update
        //     on p_e_contact_person_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select pecpv.contract_id
        //         into contract_id
        //         from p_e_contact_person_verifications as pecpv
        //         where pecpv.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_d_c_e_verifications_enforce_checking
        //     before update
        //     on domestic_contract_execution_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select dcev.contract_id
        //         into contract_id
        //         from domestic_contract_execution_verifications as dcev
        //         where dcev.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_f_c_e_v_enforce_checking
        //     before update
        //     on foreign_contract_execution_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select fce.contract_id
        //         into contract_id
        //         from foreign_contract_execution_locations as fce
        //         join foreign_contract_execution_verifications as fcev
        //             on fce.id=fcev.foreign_exec_id
        //         where fcev.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_contract_details_verifications_enforce_checking
        //     before update
        //     on contract_details_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select cd.contract_id
        //         into contract_id
        //         from contract_details as cd
        //         join contract_details_verifications as cdv
        //             on cd.id = cdv.contract_detail_id 
        //             and cdv.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_company_general_informations_enforce_checking
        //     before update
        //     on company_general_informations
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select cgi.contract_id
        //         into contract_id
        //         from company_general_informations as cgi
        //         where cgi.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_companies_enforce_checking
        //     before update
        //     on companies
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select cgi.contract_id
        //         into contract_id
        //         from company_general_informations as cgi
        //         join companies as c
        //             on cgi.id = c.company_general_information_id 
        //             and c.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_company_bank_informations_enforce_checking
        //     before update
        //     on company_bank_informations
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select cgi.contract_id
        //         into contract_id
        //         from company_general_informations as cgi
        //         join companies as c
        //             on cgi.id = c.company_general_information_id
        //         join company_bank_informations as cbi
        //             on c.id= cbi.company_id
        //             and cbi.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_subcontract_verifications_enforce_checking
        //     before update
        //     on subcontract_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select sc.contract_id
        //         into contract_id
        //         from subcontract_verifications as sc
        //         where sc.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_c_p_f_a_a_p_p_v_enforce_checking
        //     before update
        //     on contract_planning_f_a_a_p_p_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select cpfaappv.contract_id
        //         into contract_id
        //         from contract_planning_f_a_a_p_p_verifications as cpfaappv
        //         where cpfaappv.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");

        // DB::connection()->getPdo()->exec("
        // create trigger validation_l_d_d_v_enforce_checking
        //     before update
        //     on liquidated_damages_delivery_verifications
        //     for each row
        //     begin
                            
        //         declare contract_id int default null;
                
        //         select lddv.contract_id
        //         into contract_id
        //         from liquidated_damages_delivery_verifications as lddv
        //         where lddv.id=new.id;
                
        //         if new.is_confirmed=0 and new.is_confirmed != old.is_confirmed then
        //             call validation_enforce_checking_contract_planning_confirmation(contract_id);
        //         end if;
        //     end
        // ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // DB::connection()->getPdo()->exec("drop trigger validation_contract_approval_verifications_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_p_e_contact_person_verifications_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_d_c_e_verifications_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_f_c_e_v_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_contract_details_verifications_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_company_general_informations_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_companies_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_company_bank_informations_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_subcontract_verifications_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_c_p_f_a_a_p_p_v_enforce_checking");
        // DB::connection()->getPdo()->exec("drop trigger validation_l_d_d_v_enforce_checking");
    }
}
