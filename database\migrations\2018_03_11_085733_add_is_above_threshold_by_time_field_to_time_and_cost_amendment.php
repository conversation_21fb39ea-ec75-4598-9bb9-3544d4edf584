<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsAboveThresholdByTimeFieldToTimeAndCostAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->boolean('is_above_threshold_by_time')
                ->nullable()
                ->after('is_above_threshold');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('time_and_cost_amendments', 'is_above_threshold_by_time')) {
            Schema::table('time_and_cost_amendments', function (Blueprint $table) {
                $table->dropColumn('is_above_threshold_by_time');
            });
        }
    }
}
