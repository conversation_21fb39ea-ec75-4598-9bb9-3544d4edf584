<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusCancelledLog extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'cancelled_reason',
        'is_loss_implementable',
        'loss_amount',
        'is_contractor_deprived',
        'contract_status_id',
        'contract_status_cancelled_id',
        'contract_status_log_id'
    ];


    public function contract_status_cancelled()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusCancelled');
    }

    public function contract_status_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusLogs');
    }
}
