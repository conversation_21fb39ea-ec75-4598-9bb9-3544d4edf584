<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractProgressAdvance;

class ContractProgressAdvanceController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $advance = ContractProgressAdvance::where('contract_id', $contractId)->first();
        if (!$advance) {
            return response()->json([], 404);
        }

        $attachments = $advance->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
        foreach ($attachments as $key => $value) {
            $index = $attachments[$key]['field_name'];
            $att = [];
            foreach ($attachments as $k => $v) {
                if ($v['field_name'] == $index && $v['table_name'] == 'contract_progress_advances') {
                    array_push($att, $v);
                }
            }
            $advance[$attachments[$key]['field_name']] = $att;
        }
        return response()->json($advance);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->all();
            $data['guarantee_type_id'] = $data['guarantee_type']['id'];
            unset($data['guarantee_type']);
            $files['advance_payment_guarantee'] = $data['advance_payment_guarantee'];
            unset($data['advance_payment_guarantee']);
            $advance = ContractProgressAdvance::create(array_only($data, ContractProgressAdvance::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $advance, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $advance['id'],
            ]);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressAdvance $contractProgressAdvance
     * @return \Illuminate\Http\Response
     */
    public function show(ContractProgressAdvance $contractProgressAdvance)
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressAdvance $contractProgressAdvance
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractProgressAdvance $contractProgressAdvance)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractProgressAdvance $contractProgressAdvanceId
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractProgressAdvanceId)
    {
        try {

            DB::beginTransaction();
            $data = $request->all();
            $data['guarantee_type_id'] = $data['guarantee_type']['id'];
            unset($data['guarantee_type']);
            $files['advance_payment_guarantee'] = $data['advance_payment_guarantee'];
            unset($data['advance_payment_guarantee']);
            $advance = ContractProgressAdvance::where('id', $contractProgressAdvanceId)->first();
            ContractProgressAdvance::where('id', $contractProgressAdvanceId)->update(array_only($data, ContractProgressAdvance::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $advance, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::callBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressAdvance $contractProgressAdvance
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractProgressAdvance $contractProgressAdvance)
    {
        //
    }

    public function verification(Request $request)
    {
        try {
            $data = $request->all();
            $parent_id = $data['parent_id'];
            if (isset($data['is_published']) && $data['is_published']) {
                ContractProgressAdvance::where('contract_id', $parent_id)
                    ->update(
                        [
                            'is_published' => $data['is_published'],
                        ]
                    );
                return response()->json([], 204);
            }

            if (isset($data['is_confirmed'])) {
                ContractProgressAdvance::where('contract_id', $parent_id)
                    ->update(
                        [
                            'is_confirmed' => $data['is_confirmed'],
                            'is_approved' => $data['is_approved'],
                            'is_published' => $data['is_published'],
                        ]
                    );
                return response()->json([], 204);
            }

            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function verificationShow(Request $request)
    {
        try {
            $contractId = $request->id;
            $contractProgress = ContractProgressAdvance::select([
                    'is_confirmed',
                    'is_approved',
                    'is_published',
                ])->where('contract_id', $contractId)->first();
            if (!isset($contractProgress)) {
                return response()->json([], 404);
            }else{
            return response()->json($contractProgress, 200);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
