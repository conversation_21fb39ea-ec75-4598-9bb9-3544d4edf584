<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class GregorianToJalaliDateFunction extends Migration
{
    public function up()
    {
        DB::unprepared('
            create function `__mydiv`(`a` int, `b` int) 
            returns bigint(20)
            deterministic
            begin
                return floor(a / b);
            end
        ');
        DB::unprepared('
            create function `_gdmarray`(`m` smallint) 
            returns smallint(2)
            deterministic
            begin
                case m
                    when 0 then return 31;
                    when 1 then return 28;
                    when 2 then return 31;
                    when 3 then return 30;
                    when 4 then return 31;
                    when 5 then return 30;
                    when 6 then return 31;
                    when 7 then return 31;
                    when 8 then return 30;
                    when 9 then return 31;
                    when 10 then return 30;
                    when 11 then return 31;
                end case;
            end
        ');
        DB::unprepared('
                create function `_jdmarray`(`m` smallint) returns smallint(2)
                deterministic
                begin
                    case m
                        when 0 then return 31;
                        when 1 then return 31;
                        when 2 then return 31;
                        when 3 then return 31;
                        when 4 then return 31;
                        when 5 then return 31;
                        when 6 then return 30;
                        when 7 then return 30;
                        when 8 then return 30;
                        when 9 then return 30;
                        when 10 then return 30;
                        when 11 then return 29;
                    end case;
                
                end;;');
        DB::unprepared("create function `jdate`(`gdate` datetime) returns char(100) charset utf8
        deterministic
          begin
          
          	declare 
          		i,
          		gy, gm, gd,
          		g_day_no, j_day_no, j_np,
          		jy, jm, jd int default 0; /* can be unsigned int? */
          	declare resout char(100);
          	declare ttime char(20);
          
          	set gy = year(gdate) - 1600;
          	set gm = month(gdate) - 1;
          	set gd = day(gdate) - 1;
          	set ttime = time(gdate);
          	set g_day_no = ((365 * gy) + __mydiv(gy + 3, 4) - __mydiv(gy + 99, 100) + __mydiv (gy + 399, 400));
          	set i = 0;
          
          	while (i < gm) do
          		set g_day_no = g_day_no + _gdmarray(i);
          		set i = i + 1; 
          	end while;
          
          	if gm > 1 and ((gy % 4 = 0 and gy % 100 <> 0)) or gy % 400 = 0 then 
          		set g_day_no =	g_day_no + 1;
          	end if;
          	
          	set g_day_no = g_day_no + gd; 
          	set j_day_no = g_day_no - 79;
          	set j_np = j_day_no div 12053;
          	set j_day_no = j_day_no % 12053;
          	set jy = 979 + 33 * j_np + 4 * __mydiv(j_day_no, 1461);
          	set j_day_no = j_day_no % 1461;
          
          	if j_day_no >= 366 then 
          		set jy = jy + __mydiv(j_day_no - 1, 365);
          		set j_day_no = (j_day_no - 1) % 365;
          	end if;
          
          	set i = 0;
          
          	while (i < 11 and j_day_no >= _jdmarray(i)) do
          		set j_day_no = j_day_no - _jdmarray(i);
          		set i = i + 1;
          	end while;
          
          	set jm = i + 1;
          	set jd = j_day_no + 1;
          	set resout = concat_ws ('-', jy, jm, jd);
          
          	if (ttime <> '00:00:00') then
          		set resout = concat_ws(' ', resout, ttime);
          	end if;
          
          	return resout;
          end;;");
        DB::unprepared("create function `jmonth`(`gdate` datetime) returns char(100) charset utf8
        deterministic
          begin
          
          	declare 
          		i,
          		gy, gm, gd,
          		g_day_no, j_day_no, j_np,
          		jy, jm, jd int default 0; /* can be unsigned int? */
          	declare resout char(100);
          	declare ttime char(20);
          
          	set gy = year(gdate) - 1600;
          	set gm = month(gdate) - 1;
          	set gd = day(gdate) - 1;
          	set ttime = time(gdate);
          	set g_day_no = ((365 * gy) + __mydiv(gy + 3, 4) - __mydiv(gy + 99, 100) + __mydiv(gy + 399, 400));
          	set i = 0;
          
          	while (i < gm) do
          		set g_day_no = g_day_no + _gdmarray(i);
          		set i = i + 1; 
          	end while;
          
          	if gm > 1 and ((gy % 4 = 0 and gy % 100 <> 0)) or gy % 400 = 0 then 
          		set g_day_no = g_day_no + 1;
          	end if;
          	
          	set g_day_no = g_day_no + gd;
          	set j_day_no = g_day_no - 79;
          	set j_np = j_day_no div 12053;
          	set j_day_no = j_day_no % 12053;
          	set jy = 979 + 33 * j_np + 4 * __mydiv(j_day_no, 1461);
          	set j_day_no = j_day_no % 1461;
          
          	if j_day_no >= 366 then 
          		set jy = jy + __mydiv(j_day_no - 1, 365);
          		set j_day_no =(j_day_no - 1) % 365;
          	end if;
          
          	set i = 0;
          
          	while (i < 11 and j_day_no >= _jdmarray(i)) do
          		set j_day_no = j_day_no - _jdmarray(i);
          		set i = i + 1;
          	end while;
          
          	set jm = i + 1;
          	set jd = j_day_no + 1;
          	return jm;
          end;;
          delimiter ;
          
          -- ----------------------------
          -- function structure for `pmonthname`
          -- ----------------------------
          drop function if exists `pmonthname`;
          delimiter ;;
          create definer=`root`@`localhost` function `pmonthname`(`gdate` datetime) returns varchar(100) charset utf8
          begin
          
          	case pmonth(gdate)
          		when 1 then return 'فروردین';
          		when 2 then return 'اردیبهشت';
          		when 3 then	return 'خرداد';
          		when 4 then	return 'تیر';
          		when 5 then	return 'مرداد';
          		when 6 then	return 'شهریور';
          		when 7 then	return 'مهر';
          		when 8 then	return 'آبان';
          		when 9 then	return 'آذر';
          		when 10 then return	'دی';
          		when 11 then return	'بهمن';
          		when 12 then return	'اسفند';
          	end case;
          
          end;;");
        DB::unprepared('
        create function `jyear`(`gdate` datetime) returns char(100) charset utf8
        deterministic
            begin
                declare
                    i,
                    gy, gm, gd,
                    g_day_no, j_day_no, j_np,
                    jy, jm, jd int default 0; /* can be unsigned int? */
                declare resout char(100);
                declare ttime char(20);
            
                set gy = year(gdate) - 1600;
                set gm = month(gdate) - 1;
                set gd = day(gdate) - 1;
                set ttime = time(gdate);
                set g_day_no = ((365 * gy) + __mydiv(gy + 3, 4) - __mydiv(gy + 99, 100) + __mydiv(gy + 399, 400));
                set i = 0;
            
                while (i < gm) do
                    set g_day_no = g_day_no + _gdmarray(i);
                    set i = i + 1;
                end while;
            
                if gm > 1 and ((gy % 4 = 0 and gy % 100 <> 0)) or gy % 400 = 0 then
                    set g_day_no =	g_day_no + 1;
                end if;
                
                set g_day_no = g_day_no + gd;
                set j_day_no = g_day_no - 79;
                set j_np = j_day_no div 12053;
                set j_day_no = j_day_no % 12053;
                set jy = 979 + 33 * j_np + 4 * __mydiv(j_day_no, 1461);
                set j_day_no = j_day_no % 1461;
            
                if j_day_no >= 366 then
                    set jy = jy + __mydiv(j_day_no - 1, 365);
                    set j_day_no = (j_day_no - 1) % 365;
                end if;
            
                set i = 0;
            
                while (i < 11 and j_day_no >= _jdmarray(i)) do
                    set j_day_no = j_day_no - _jdmarray(i);
                    set i = i + 1;
                end while;
            
                set jm = i + 1;
                set jd = j_day_no + 1;
                return jy;
            end;;');
        DB::unprepared('
        create function `jday`(`gdate` datetime) returns char(100) charset utf8
        deterministic
            begin
            
                declare
                    i,
                    gy, gm, gd,
                    g_day_no, j_day_no, j_np,
                    jy, jm, jd int default 0; /* can be unsigned int? */
                declare resout char(100);
                declare ttime char(20);
            
                set gy = year(gdate) - 1600;
                set gm = month(gdate) - 1;
                set gd = day(gdate) - 1;
                set ttime = time(gdate);
                set g_day_no = ((365 * gy) + __mydiv(gy + 3, 4) - __mydiv(gy + 99 , 100) + __mydiv(gy + 399, 400));
                set i = 0;
            
                while (i < gm) do
                    set g_day_no = g_day_no + _gdmarray(i);
                    set i = i + 1;
                end while;
            
                if gm > 1 and ((gy % 4 = 0 and gy % 100 <> 0)) or gy % 400 = 0 then
                    set g_day_no = g_day_no + 1;
                end if;
                
                set g_day_no = g_day_no + gd;
                set j_day_no = g_day_no - 79;
                set j_np = j_day_no div 12053;
                set j_day_no = j_day_no % 12053;
                set jy = 979 + 33 * j_np + 4 * __mydiv(j_day_no, 1461);
                set j_day_no = j_day_no % 1461;
            
                if j_day_no >= 366 then
                    set jy = jy + __mydiv(j_day_no - 1, 365);
                    set j_day_no = (j_day_no-1) % 365;
                end if;
            
                set i = 0;
            
                while (i < 11 and j_day_no >= _jdmarray(i)) do
                    set j_day_no = j_day_no - _jdmarray(i);
                    set i = i + 1;
                end while;
            
                set jm = i + 1;
                set jd = j_day_no + 1;
                return jd;
            end;;');
        DB::unprepared('
        create function `_gdmarray2`(`m` smallint, `k` smallint) returns smallint(2)
        deterministic
            begin
            
                case m
                    when 0 then return 31;
                    when 1 then return 28+k;
                    when 2 then return 31;
                    when 3 then return 30;
                    when 4 then return 31;
                    when 5 then return 30;
                    when 6 then return 31;
                    when 7 then return 31;
                    when 8 then return 30;
                    when 9 then return 31;
                    when 10 then return 30;
                    when 11 then return 31;
                end case;
               
            
            end;;');
        DB::unprepared("
        create function `gdate`(`jy` smallint, `jm` smallint, `jd` smallint) returns datetime
        deterministic
            begin
            
                declare
                    i, j, e, k, mo,
                    gy, gm, gd,
                    g_day_no, j_day_no, bkab, jmm, mday, g_day_mo, bkab1, j1
                int default 0; /* can be unsigned int? */
                declare resout char(100);
                declare fdate datetime;
            
                
              set bkab = __mymod(jy,33);
            
              if (bkab = 1 or bkab= 5 or bkab = 9 or bkab = 13 or bkab = 17 or bkab = 22 or bkab = 26 or bkab = 30) then
                set j=1;
              end if;
            
              set bkab1 = __mymod(jy+1,33);
            
              if (bkab1 = 1 or bkab1= 5 or bkab1 = 9 or bkab1 = 13 or bkab1 = 17 or bkab1 = 22 or bkab1 = 26 or bkab1 = 30) then
                set j1=1;
              end if;
            
                case jm
                    when 1 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 2 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 3 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 4 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 5 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 6 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 7 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 8 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 9 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 10 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 11 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 12 then if jd > _jdmarray2(jm)+j or jd <= 0 then set e=1; end if;
                end case;
              if jm > 12 or jm <= 0 then set e=1; end if;
              if jy <= 0 then set e=1; end if;
            
              if e>0 then
                return 0;
              end if;
            
              if (jm>=11) or (jm=10 and jd>=11 and j=0) or (jm=10 and jd>11 and j=1) then
                set i=1;
              end if;
              set gy = jy + 621 + i;
            
              if (__mymod(gy,4)=0) then
                set k=1;
              end if;
                
                if (__mymod(gy,100)=0) and (__mymod(gy,400)<>0) then
                    set k=0;
                end if;
            
              set jmm=jm-1;
            
              while (jmm > 0) do
                set mday=mday+_jdmarray2(jmm);
                set jmm=jmm-1;
              end while;
            
              set j_day_no=(jy-1)*365+(__mydiv(jy,4))+mday+jd;
              set g_day_no=j_day_no+226899;
            
            
              set g_day_no=g_day_no-(__mydiv(gy-1,4));
              set g_day_mo=__mymod(g_day_no,365);
            
                if (k=1 and j=1) then
                    if (g_day_mo=0) then
                        return concat_ws('-',gy,'12','30');
                    end if;
                    if (g_day_mo=1) then
                        return concat_ws('-',gy,'12','31');
                    end if;
                end if;
            
                if (g_day_mo=0) then
                    return concat_ws('-',gy,'12','31');
                end if;
                        
            
              set mo=0;
              set gm=gm+1;
              while g_day_mo>_gdmarray2(mo,k) do
                    set g_day_mo=g_day_mo-_gdmarray2(mo,k);
                set mo=mo+1;
                set gm=gm+1;
              end while;
              set gd=g_day_mo;
            
              return concat_ws('-',gy,gm,gd);
            end;;"

        );
        DB::unprepared("
        create function `gdatestr`(`jdat` char(10)) returns datetime
        deterministic
            begin
            
                declare
                    i, j, e, k, mo,
                    gy, gm, gd,
                    g_day_no, j_day_no, bkab, jmm, mday, g_day_mo, jd, jy, jm,bkab1,j1
                int default 0; /* ### can't be unsigned int! ### */
                declare resout char(100);
                declare jdd, jyd, jmd, jt varchar(100);
                declare fdate datetime;
            
                set jdd = substring_index(jdat, '/', -1);
                set jt = substring_index(jdat, '/', 2);
                set jyd = substring_index(jt, '/', 1);
                set jmd = substring_index(jt, '/', -1);
                set jd = cast(jdd as signed);
                set jy = cast(jyd as signed);
                set jm = cast(jmd as signed);
            
            
                 set bkab = __mymod(jy,33);
            
              if (bkab = 1 or bkab= 5 or bkab = 9 or bkab = 13 or bkab = 17 or bkab = 22 or bkab = 26 or bkab = 30) then
                set j=1;
              end if;
            
              set bkab1 = __mymod(jy+1,33);
            
              if (bkab1 = 1 or bkab1= 5 or bkab1 = 9 or bkab1 = 13 or bkab1 = 17 or bkab1 = 22 or bkab1 = 26 or bkab1 = 30) then
                set j1=1;
              end if;
            
                case jm
                    when 1 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 2 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 3 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 4 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 5 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 6 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 7 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 8 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 9 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 10 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 11 then if jd > _jdmarray2(jm) or jd <= 0 then set e=1; end if;
                    when 12 then if jd > _jdmarray2(jm)+j or jd <= 0 then set e=1; end if;
                end case;
              if jm > 12 or jm <= 0 then set e=1; end if;
              if jy <= 0 then set e=1; end if;
            
              if e>0 then
                return 0;
              end if;
            
              if (jm>=11) or (jm=10 and jd>=11 and j=0) or (jm=10 and jd>11 and j=1) then
                set i=1;
              end if;
              set gy = jy + 621 + i;
            
              if (__mymod(gy,4)=0) then
                set k=1;
              end if;
                
                if (__mymod(gy,100)=0) and (__mymod(gy,400)<>0) then
                    set k=0;
                end if;
            
              set jmm=jm-1;
            
              while (jmm > 0) do
                set mday=mday+_jdmarray2(jmm);
                set jmm=jmm-1;
              end while;
            
              set j_day_no=(jy-1)*365+(__mydiv(jy,4))+mday+jd;
              set g_day_no=j_day_no+226899;
            
            
              set g_day_no=g_day_no-(__mydiv(gy-1,4));
              set g_day_mo=__mymod(g_day_no,365);
            
                if (k=1 and j=1) then
                    if (g_day_mo=0) then
                        return concat_ws('-',gy,'12','30');
                    end if;
                    if (g_day_mo=1) then
                        return concat_ws('-',gy,'12','31');
                    end if;
                end if;
            
                if (g_day_mo=0) then
                    return concat_ws('-',gy,'12','31');
                end if;
                        
            
              set mo=0;
              set gm=gm+1;
              while g_day_mo>_gdmarray2(mo,k) do
                    set g_day_mo=g_day_mo-_gdmarray2(mo,k);
                set mo=mo+1;
                set gm=gm+1;
              end while;
              set gd=g_day_mo;
            
              return concat_ws('-',gy,gm,gd);
            end;;");

    }

    public function down()
    {
        DB::unprepared('drop function if exists __mydiv');
        DB::unprepared('drop function if exists _gdmarray');
        DB::unprepared('drop function if exists _jdmarray');
        DB::unprepared('drop function if exists jdate');
        DB::unprepared('drop function if exists jmonth');
        DB::unprepared('drop function if exists jyear');
        DB::unprepared('drop function if exists jday');
        DB::unprepared('drop function if exists _gdmarray2');
        DB::unprepared('drop function if exists gdate');
        DB::unprepared('drop function if exists gdatestr');
    }
}
