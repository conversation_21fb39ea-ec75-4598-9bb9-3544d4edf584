<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class   Contract extends Model
{
    public static $fields = [
        'npa_identification_number',
        'is_above_threshold',
        'sector_id',
        'procurement_entity_id',
        'contract_number',
        'procurement_type_id',
        'exchange_rate',
        'currency_id',
        'project_id',
        'contract_manager_id',
        'specialist_id',
        'is_above_threshold',
        'is_confirmed',
        'is_approved',
        'is_published',
        'has_requested_to_unpublish',
        'published_date',
        'procurement_method_id'
    ];

    protected $guarded = ['id'];

    public function contract_approval()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractApproval');
    }

    public function contract_status()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatus');
    }

    public function npa_other_comments()
    {
        return $this->hasMany('NPA\ACPMS\Models\NpaOtherComments');
    }

    public function contract_specialists()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractSpecialists');
    }
    public function contract_tags()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractTags');
    }
    public function contract_status_logs()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusLog');
    }

    public function contract_payment()
    {
        return $this->hasMany('NPA\ACPMS\Models\Payment');
    }

    public function payment_delay()
    {
        return $this->hasOne('NPA\ACPMS\Models\PaymentDelay');
    }

    public function remarks_and_challenges()
    {
        return $this->hasMany('NPA\ACPMS\Models\ChallengesAndRemarks');
    }

    public function procurement_entity_contact_person()
    {
        return $this->hasMany('NPA\ACPMS\Models\ProcurementEntityContactPerson');
    }

    public function p_e_contact_person_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\PEContactPersonVerification');
    }

    public function domestic_contract_execution_location()
    {
        return $this->hasMany('NPA\ACPMS\Models\DomesticContractExecutionLocation');
    }

    public function foreign_contract_execution_location()
    {
        return $this->hasOne('NPA\ACPMS\Models\ForeignContractExecutionLocation');
    }

    public function domestic_contract_execution_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\DomesticContractExecutionVerification');
    }

    public function contract_details()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractDetails');
    }

    public function contract_progress()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractProgress');
    }

    public function contract_progress_payments()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractProgressPayment');
    }

    public function liquidated_damages_after_delivery_Service()
    {
        return $this->hasMany('NPA\ACPMS\Models\LiquidatedDamagesAfterDeliveryService');
    }

    public function liquidated_damages_after_delivery_service_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\LiquidatedDamagesDeliveryVerification');
    }

    public function contract_manager()
    {
        return $this->belongsTo('NPA\ACPMS\User', 'contract_manager_id');
    }

    public function specialist()
    {
        return $this->belongsTo('NPA\ACPMS\User', 'specialist_id');
    }

    public function contract_planning_f_a_a_p_p_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractPlanningFAAPPVerification');
    }

    public function contract_planning_f_a_a_p_p()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress');
    }

    public function contract_re_planning_Date_f_a_a_p_p_verification()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification');
    }

    public function contract_re_planning_Date_f_a_a_p_p()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRePlanningDateFAAPP');
    }

    public function amendments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Amendment');
    }

    public function company_general_information()
    {
        return $this->hasOne('NPA\ACPMS\Models\CompanyGeneralInformation');
    }

    public function contract_retention()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetention');
    }

    public function subcontract()
    {
        return $this->hasMany('NPA\ACPMS\Models\Subcontract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract_planning_advance()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractPlanningAdvance');
    }

    public function contract_general_information_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractGeneralInformationVerification');
    }

    public function exception()
    {
        return $this->hasOne('NPA\ACPMS\Models\Exception');
    }

    public function donors()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractDonor');
    }

    public function consultancy_contracts_selection_method()
    {
        return $this->hasOne('NPA\ACPMS\Models\ConsultancyContractsSelectionMethod');
    }

    public function progress_analysis_reports()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ProgressAnalysisReport');
    }
}
