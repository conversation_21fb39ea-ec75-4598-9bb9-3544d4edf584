<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\CompanyGeneralInformation;

class CompanyGeneralInformationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $query = $request->all();
            $companyGeneralInformation = CompanyGeneralInformation::create(array_only($query, CompanyGeneralInformation::$fields));

            return response()->json([], 201, [
                'location' => $companyGeneralInformation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\CompanyGeneralInformation $companyGeneralInformation
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {
        try {
            $companyGeneralInformation = CompanyGeneralInformation::
            select('id', 'is_joint_venture', 'contract_id', 'is_approved', 'is_confirmed')
                ->where('contract_id', $contractId)
                ->first();
            if (!$companyGeneralInformation) {
                return response()->json([], 404);
            }
            return response()->json($companyGeneralInformation, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\CompanyGeneralInformation $companyGeneralInformation
     * @return \Illuminate\Http\Response
     */
    public function edit(CompanyGeneralInformation $companyGeneralInformation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $data = $request->all();
        $companyGeneralInformation = CompanyGeneralInformation::where('id', $data['id'])->first();
        if (array_key_exists('is_joint_venture', $data)) {
            if ($data['is_joint_venture'] !== $companyGeneralInformation['is_joint_venture']) {
                $companies = $companyGeneralInformation->companies();
                $companies->delete();
            }

        } else {
            $companyGeneralInformation = CompanyGeneralInformation::where('contract_id', $data['parent_id'])->first();
            unset($data['parent_id']);
        }
        try {
            $companyGeneralInformation->update(array_only($data, CompanyGeneralInformation::$fields));

            return response()->json([], 204, [
                'location' => $companyGeneralInformation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\CompanyGeneralInformation $companyGeneralInformation
     * @return \Illuminate\Http\Response
     */
    public function destroy(CompanyGeneralInformation $companyGeneralInformation)
    {
        //
    }
}
