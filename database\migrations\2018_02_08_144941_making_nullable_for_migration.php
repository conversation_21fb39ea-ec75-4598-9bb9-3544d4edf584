<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MakingNullableForMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE users MODIFY email varchar(191) null');
        DB::statement('ALTER TABLE contracts MODIFY signed_date date null');
        DB::statement('ALTER TABLE contracts MODIFY has_requested_to_unpublish boolean null');
        DB::statement('ALTER TABLE contract_details MODIFY planned_start_date date null');
        DB::statement('ALTER TABLE contract_details MODIFY planned_end_date date null');
        DB::statement('ALTER TABLE contract_details MODIFY agreement_signature_date date null');
        DB::statement('ALTER TABLE contract_details MODIFY planned_value double null');
        DB::statement('ALTER TABLE contract_details MODIFY actual_value double null');
        DB::statement('ALTER TABLE contract_performance_securities MODIFY start_date date null');
        DB::statement('ALTER TABLE contract_performance_securities MODIFY end_date date null');
        DB::statement('ALTER TABLE contract_performance_securities MODIFY amount double not null'); // This is a fix in data type, from varchar(191)
        DB::statement('ALTER TABLE contract_approvals MODIFY award_date date null');
        DB::statement('ALTER TABLE contract_approvals MODIFY award_number varchar(50) null');
        DB::statement('ALTER TABLE procurement_entity_contact_people MODIFY personal_contact_number varchar(191) null');
        DB::statement('ALTER TABLE procurement_entity_contact_people MODIFY email varchar(191) null');
        DB::statement('ALTER TABLE procurement_entity_contact_people MODIFY last_name varchar(191) null');
        DB::statement('ALTER TABLE procurement_entity_contact_people MODIFY job varchar(191) null');
        DB::statement('ALTER TABLE challenges_and_remarks MODIFY creator_id integer unsigned null');
        DB::statement('ALTER TABLE challenges_and_remarks MODIFY end_date date null');
        DB::statement('ALTER TABLE contract_progresses MODIFY actual_start_date date null');
        DB::statement('ALTER TABLE companies MODIFY address text null');
        DB::statement('ALTER TABLE companies MODIFY remarks text not null');
        DB::statement('ALTER TABLE companies MODIFY percentage_contract_share float null');
        DB::statement('ALTER TABLE companies MODIFY joint_venture_company_role_id integer unsigned null');
        DB::statement('ALTER TABLE companies MODIFY company_id integer unsigned null');
        DB::statement('ALTER TABLE companies MODIFY licence_end_date date null');
        DB::statement('ALTER TABLE liquidated_damages_periods MODIFY expiration_date date null');
        DB::statement('ALTER TABLE after_delivery_service_periods MODIFY end_date date null');
        DB::statement('ALTER TABLE contract_planning_advances MODIFY amount integer null');


        Schema::dropIfExists('award_details');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::create('award_details', function (Blueprint $table) {
            $table->increments('id');
            $table->boolean('is_award_authority_eligible');
            $table->float('contract_total_value');
            $table->timestamps();
        });
    }
}
