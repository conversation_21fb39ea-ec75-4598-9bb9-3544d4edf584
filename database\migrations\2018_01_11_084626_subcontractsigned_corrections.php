<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SubcontractsignedCorrections extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->dropForeign('subcontract_signeds_subcontract_id_foreign');
            $table->dropColumn('subcontract_id');
            $table->dropColumn('company_name');
            $table->dropColumn('company_licence_number');
            $table->dropColumn('company_TIN_number');
            $table->integer('company_id');
        });
    }

    public function down()
    {
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->integer('subcontract_id')->unsigned();
            $table->foreign('subcontract_id')->references('id')->on('subcontracts');
            $table->string('company_name');
            $table->string('company_licence_number');
            $table->string('company_TIN_number');
            $table->dropColumn('company_id');
        });
    }
}
