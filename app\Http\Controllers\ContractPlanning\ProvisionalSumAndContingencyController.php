<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ProvisionalSumAndContingency;
use Illuminate\Http\Request;

class ProvisionalSumAndContingencyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ProvisionalSumAndContingency  $provisionalSumAndContingency
     * @return \Illuminate\Http\Response
     */
    public function show(ProvisionalSumAndContingency $provisionalSumAndContingency)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ProvisionalSumAndContingency  $provisionalSumAndContingency
     * @return \Illuminate\Http\Response
     */
    public function edit(ProvisionalSumAndContingency $provisionalSumAndContingency)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \NPA\ACPMS\Models\ProvisionalSumAndContingency  $provisionalSumAndContingency
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProvisionalSumAndContingency $provisionalSumAndContingency)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ProvisionalSumAndContingency  $provisionalSumAndContingency
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProvisionalSumAndContingency $provisionalSumAndContingency)
    {
        //
    }
}
