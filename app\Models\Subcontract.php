<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class Subcontract extends Model
{

    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'is_subcontract_applicable'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function subcontract_applicable()
    {
        return $this->hasOne('NPA\ACPMS\Models\SubcontractApplicable');
    }

    public function subcontract_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\SubcontractVerification');
    }
}

