<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateOcdsSyncContractDataOnPublishedLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ocds_sync_contract_data_on_published_logs', function (Blueprint $table) {
            $table->string('npa_identification_number')->after('contract_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ocds_sync_contract_data_on_published_logs', function (Blueprint $table) {
            $table->dropColumn('npa_identification_number');
        });
    }
}
