<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsAdvancePaymentApplicableColumnToContractDetails extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_details', function (Blueprint $table) {
            $table->integer('is_advance_payment_applicable')->nullable()->after('is_delay_penalty_applicable');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('contract_details', 'is_advance_payment_applicable')) {
            Schema::table('contract_details', function (Blueprint $table) {
                $table->dropColumn('is_advance_payment_applicable');
            });
        }
    }
}
