<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingCreateAmendmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('amendments', function (Blueprint $table) {
            $table->dropForeign('amendments_re_plan_id_foreign');
            $table->foreign('re_plan_id')
                ->references('id')
                ->on('contract_re_planning_date_f_a_a_p_ps')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('amendments', function (Blueprint $table) {
            $table->dropForeign('amendments_re_plan_id_foreign');
            $table->foreign('re_plan_id')
                ->references('id')
                ->on('contract_re_planning_date_f_a_a_p_ps');
        });
    }
}
