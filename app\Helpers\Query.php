<?php

namespace NPA\ACPMS\Helpers;


class Query
{
    static function buildQuery($builder, $query, $specialSearch = null)
    {
        try {
            $searchQuery = array_has($query, 'search') ? $query['search'] : [];
            $sortQuery = array_has($query, 'sort') ? $query['sort'] : [];
            if (count($searchQuery) > 0) {
                foreach ($searchQuery as $k => $v) {
                    $match = array_has($v, 'match') ? $v['match'] : 'full';
                    if ($match === 'full') {
                        $builder->where($k, $v['value']);
                    } else if ($match === 'partial') {
                        $builder->where($k, 'like', '%' . $v['value'] . '%');
                    } else if ($match === 'in') {
                        $builder->whereIn($k, $v['value']);
                    } else {
                        throw new \Exception('Invalid value passed for: search[' . $k . '][match] = ' . $v['value']);
                    }
                }
            }

            if ($specialSearch) {
                $builder->where($specialSearch);
            }

            if (count($sortQuery) > 0) {
                foreach ($sortQuery as $k => $v) {
                    if ($v !== 'asc' && $v !== 'desc') {
                        throw new \Exception('Invalid value passed for: sort[' . $k . '] = ' . $v);
                    }
                    $builder->orderBy($k, $v);
                }
            }

            $limit = array_has($query, 'limit') ? $query['limit'] : false;
            if ($limit) {
                $builder->limit($limit);

                $offset = array_has($query, 'offset') ? $query['offset'] : false;
                if ($offset) {
                    $builder->offset($offset);
                }

            }

        } catch (\Throwable $t) {
            throw $t;
        }

        return $builder;

    }

    public static function buildSlug($nameEn)
    {
        $replaceableCharacters = [
            ' ', '_', ',', '/', '\\'
        ];
        $slugBuilt = '';

        $nameEn = trim(strtolower($nameEn));
        for ($i = 0; $i < strlen($nameEn); $i++) {
            if (ctype_alpha($nameEn[$i])) {
                $slugBuilt .= $nameEn[$i];
                continue;
            }
            $matched = array_where($replaceableCharacters, function ($v) use ($nameEn, $i) {
                return $v === $nameEn[$i];
            });
            if (count($matched) > 0) {
                $slugBuilt .= '-';


            }
        }
        return $slugBuilt;

    }

    public static function buildQueryString($searchParameters)
    {

        $queryString = '';
        foreach ($searchParameters as $k => $v) {
            if ($v !== 'null') {
                $queryString .= $k . '=' . urlencode($v) . '&';
            }
        }

        if (strlen($queryString) > 0) {
            $queryString = '?' . substr($queryString, 0, strlen($queryString) - 1);
        }
        return $queryString;
    }

}
