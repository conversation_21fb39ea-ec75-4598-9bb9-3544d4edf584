<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DropOperationCompaniesUserAfterInsertAutoAssignmentTrigger extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec('drop trigger if exists operation_companies_user_after_insert_auto_assignment');
        DB::connection()->getPdo()->exec('drop trigger if exists operation_companies_user_after_update_auto_assignment');
        DB::connection()->getPdo()->exec('drop trigger if exists operation_companies_user_after_delete_auto_assignment');
        Schema::dropIfExists('user_companies');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
