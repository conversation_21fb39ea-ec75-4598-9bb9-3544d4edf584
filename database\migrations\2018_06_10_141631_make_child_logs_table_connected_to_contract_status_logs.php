<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakeChildLogsTableConnectedToContractStatusLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::disableForeignKeyConstraints();
        Schema::table('contract_status_cancelled_logs', function($table) {
            $table->unsignedInteger('contract_status_log_id')
                ->after('contract_status_cancelled_id')
                ->unique()
                ->nullable();
            $table->foreign('contract_status_log_id')->references('id')->on('contract_status_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_status_not_signed_logs', function($table) {
            $table->unsignedInteger('contract_status_log_id')
                ->after('contract_status_id')
                ->unique()
                ->nullable();
            $table->foreign('contract_status_log_id')->references('id')->on('contract_status_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_status_suspended_logs', function($table) {
            $table->unsignedInteger('contract_status_log_id')
                ->after('contract_status_suspended_id')
                ->unique()
                ->nullable();
            $table->foreign('contract_status_log_id')->references('id')->on('contract_status_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contractor_performance_assessment_logs', function($table) {
            $table->unsignedInteger('contract_status_log_id')
                ->after('contractor_performance_assessment_id')
                ->nullable();
            $table->unique('contract_status_log_id', 'contract_status_log_id');
            $table->foreign('contract_status_log_id', 'contract_status_log_id')->references('id')->on('contract_status_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_retention_logs', function($table) {
            $table->unsignedInteger('contract_status_log_id')
                ->after('contract_retention_id')
                ->unique()
                ->nullable();
            $table->foreign('contract_status_log_id')->references('id')->on('contract_status_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_retention_applicable_logs', function($table) {
            $table->unsignedInteger('contract_retention_log_id')
                ->after('contract_retention_applicable_id')
                ->nullable();
            $table->unique('contract_retention_log_id', 'contract_retention_log_id');
            $table->foreign('contract_retention_log_id', 'contract_retention_log_id')
                ->references('id')->on('contract_retention_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_retention_returned_logs', function($table) {
            $table->unsignedInteger('contract_retention_log_id')
                ->after('contract_retention_returned_id')
                ->nullable();
            $table->unique('contract_retention_log_id', 'contract_retention_log_id');
            $table->foreign('contract_retention_log_id', 'retention_log_id')->references('id')
                ->on('contract_retention_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::table('contract_retention_not_returned_logs', function($table) {
            $table->unsignedInteger('contract_retention_log_id')
                ->after('contract_retention_not_returned_id')
                ->nullable();
            $table->unique('contract_retention_log_id', 'contract_retention_log_id');
            $table->foreign('contract_retention_log_id', 'c_r_log_id')->references('id')->on('contract_retention_logs')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
        Schema::enableForeignKeyConstraints();

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        Schema::table('contract_status_cancelled_logs', function (Blueprint $table) {
            $table->dropForeign('contract_status_cancelled_logs_contract_status_log_id_foreign');
            $table->dropColumn('contract_status_log_id');
        });
        Schema::table('contract_status_not_signed_logs', function (Blueprint $table) {
            $table->dropForeign('contract_status_not_signed_logs_contract_status_log_id_foreign');
            $table->dropColumn('contract_status_log_id');
        });
        Schema::table('contract_status_suspended_logs', function (Blueprint $table) {
            $table->dropForeign('contract_status_suspended_logs_contract_status_log_id_foreign');
            $table->dropColumn('contract_status_log_id');
        });
        Schema::table('contractor_performance_assessment_logs', function (Blueprint $table) {
            $table->dropForeign('contract_status_log_id');
            $table->dropColumn('contract_status_log_id');
        });
        Schema::table('contract_retention_logs', function (Blueprint $table) {
            $table->dropForeign('contract_retention_logs_contract_status_log_id_foreign');
            $table->dropColumn('contract_status_log_id');
        });
        Schema::table('contract_retention_applicable_logs', function (Blueprint $table) {
            $table->dropForeign('contract_retention_log_id');
            $table->dropColumn('contract_retention_log_id');
        });
        Schema::table('contract_retention_returned_logs', function (Blueprint $table) {
            $table->dropForeign('retention_log_id');
            $table->dropColumn('contract_retention_log_id');
        });
        Schema::table('contract_retention_not_returned_logs', function (Blueprint $table) {
            $table->dropForeign('c_r_log_id');
            $table->dropColumn('contract_retention_log_id');
        });

    }
}
