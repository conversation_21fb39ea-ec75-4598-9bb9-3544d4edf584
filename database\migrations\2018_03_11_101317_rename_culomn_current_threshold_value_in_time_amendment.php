<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameCulomnCurrentThresholdValueInTimeAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->renameColumn('current_threshold_value', 'current_time_threshold_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->renameColumn('current_time_threshold_value', 'current_threshold_value');
        });
    }
}
