<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractProgressDefectLiability;
use Illuminate\Http\Request;

class ContractProgressDefectLiabilityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
//        var_dump('jani jani ', $request->contract_id);
        try {
            $contractId = $request->query('contract_id');
            $defectLiability = ContractProgressDefectLiability::where('contract_id', $contractId)->get();
            if (!$defectLiability) {
                return response()->json([], 404);
            }
            return response()->json($defectLiability);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $files['compensation_report'] = $data['compensation_report'];
            $contractProgressDefectLiability = ContractProgressDefectLiability::create(array_only($data, ContractProgressDefectLiability::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractProgressDefectLiability, $fieldName, $fileContent);
                }
            }

            return response()->json([], 201, [
                'location' => $contractProgressDefectLiability['id'],
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressDefectLiability $contractProgressDefectLiability
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $contractProgressDefectLiability = ContractProgressDefectLiability::where('id', $id)->first();
            if (!$contractProgressDefectLiability) {
                return response()->json([], 404);
            }
            $attachments = $contractProgressDefectLiability->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            $data = [];
//            dd($attachments);
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index && $v['table_name'] == 'contract_progress_defect_liabilities') {
                        array_push($att, $v);
                        $data[$attachments[$key]['field_name']] = $att;
                    }
                }
            }
            return response()->json($data);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressDefectLiability $contractProgressDefectLiability
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractProgressDefectLiability $contractProgressDefectLiability)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractProgressDefectLiability $contractProgressDefectLiability
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $data = $request->all();

            if (!array_key_exists('parent_id', $data)) {
                $files['compensation_report'] = $data['compensation_report'];
                $contractProgressDefectLiability = ContractProgressDefectLiability::where('id', $id)->first();
                ContractProgressDefectLiability::where('id', $id)->update(array_only($data, ContractProgressDefectLiability::$fields));
                foreach ($files as $fieldName => $fileContent) {
                    if ($fileContent !== null) {
                        AttachmentController::saveFile($request, $contractProgressDefectLiability, $fieldName, $fileContent);
                    }
                }
            } elseif (isset($data['is_published']) && $data['is_published']) {
                $currentDate = Carbon::now()->toDateTimeString();
                ContractProgressDefectLiability::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
            } else {
                ContractProgressDefectLiability::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                        ]
                    );
            }
            return response()->json([], 204);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressDefectLiability $contractProgressDefectLiability
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $isDeleted = ContractProgressDefectLiability::where('id', $id)->delete();
        if (!$isDeleted) {
            return response()->json([], 404);
        }
        return response()->json([], 204);
    }
}
