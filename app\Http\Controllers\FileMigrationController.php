<?php

namespace NPA\ACPMS\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use NPA\ACPMS\Helpers\Error;
use Throwable;

class FileMigrationController extends Controller
{

    public function migrate()
    {
        try {

            $files = Storage::files('data');
            Storage::copy('data/hello.txt', 'newData/new.txt');
            dd($files);


            return response()->json();
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

}
