<?php

namespace NPA\ACPMS\Http\Controllers\API\Domestic\Daemons;

use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\API\Domestic\Daemons\CommonValue;
use NPA\ACPMS\Models\API\Domestic\Daemons\IntegrationToSyncProject;
use NPA\ACPMS\Models\Contract;

class ProjectsController extends Controller
{
    public function sync()
    {
        $lastSyncedId = CommonValue::where('common_key', 'last_synced_id')->first()->common_value;
        $request = Http::post(
            config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [], [
                'last_synced_id' => $lastSyncedId
            ]
        );
        if ($request->status_code > 300) {

            //TODO: Handle
            throw new \Exception($request->body);
        }

        $aboveThresholdValues = [];
        $aboveThresholdQuerySting = '';

        foreach (json_decode($request->body, true) as $r) {

            if ($r['is_above_threshold'] == 'yes') {

                // Collect all the records to be sent to NPCS to check the NPC decision status
                $aboveThresholdValues[] = [
                    'project_id' => $r['project_id'],
                    'npa_identification_number' => $r['npa_identification_number']
                ];
                $aboveThresholdQuerySting .= 'npa_identification_numbers[]=' . urlencode($r['npa_identification_number']) . '&';

            } else {

                // Check if contract is signed
                if ($this->isContractSigned($r)) {
                    Contract::insert([
                        'project_id' => $r['project_id'],
                        'npa_identification_number' => $r['npa_identification_number'],
                        'is_above_threshold' => false
                    ]);
                } else {
                    IntegrationToSyncProject::crreate([
                        'project_id' => $r['project_id'],
                        'is_above_threshold' => $r['is_above_threshold'] === 'Yes'
                    ]);
                }

            }
        }

        // Send the request to NPCS
        if (strlen($aboveThresholdQuerySting) <= 0) {

            // TODO: SUCCESS
            return;
        }

        $aboveThresholdQuerySting = '?' . substr(
                $aboveThresholdQuerySting,
                0,
                strlen($aboveThresholdQuerySting) - 1
            );

        $request = Http::get(
            config('custom.CUSTOM_API_INTERNAL_NPCS_BASE_URL') .
            'api/domestic/cpms/v1/commission-decisions' .
            $aboveThresholdQuerySting, []
        );
        if ($request->status_code > 300) {
            throw new \Exception($request->body);
        }

        // TODO:
        //  1. Handle the the part if it is above threshold
        //  2. Handle to sync part

    }

    private function isContractSigned($record)
    {
        return !!$record['contract_signed_date'];
    }

}
