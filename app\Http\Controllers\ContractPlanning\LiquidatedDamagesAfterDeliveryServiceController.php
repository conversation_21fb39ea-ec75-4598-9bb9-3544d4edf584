<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use Illuminate\Http\Request;
use NPA\ACPMS\Models\AfterDeliveryServicePeriod;
use NPA\ACPMS\Models\LiquidatedDamagesAfterDeliveryService;
use NPA\ACPMS\Models\LiquidatedDamagesPeriod;

class LiquidatedDamagesAfterDeliveryServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $data = $request->all();
        try {
            DB::beginTransaction();
            $liquidatedDamagesAfterDeliveryService = LiquidatedDamagesAfterDeliveryService::create(array_only($data, LiquidatedDamagesAfterDeliveryService::$fields));

            if ($data['is_liquidated_damages_period_applicable']) {
                $data['liquidated_da_after_de_id'] = $liquidatedDamagesAfterDeliveryService['id'];
                $data['duration_in_month'] = $data['liquidated_damages_period']['duration_in_month'];
                $data['expiration_date'] = $data['liquidated_damages_period']['expiration_date'];
                $data['remarks'] = $data['liquidated_damages_period']['remarks'];
                LiquidatedDamagesPeriod::create(array_only($data, LiquidatedDamagesPeriod::$fields));
            }

            if ($data['is_after_delivery_service_period_applicable']) {
                $data['liquidated_da_after_de_id'] = $liquidatedDamagesAfterDeliveryService['id'];
                $data['duration_in_month'] = $data['after_delivery_service_period']['duration_in_month'];
                $data['end_date'] = $data['after_delivery_service_period']['end_date'];
                $data['remarks'] = $data['after_delivery_service_period']['remarks'];
                AfterDeliveryServicePeriod::create(array_only($data, AfterDeliveryServicePeriod::$fields));
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $liquidatedDamagesAfterDeliveryService['id'],
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($contractId)
    {
        try {
            $liquidatedDamagesAfterDeliveryService = LiquidatedDamagesAfterDeliveryService::where('contract_id', $contractId)->first();
            if (!isset($liquidatedDamagesAfterDeliveryService)) {
                return response()->json([], 404);
            }
            $data = $liquidatedDamagesAfterDeliveryService;
            $data['liquidated_damages_period'] = [];
            $data['after_delivery_service_period'] = [];
            if ($liquidatedDamagesAfterDeliveryService['is_liquidated_damages_period_applicable']) {
                $liquidatedDamagesPeriod = LiquidatedDamagesPeriod::where('liquidated_da_after_de_id', $liquidatedDamagesAfterDeliveryService['id'])->first();
                $data['liquidated_damages_period'] = $liquidatedDamagesPeriod;
            }

            if ($liquidatedDamagesAfterDeliveryService['is_after_delivery_service_period_applicable']) {
                $afterDeliveryServicePeriod = AfterDeliveryServicePeriod::where('liquidated_da_after_de_id', $liquidatedDamagesAfterDeliveryService['id'])->first();
                $data['after_delivery_service_period'] = $afterDeliveryServicePeriod;
            }
            return response()->json($data);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \NPA\ACPMS\LiquidatedDamagesAfterDeliveryService $liquidatedDamagesAfterDeliveryService
     * @return \Illuminate\Http\Response
     */
    public function edit(LiquidatedDamagesAfterDeliveryService $liquidatedDamagesAfterDeliveryService)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param  \Illuminate\Http\Request $request
     * @param  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            $liquidatedDamagesAfterDeliveryService = LiquidatedDamagesAfterDeliveryService::where('id', $id)->first();
            LiquidatedDamagesAfterDeliveryService::Where('id', $id)->update(array_only($data, LiquidatedDamagesAfterDeliveryService::$fields));
            if ($data['is_liquidated_damages_period_applicable']) {
                $tempData = $data['liquidated_damages_period'];
                $tempData['liquidated_da_after_de_id'] = $id;
                LiquidatedDamagesPeriod
                    ::updateOrCreate([
                        'liquidated_da_after_de_id' => $liquidatedDamagesAfterDeliveryService['id']
                    ], array_only($tempData, LiquidatedDamagesPeriod::$fields));
            }

            if ($data['is_after_delivery_service_period_applicable']) {
                $tempData = $data['after_delivery_service_period'];
                $tempData['liquidated_da_after_de_id'] = $id;
                AfterDeliveryServicePeriod
                    ::updateOrCreate([
                        'liquidated_da_after_de_id' => $liquidatedDamagesAfterDeliveryService['id']
                    ], array_only($tempData, AfterDeliveryServicePeriod::$fields));
            }
            DB::commit();
            return response()->json([], 204);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     * @param LiquidatedDamagesAfterDeliveryService $liquidatedDamagesAfterDeliveryService
     */
    public function destroy(LiquidatedDamagesAfterDeliveryService $liquidatedDamagesAfterDeliveryService)
    {
        //
    }
}
