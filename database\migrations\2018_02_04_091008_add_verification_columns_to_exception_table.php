<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddVerificationColumnsToExceptionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('exceptions', function (Blueprint $table) {
            $table->boolean('is_confirmed')
                ->default(false)
                ->nullable()
                ->after('is_article_54_applicable');
            $table->boolean('is_approved')
                ->default(false)
                ->nullable()
                ->after('is_confirmed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('exceptions', function (Blueprint $table) {
            $table->dropColumn('is_confirmed');
            $table->dropColumn('is_approved');
        });
    }
}
