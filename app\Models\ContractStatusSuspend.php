<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusSuspend extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'suspend_reason',
        'contract_status_id',
    ];

    //
    public function contract_status()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function contract_status_suspended_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusSuspendedLog');
    }
}
