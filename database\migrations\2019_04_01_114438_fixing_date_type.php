<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingDateType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_planning_advances', function (Blueprint $table) {
            $table->date('date')->change()->nullable()->default(null);
        });
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->date('approval_date')->change()->nullable()->default(null);
            $table->date('end_date')->change()->nullable()->default(null);
            $table->date('company_licence_end_date')->change()->nullable()->default(null);
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->date('amended_performance_security_end_date')->change()->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_planning_advances', function (Blueprint $table) {
            $table->string('date')->change();
        });
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->string('approval_date')->change();
            $table->string('end_date')->change();
            $table->string('company_licence_end_date')->change();
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->string('amended_performance_security_end_date')->change();
        });
    }
}
