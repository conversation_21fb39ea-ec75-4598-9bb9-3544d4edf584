<?php

namespace NPA\ACPMS\Http\Controllers;

use NPA\ACPMS\ConsultancyContractsSelectionMethod;
use Illuminate\Http\Request;

class ConsultancyContractsSelectionMethodController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\ConsultancyContractsSelectionMethod  $consultancyContractsSelectionMethod
     * @return \Illuminate\Http\Response
     */
    public function show(ConsultancyContractsSelectionMethod $consultancyContractsSelectionMethod)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\ConsultancyContractsSelectionMethod  $consultancyContractsSelectionMethod
     * @return \Illuminate\Http\Response
     */
    public function edit(ConsultancyContractsSelectionMethod $consultancyContractsSelectionMethod)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \NPA\ACPMS\ConsultancyContractsSelectionMethod  $consultancyContractsSelectionMethod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ConsultancyContractsSelectionMethod $consultancyContractsSelectionMethod)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\ConsultancyContractsSelectionMethod  $consultancyContractsSelectionMethod
     * @return \Illuminate\Http\Response
     */
    public function destroy(ConsultancyContractsSelectionMethod $consultancyContractsSelectionMethod)
    {
        //
    }
}
