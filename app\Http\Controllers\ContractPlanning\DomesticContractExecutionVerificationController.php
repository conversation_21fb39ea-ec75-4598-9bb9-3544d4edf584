<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\DomesticContractExecutionVerification;

class DomesticContractExecutionVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $data = DomesticContractExecutionVerification::create(array_only($data, DomesticContractExecutionVerification::$fields));
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {
        $contractId = DomesticContractExecutionVerification::where('contract_id', $contractId)->first();
        return response()->json($contractId, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\DomesticContractExecutionVerification $domesticContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(DomesticContractExecutionVerification $domesticContractExecutionVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\DomesticContractExecutionVerification $domesticContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $domesticContractExecutionVerification)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $domesticVerification =
                DomesticContractExecutionVerification::where('contract_id', $domesticContractExecutionVerification)->first();
            $isUpdated = $domesticVerification->update(array_only($data, DomesticContractExecutionVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\DomesticContractExecutionVerification $domesticContractExecutionVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(DomesticContractExecutionVerification $domesticContractExecutionVerification)
    {
        //
    }
}
