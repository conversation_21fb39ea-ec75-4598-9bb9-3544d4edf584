<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusNotSignedLog extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'not_signed_reason',
        'contract_status_id',
        'contract_status_not_signed_id',
        'contract_status_log_id'
    ];

    public function contract_status_not_signed()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusNotSigned');
    }
}
