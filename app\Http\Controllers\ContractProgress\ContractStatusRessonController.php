<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractStatusReason;

class ContractStatusRessonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        dd('inside Reason controller');
        $builder = ContractStatusReason::whereRaw('1=1');
        try {
            $updatedBuilder = Query::buildQuery($builder, $request->query());
            return response()->json($updatedBuilder->get());

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $cod = ContractStatusReason::where('id', $id)->get();
        return response()->json(['Result' => $cod]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {


        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
