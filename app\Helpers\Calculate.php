<?php
/**
 * Created by PhpStorm.
 * User: Wahrez
 * Date: 3/18/2018
 * Time: 9:32 AM
 */

namespace NPA\ACPMS\Helpers;


use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\ProvisionalSumAndContingency;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class Calculate
{
    public static function totalAmount($contractId, $exchange = false)
    {
        $exchange_rate = Contract::select('exchange_rate')->where('id', $contractId)->first();
        $exchange_rate = is_null($exchange_rate->exchange_rate) ? 1 : $exchange_rate->exchange_rate;

        $contractDetail = ContractDetails::select(
            'id',
            'actual_value',
            'is_provisional_sum_and_contingency_applicable',
            'is_provisional_sum_and_contingency_included'
        )->where('contract_id', $contractId)->first();
        if (isset($contractDetail)) {
            $contract_details_id = $contractDetail['id'];
            $actual_value = isset($contractDetail['actual_value']) ? $contractDetail['actual_value'] : 0;
            if (!is_null($contractDetail['is_provisional_sum_and_contingency_applicable']) && $contractDetail['is_provisional_sum_and_contingency_applicable']
                && is_null($contractDetail['is_provisional_sum_and_contingency_included']) || !$contractDetail['is_provisional_sum_and_contingency_included']) {
                $provisional_sum_and_contingency = ProvisionalSumAndContingency::select('provisional_sum_and_contingency')
                    ->where('contract_detail_id', $contract_details_id)->first();
                $provisional_sum_and_contingency =
                    isset($provisional_sum_and_contingency['provisional_sum_and_contingency']) ? $provisional_sum_and_contingency['provisional_sum_and_contingency'] : 0;
            } else {
                $provisional_sum_and_contingency = 0;
            }
            $amendment_type_id = null;
            $amendment_types = null;
            $amendment_amount = 0;

            $amendment_types = DropDowns::getBySlugs(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType',
                ['cost-amendment', 'time-and-cost-amendment']
            );
            $amendment_type_cost_id = null;
            $amendment_type_cost_slug = 'cost-amendment';
            $amendment_type_cost_and_time_id = null;
            $amendment_type_cost_and_time_slug = 'time-and-cost-amendment';
            foreach ($amendment_types as $at) {
                if ($at['slug'] === $amendment_type_cost_slug) {
                    $amendment_type_cost_id = $at['id'];
                } else if ($at['slug'] === $amendment_type_cost_and_time_slug) {
                    $amendment_type_cost_and_time_id = $at['id'];
                }
            }
            $amendments = Amendment
                ::select('type_id', 'id')
                ->where('contract_id', $contractId)
                ->where('is_approved', 1)
                ->whereIn('type_id', [
                    $amendment_type_cost_id,
                    $amendment_type_cost_and_time_id
                ])
                ->get()
                ->toArray();
            if (count($amendments) > 0) {
                foreach ($amendments as $amendment) {
                    if ($amendment['type_id'] == $amendment_type_cost_id) {
                        $cost_amendment = CostAmendment::select('amendment_amount')->where('amendment_id', $amendment['id'])->first();
                        $amendment_amount += $cost_amendment->amendment_amount;
                    }
                    if ($amendment['type_id'] == $amendment_type_cost_and_time_id) {
                        $time_and_cost_amendment = TimeAndCostAmendment::select('amendment_amount')->where('amendment_id', $amendment['id'])->first();
                        $amendment_amount += $time_and_cost_amendment->amendment_amount;
                    }
                }
            }
            if ($exchange) {
                $grand_total_amount = ($actual_value + $provisional_sum_and_contingency + $amendment_amount) * $exchange_rate;
            } else {
                $grand_total_amount = ($actual_value + $provisional_sum_and_contingency + $amendment_amount);
            }
            return $grand_total_amount;
        }
        return null;
    }

    public static function actualPaymentProgressPercentage($contractId)
    {
//        $contractId = 2;
        $actual_payment_progress = DB::table('contracts as c')
            ->leftJoin('contract_progress_payments as cpp', 'c.id', '=', 'cpp.contract_id')
            ->where('c.id', $contractId)
            ->where('cpp.is_confirmed', 1)
            ->sum('cpp.amount');
        $actual_value = DB::table('contracts as c')
            ->leftJoin('contract_details as cd', 'c.id', '=', 'cd.contract_id')
            ->leftJoin('contract_details_verifications as cdv', 'cd.id', '=', 'cdv.contract_detail_id')
            ->select('cd.actual_value')
            ->where('c.id', $contractId)
//            ->where('cdv.is_confirmed', 1)
            ->first()->actual_value;
        $actual_payment_progress_percentage = $actual_value ? round((($actual_payment_progress * 100) / $actual_value), 2) : 0;

        return $actual_payment_progress_percentage;
    }

    public static function actualPhysicalProgressPercentage($contractId)
    {
        // Get physical progress from regular payments
        $actual_physical_progress = DB::table('contract_progress_payments')
            ->where('contract_id', $contractId)
            ->where('is_confirmed', 1)
            ->sum('physical_progress_percentage');

        // Get physical progress from payments after due date
        $after_due_date_physical_progress = DB::table('contract_progress_payment_after_due_dates')
            ->where('contract_id', $contractId)
            ->where('is_confirmed', 1)
            ->sum('physical_progress_percentage');

        // Return the sum of both
        return $actual_physical_progress + $after_due_date_physical_progress;
    }
}