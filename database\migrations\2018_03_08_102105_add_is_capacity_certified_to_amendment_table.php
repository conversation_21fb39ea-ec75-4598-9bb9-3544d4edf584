<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsCapacityCertifiedToAmendmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('amendments', function (Blueprint $table) {
            $table->boolean('is_pe_capacity_certified')->after('contract_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('amendments', 'is_pe_capacity_certified')) {
            Schema::table('amendments', function (Blueprint $table) {
                $table->dropColumn('is_pe_capacity_certified');
            });
        }
    }
}
