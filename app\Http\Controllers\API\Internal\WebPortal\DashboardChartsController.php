<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Http\Controllers\Controller;

class DashboardChartsController extends Controller
{
    public function index(Request $request)
    {
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $queryString = $this->prepareQueryStrings($request->query(), $contractStatuses);
        $data['widgets'] = $this->widgets($queryString, $contractStatuses);
        $data['contracts_value_based_on_status'] = $this->contractValueBasedOnStatus($queryString);
        $data['contracts_count_based_on_status'] = $this->contractsCountBasedOnStatus($queryString);
        $data['contracts_amendment_level'] = $this->contractsAmendmentLevel($queryString, $amendmentTypes);
        $data['contracts_based_on_province'] = $this->contractsBasedOnProvince($queryString);
        $data['contracts_value_based_on_donor'] = $this->contractsValueBasedOnDonor($queryString);
        $data['contract_progress_volume'] = $this->contractProgressVolume($queryString);
        $data['contracts_count_and_value_based_on_procurement_type'] = $this->contractsCountAndValueBasedOnProcurementTypeChart($queryString);
        $data['type_of_amendments_in_contracts_chart'] = $this->typeOfAmendmentsInContractsChart($queryString);
        $data['contract_challenges_contract_amount'] = $this->contractChallengesContractAmount($queryString);
        $data['number_of_contracts_based_on_company'] = $this->numberOfContractsBasedOnCompany($queryString);
        $now = Carbon::now();
        $jalaliNow = JDateTime::toJalali($now->year, $now->month, $now->day);
        $data['last_year'] = $jalaliNow[0];
        if ($jalaliNow[1] > 9)
            $data['last_year'] = $jalaliNow[0] + 1;
        return response()->json($data, 200);
    }


    private function widgets($queryString, $contractStatuses)
    {
        $contractStatusNotSigned = DropDowns::getElementTypeBySlug($contractStatuses, 'not-signed');
        $contractStatusPending = DropDowns::getElementTypeBySlug($contractStatuses, 'pending');
        $contractStatusCancelled = DropDowns::getElementTypeBySlug($contractStatuses, 'cancelled');

        $data['main_widgets'] = DB::select('
        select
                (
                    select count(c.id) as contracts_count
                    from contracts as c

                    ' . $queryString['joins'] . '
                    where 1=1 ' . $queryString['query_string'] . '
                ) as contracts_count
                ,(
                    select count(c.id) as published_contracts
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where c.is_published = true ' . $queryString['query_string'] . '
                ) as published_contracts
                ,(
                    select count(c.id) as not_published_contracts
                        from contracts as c
                    ' . $queryString['joins'] . '
                    where c.is_published != true ' . $queryString['query_string'] . '
                ) as not_published_contracts')[0];
        $data['contract_plan_amount_and_percentage'] = DB::select('
                select
                    sum(
                            if(crpp.amount,ifnull(crpp.amount, 0), ifnull(cpfapp.amount, 0)) *  
                            if(c.exchange_rate, c.exchange_rate, 1)) as contracts_plan_amount
                from contracts as c
                left join (
                    select contract_id, sum(amount) as amount
                    from contract_planning_finance_affairs_and_physical_progresses
                    group by contract_id
                ) as  cpfapp
                    on c.id = cpfapp.contract_id
                left join (
                    select
                        inner_crp.contract_id, plan_payments.amount
                    from contract_re_planning_date_f_a_a_p_ps as inner_crp
                    join ( 
                        select 
                            contract_id, 
                            max(id) as max_id 
                        from contract_re_planning_date_f_a_a_p_ps
                        group by contract_id
                    ) as max_value
                        on inner_crp.id = max_value.max_id
                    join ( 
                        select 
                            contract_re_planning_date_f_a_a_p_p_id as id, 
                            sum(amount) as amount
                        from contract_re_planning_finance_affairs_and_physical_progresses
                        group by contract_re_planning_date_f_a_a_p_p_id
                    ) as plan_payments
                        on plan_payments.id = max_value.max_id
                ) as crpp
                  on crpp.contract_id = c.id
                join (
                  select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                  on cs.contract_id = c.id
                ' . $queryString['joins'] . '
                where c.is_published = true and
                    cs.status_id not in (' . $contractStatusNotSigned['id'] . ',' . $contractStatusPending['id'] . ',' . $contractStatusCancelled['id'] . ')
                    ' . $queryString['query_string'] . '

        ')[0];
        $data['contract_actual_amount_and_percentage'] = DB::select('
             select
                sum(ifnull(cpp.amount,0) * if(c.exchange_rate, c.exchange_rate, 1)) as contracts_actual_amount
            from contracts as c
            join contract_progress_payments as cpp
                on c.id = cpp.contract_id and cpp.is_approved = true
            join (
                select
                    csl.id, csl.contract_id, csl.status_id
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                    on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where c.is_published = true and
            cs.status_id not in (' . $contractStatusNotSigned['id'] . ',' . $contractStatusPending['id'] . ',' . $contractStatusCancelled['id'] . ')
            ' . $queryString['query_string'] . '
        ')[0];
        $data['signed_contracts_count_and_value'] = DB::select('
         select
                count(c.id) as contract_count,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                        ) as contracts_value
            from contracts as c
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where c.is_published = true and
            cs.status_id not in (' . $contractStatusNotSigned['id'] . ',' . $contractStatusPending['id'] . ',' . $contractStatusCancelled['id'] . ')
            ' . $queryString['query_string'] . '
        ')[0];
        $data['cancelled_contracts_count_and_value'] = DB::select('
         select
                count(c.id) as contract_count,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                        ) as contracts_value

            from contracts as c
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where c.is_published = true and
            cs.status_id = ' . $contractStatusCancelled['id'] . '
            ' . $queryString['query_string'] . '
        ')[0];
        $data['not_signed_contracts_count_and_value'] = DB::select('
         select
                count(c.id) as contract_count,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                        ) as contracts_value

            from contracts as c
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where c.is_published = true and
            cs.status_id = ' . $contractStatusNotSigned['id'] . '
            ' . $queryString['query_string'] . '
        ')[0];
        $data['amendment_contracts_count_and_value'] = DB::select('
            select
                count(a.contract_id) as contract_count,
                    sum(
                        ifnull(amendment.amount, 0)
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) as amendment_contracts_value
            from contracts as c
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id
            join(
                select  contract_id from amendments where is_approved = true group by contract_id
            ) as a
                on c.id = a.contract_id 
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where c.is_published = true and
            cs.status_id not in (' . $contractStatusNotSigned['id'] . ',' . $contractStatusPending['id'] . ',' . $contractStatusCancelled['id'] . ')
            ' . $queryString['query_string'] . '
        ')[0];

        $data['contract_plan_amount_and_percentage']->percentage = $data['signed_contracts_count_and_value']->contracts_value ?
            round(($data['contract_plan_amount_and_percentage']->contracts_plan_amount * 100) /
                $data['signed_contracts_count_and_value']->contracts_value, 2) : 0;
        $data['contract_actual_amount_and_percentage']->percentage = $data['signed_contracts_count_and_value']->contracts_value ?
            round(($data['contract_actual_amount_and_percentage']->contracts_actual_amount * 100) /
                $data['signed_contracts_count_and_value']->contracts_value, 2) : 0;
        return $data;
    }

    private function contractsCountAndValueBasedOnProcurementTypeChart($queryString)
    {
        $data = DB::select('
                            select
                                c.procurement_type_id as type_id,
                                count(c.id) as contracts_count,
                                    sum(
                                        (ifnull(cd.actual_value, 0) +
                                        ifnull(psc.provisional_sum_and_contingency, 0) +
                                        ifnull(amendment.amount, 0))
                                        * if(c.exchange_rate, c.exchange_rate, 1)
                                    ) as contract_total_value

                            from contracts as c
                            left join (
                                 select
                                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                        a.contract_id as contract_id
                                 from amendments as a
                                 left join cost_amendments as ca
                                     on ca.amendment_id = a.id
                                 left join time_and_cost_amendments as tca
                                     on tca.amendment_id = a.id
                                 where a.is_approved = true
                                 group by  a.contract_id
                            ) as amendment
                                on c.id = amendment.contract_id
                            join contract_details as cd
                                on cd.contract_id = c.id
                            join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id
                                and cdv.is_approved = true
                            left join provisional_sum_and_contingencies as psc
                                on psc.contract_detail_id = cd.id
                            ' . $queryString['joins'] . '
                            where  c.is_published = true
                            ' . $queryString['query_string'] . '
                            group by c.procurement_type_id;
        ');

        return $data;
    }

    private function contractValueBasedOnStatus($queryString)
    {
        $data['total_contract_value'] = DB::select('
        select
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) as contracts_total_value

            from contracts as c
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where c.is_published = true  ' . $queryString['query_string'] . ' ')[0];
        $data['contracts_value_based_status'] = DB::select('
        select
                cs.status_id as status_id,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    )  as status_value
            from (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on cs.contract_id = amendment.contract_id
            join contracts as c
                on c.id = cs.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
             ' . $queryString['joins'] . '
                where c.is_published = true ' . $queryString['query_string'] . '
            group by cs.status_id
			');
        return $data;
    }

    private function contractsCountBasedOnStatus($queryString)
    {
        $data['contracts_count'] = DB::select
        ('
            select
                count(c.id) as contract_count
            from contracts as c
            ' . $queryString['joins'] . '
            where c.is_published = true ' . $queryString['query_string'] . '
	    ')[0];
        $data['contracts_count_based_status'] = DB::select
        ('
           select
                cs.status_id as status_id,
                count(cs.contract_id) as status_count
            from (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            ' . $queryString['joins'] . '
            where c.is_published = true ' . $queryString['query_string'] . '
            group by cs.status_id

        ');

        return $data;
    }

    private function contractsAmendmentLevel($queryString, $amendmentTypes)
    {
        $timeAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-amendment');
        $timeAndCostAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-and-cost-amendment');
        $data = DB::select('
                    select
                        ifnull(
                          sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)))
                        , 0)  as total_value_percentage,
                        ifnull(
                          sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)))
                        , 0)  as cost_amendment_percentage,
                        ifnull(
                          sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))
                        , 0) as total_time_percentage,
                        ifnull(
                          sum(ifnull(amendment_time.days, 0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))
                        , 0) as time_amendment_percentage

                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    join contract_progresses as cp
                        on c.id = cp.contract_id
                    join contract_progress_verifications as cpv
                        on cp.id = cpv.contract_progress_id
                        and cpv.is_approved = true
                     left join (
                     select
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id

                    ) as amendment_cost
                       on amendment_cost.contract_id = c.id

                    left join (
                        select
                             a.contract_id,
                             GREATEST(
                                      ifnull(
                                          datediff(
                                              (select amended_end_date from time_amendments where amendment_id = a.id)
                                              ,
                                              (select planned_end_date from contract_details where contract_id = a.contract_id)
                                          ),0),
                                      ifnull(
                                            datediff(
                                                (select amended_end_date from time_and_cost_amendments where amendment_id = a.id)
                                                ,
                                                (select planned_end_date from contract_details where contract_id = a.contract_id)
                                            ),0)
                              ) as days
                        from amendments as a
                        join (
                            SELECT
                            contract_id, max(id) as max_id
                            FROM amendments
                            where type_id in (' . $timeAmendment['id'] . ',' . $timeAndCostAmendment['id'] . ') and is_approved =true
                            group by contract_id
                            ) as max_value
                            on max_value.max_id = a.id
                        group by a.contract_id, a.id
                    ) as amendment_time
                        on amendment_time.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where c.is_published = true
                    ' . $queryString['query_string'] . '
        ');
        return $data ? $data[0] : [];
    }

    private function contractsBasedOnProvince($queryString)
    {

        $data['no_shared_contracts'] = DB::select('
            select 
                tp.id as province_id,
                cs.status_id as contract_status_id,
                count(distinct_province.contract_id) as contracts_count,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(distinct_province.exchange_rate, distinct_province.exchange_rate, 1)
                    ) as total_contract_value
            from temp_provinces as tp
            join (
                select distinct 
                    inner_tp.id as province_id,
                    c.id as contract_id,
                    c.exchange_rate
                from domestic_contract_execution_locations as del
                join temp_districts as inner_td
                  on inner_td.id = del.district_id
                join temp_provinces as inner_tp
                    on inner_td.temp_province_id = inner_tp.id
                join contracts as c
                    on c.id = del.contract_id
                ' . $queryString['joins'] . '
                where c.is_published = true ' . $queryString['query_string'] . '
            ) as distinct_province
                on distinct_province.province_id = tp.id
            join (
              select
                    csl.id, csl.contract_id, csl.status_id
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                    on csl.id = max_value.max_id
            ) as cs
              on cs.contract_id = distinct_province.contract_id
            left join contract_details as cd
                on cd.contract_id = distinct_province.contract_id
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    group by  a.contract_id
            
            ) as amendment
                on distinct_province.contract_id = amendment.contract_id
            where distinct_province.contract_id not in
                (
                    select
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dc
                        on dc.contract_id = c.id
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                )
            group by tp.id, cs.status_id
              


        ');

        $data['shared_contracts'] = DB::select('
            select 
                tp.id as province_id,
                count(distinct_province.contract_id) as shared_contracts_count,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(distinct_province.exchange_rate, distinct_province.exchange_rate, 1)
                    ) as total_contract_shared_value
            from temp_provinces as tp
            join (
                select distinct 
                    inner_tp.id as province_id,
                    c.id as contract_id,
                    c.exchange_rate
                from domestic_contract_execution_locations as del
                join temp_districts as inner_td
                  on inner_td.id = del.district_id
                join temp_provinces as inner_tp
                    on inner_td.temp_province_id = inner_tp.id
                join contracts as c
                    on c.id = del.contract_id
                ' . $queryString['joins'] . '
                where c.is_published = true ' . $queryString['query_string'] . '
            ) as distinct_province
                on distinct_province.province_id = tp.id
            left join contract_details as cd
                on cd.contract_id = distinct_province.contract_id
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    group by  a.contract_id
            
            ) as amendment
                on distinct_province.contract_id = amendment.contract_id
            where distinct_province.contract_id in
                (
                    select
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dc
                        on dc.contract_id = c.id
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                )
            group by tp.id
        ');

        return $data;
    }

    private function contractsValueBasedOnDonor($queryString)
    {

        $data = DB::select('
            select
                sum(
                    (ifnull(cd.actual_value, 0) +
                    ifnull(psc.provisional_sum_and_contingency, 0) +
                    ifnull(amendment.amount, 0)) *
                    if(c.exchange_rate, c.exchange_rate, 1)
                ) as value,
                cdo.donor_id as donor_id ,
                c.procurement_type_id
            from contracts as c
            join contract_donors as cdo
            on c.id = cdo.contract_id
            join contract_details as cd
              on c.id = cd.contract_id
            join contract_details_verifications cdv
              on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join (
                select
                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                    a.contract_id as contract_id
                from amendments as a
                left join cost_amendments as ca
                    on ca.amendment_id = a.id
                left join time_and_cost_amendments as tca
                    on tca.amendment_id = a.id
                where a.is_approved = true
                group by  a.contract_id

            ) as amendment
                  on c.id = amendment.contract_id
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where c.is_published = true ' . $queryString['query_string'] . '
            group by cdo.donor_id, c.procurement_type_id
        ');
        return $data;
    }

    private function contractProgressVolume($queryString)
    {
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $otherQuarterYear = $current_jalali_date[0];
        $firstQuarter = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $otherQuarterYear = $current_jalali_date[0] + 1;
        } else {
            $firstQuarter = $current_jalali_date[0] - 1;
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => [10, 11, 12]],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => [1, 2, 3]],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => [4, 5, 6]],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => [7, 8, 9]],
        ];

        foreach ($year as $key => $value) {
            $data['actual_payments'][$key] = DB::select('
                             select
                                    sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))  as actual_amount
								from contracts as c
                                join contract_progress_payments as cpp
                                    on  cpp.contract_id = c.id and
                                        cpp.is_approved = true
                                ' . $queryString['joins'] . '
                                where cpp.year = ' . $value['year'] . ' and
                                    cpp.month_id in(' . $value['months'][0] . ',' . $value['months'][1] . ',' . $value['months'][2] . ')
                                ' . $queryString['query_string'] . '
                                and c.is_published = true

            ')[0];
            $data['plan_and_actual'][$key] = DB::select('
                    select
                        sum((cp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1)) as planning_progress_value,

                        sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1)) as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                    join contract_statuses as cs
                        on cs.contract_id = cp.contract_id
                    join contract_status_verifications as csv
                        on csv.contract_status_id = cs.id
                    join contracts as c
                        on cp.contract_id = c.id
                    left join contract_details as cd
	                    on cd.contract_id = c.id
                    left join (
                         select
                                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                a.contract_id as contract_id
                         from amendments as a
                         left join cost_amendments as ca
                             on ca.amendment_id = a.id
                         left join time_and_cost_amendments as tca
                             on tca.amendment_id = a.id
                         where a.is_approved = true
                         group by  a.contract_id
                    ) as amendment
                        on c.id = amendment.contract_id
                    ' . $queryString['joins'] . '
                    where
                        cpv.is_approved = true and
                        csv.is_approved = true  and
                        cp.year = ' . $value['year'] . ' and
                        cp.month_id in(' . $value['months'][0] . ',' . $value['months'][1] . ',' . $value['months'][2] . ')
                    ' . $queryString['query_string'] . '
                    and c.is_published = true
        ')[0];
            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1))  as planning_progress_value,

                        sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1))  as actual_progress_value

                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join contract_statuses as cs
                        on cs.contract_id = cr.contract_id
                    join contract_status_verifications as csv
                        on csv.contract_status_id = cs.id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join contract_details as cd
                        on cd.contract_id = c.id
                    ' . $queryString['joins'] . '
                    left join (
                         select
                                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                a.contract_id as contract_id
                         from amendments as a
                         left join cost_amendments as ca
                             on ca.amendment_id = a.id
                         left join time_and_cost_amendments as tca
                             on tca.amendment_id = a.id
                         where a.is_approved = true
                         group by  a.contract_id
                    ) as amendment
                        on c.id = amendment.contract_id
                    where
                        crv.is_approved = true and
                        csv.is_approved = true and
                        crp.year = ' . $value['year'] . ' and
                        crp.month_id in(' . $value['months'][0] . ',' . $value['months'][1] . ',' . $value['months'][2] . ')
                    ' . $queryString['query_string'] . '
                    and c.is_published = true
                    group by cr.iteration_count;

        ');

        }

        return $data;

    }

    private function typeOfAmendmentsInContractsChart($queryString)
    {
        $data = DB::select('
        select
            a.type_id as type_id,
            count(distinct a.contract_id) as contracts_count
        from contracts as c
        join amendments as a
            on a.contract_id = c.id
            and a.is_approved = true
        join(
                select  contract_id from amendments where is_approved = true group by contract_id
            ) as ad
                on c.id = ad.contract_id 
        ' . $queryString['joins'] . '
        where c.is_published = true  ' . $queryString['query_string'] . '
        group by  a.type_id;
       ');

        return $data;
    }

    private function contractChallengesContractAmount($queryString)
    {
        $data = DB::select('

                    select
                        no_duplication.category_id,
                        count(no_duplication.contract_id) as contract_count,
                            sum(
                                (ifnull(cd.actual_value, 0) +
                                ifnull(psc.provisional_sum_and_contingency, 0) +
                                ifnull(amendment.amount, 0))
                                * if(c.exchange_rate, c.exchange_rate, 1)
                            ) as contract_value
                    from (select distinct
                                car.category_id,
                                car.contract_id
                            from challenges_and_remarks as car
                            where car.is_approved = true
                            ) as no_duplication
                    join contract_details as cd
                        on cd.contract_id = no_duplication.contract_id
                    left join (
                         select
                                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                a.contract_id as contract_id
                            from amendments as a
                            left join cost_amendments as ca
                                on ca.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            group by  a.contract_id

                    ) as amendment
                        on  amendment.contract_id = cd.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id
                    join contract_details_verifications as cdv
                        on cd.id = cdv.contract_detail_id
                        and cdv.is_approved = true
                    join contracts as c
                        on c.id = no_duplication.contract_id
                     ' . $queryString['joins'] . '
                     where 1 = 1
                    ' . $queryString['query_string'] . '
                    group by no_duplication.category_id

            ');
        return $data;
    }

    private function numberOfContractsBasedOnCompany($queryString)
    {
        $data = DB::select('
                  select
                      c.procurement_type_id,
                      com.license_number,
                      count(c.id) as contract_count
                    from contracts as c
                    inner join company_general_informations as cgi
                      on c.id = cgi.contract_id
                    inner join companies as com
                      on cgi.id = com.company_general_information_id
                    join contract_details as cd
                      on c.id = cd.contract_id
                    ' . $queryString['joins'] . '
                    where c.is_published = true
                    ' . $queryString['query_string'] . '
                    group by c.procurement_type_id, com.license_number');
        return $data;
    }

    public function prepareQueryStrings($queryArray, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');
        $contractCompletionDefectLiabilityPeriodStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');
        $suspendedStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'suspended');
        $startYearArray = isset($queryArray['start_year']) ? JDateTime::toGregorian($queryArray['start_year'] - 1, 10, 1) : null;
        $endYearArray = isset($queryArray['end_year']) ? JDateTime::toGregorian($queryArray['end_year'], 9, 30) : null;
        $fiscal_start_year = $startYearArray ? $startYearArray[0] . '-' . $startYearArray[1] . '-' . $startYearArray[2] : null;
        $fiscal_end_year = $endYearArray ? ($endYearArray[0]) . '-' . $endYearArray[1] . '-' . $endYearArray[2] : null;
        $data['joins'] = '';
        $data['query_string'] = '';
        $contractTotalValue = false;
        $actualPaymentsValue = false;
        $actualPaymentsPhysicalPercentage = false;
        $fiscalYear = false;
        $locationJoinCondition = true;
        $costAmendmentPercentage = false;
        $timeAmendmentPercentage = false;

        foreach ($queryArray as $key => $value) {
            switch ($key) {
                case 'procurement_entity_id':
                    $data['query_string'] .= ' and c.procurement_entity_id = ' . $value;
                    break;
                case 'sector_id':
                    $data['query_string'] .= ' and c.sector_id = ' . $value;
                    break;
                case 'procurement_method_id':
                    $data['query_string'] .= ' and c.procurement_method_id = ' . $value;
                    break;
                case 'procurement_type_id':
                    $data['query_string'] .= ' and c.procurement_type_id = ' . $value;
                    break;
                case 'donor_id':
                    $data['joins'] .= ' join contract_donors as qcdj on qcdj.contract_id = c.id ';
                    $data['query_string'] .= ' and qcdj.donor_id = ' . $value;
                    break;
                case 'amendment_id':
                    $data['joins'] .= ' join amendments as qaj on qaj.contract_id = c.id and qaj.is_approved = true';
                    $data['query_string'] .= ' and qaj.type_id = ' . $value;
                    break;
                case 'zone_id':
                case 'province_id':
                case 'district_id':
                    if ($locationJoinCondition && isset($queryArray['district_id'])) {
                        $locationJoinCondition = false;
                        $data['query_string'] .= ' and c.id in (
                                select distinct
                                    inner_del.contract_id
                                from domestic_contract_execution_locations as inner_del
                                where inner_del.district_id in (
                                    select 
                                        td.id
                                    from temp_districts as td
                                    where td.id = ' . $queryArray['district_id'] . '
                                )
                            )';
                    }
                    if ($locationJoinCondition && isset($queryArray['province_id'])) {
                        $locationJoinCondition = false;
                        $data['query_string'] .= ' and c.id in (
                                select distinct
                                    inner_del.contract_id
                                from domestic_contract_execution_locations as inner_del
                                where inner_del.district_id in (
                                    select 
                                        td.id
                                    from temp_districts as td
                                    join temp_provinces as tp
                                        on tp.id = td.temp_province_id
                                    join temp_zones as tz
                                        on tp.temp_zone_id = tz.id
                                    where tp.id = ' . $queryArray['province_id'] . '
                                )
                            )';
                    }
                    if ($locationJoinCondition && isset($queryArray['zone_id'])) {
                        $locationJoinCondition = false;
                        $data['query_string'] .= ' and c.id in (
                                select distinct
                                    inner_del.contract_id
                                from domestic_contract_execution_locations as inner_del
                                where inner_del.district_id in (
                                    select 
                                        td.id
                                    from temp_districts as td
                                    join temp_provinces as tp
                                        on tp.id = td.temp_province_id
                                    join temp_zones as tz
                                        on tp.temp_zone_id = tz.id
                                    where tz.id = ' . $queryArray['zone_id'] . '
                                )
                            )';
                    }

                    break;
                case 'status_id':
                    $data['joins'] .= ' join contract_status_logs as qcslj
                                    on qcslj.contract_id = c.id';

                    $data['query_string'] .= '
                                        and c.id in (select
                                            qcslj.contract_id
                                        from contract_status_logs as qcslj
                                        where qcslj.id = (
                                            select
                                            max(inner_csl.id)
                                            from contract_status_logs as inner_csl
                                            where qcslj.contract_id = inner_csl.contract_id)
                                            and qcslj.status_id = ' . $value . '
                                        group by qcslj.contract_id)
                                ';
                    break;
                case 'transferred_contracts_year':
                    $startFiscalYearDate = JDateTime::toGregorian($queryArray['transferred_contracts_year'] - 1, 10, 1);
                    $startFiscalYearDate = $startFiscalYearDate[0] . '-' . $startFiscalYearDate[1] . '-' . $startFiscalYearDate[2];
                    $data['query_string'] .= ' and c.id in
                                            (
                                           select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            join (
                                              select
                                                    csl.id, csl.contract_id, csl.status_id
                                                from contract_status_logs as csl
                                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                                    on csl.id = max_value.max_id
                                            ) as cs
                                                on cs.contract_id = q_inner_c.id
                                            join contract_details as cd 
							                    on q_inner_c.id = cd.contract_id
                                            where cs.status_id in (' . $workInProgressStatus['id'] . ',' . $contractCompletionDefectLiabilityPeriodStatus['id'] . ',' . $suspendedStatus['id'] . ') and 
                                                cd.agreement_signature_date < "' . $startFiscalYearDate . '"
                                            ) ';
                    break;
                case (preg_match('/total_value.*/', $key) ? true : false):

                    if (!$contractTotalValue) {
                        $contractTotalValue = true;
                        $condition = ' between ' . $queryArray['total_value_from'] * 1000000 . ' and ' . $queryArray['total_value_to'] * 1000000;
                        $data['query_string'] .= ' and c.id in
                                            (select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            left join contract_details as q_inner_cd
                                                on q_inner_cd.contract_id = q_inner_c.id
                                            left join contract_details_verifications as q_inner_cdv
                                                on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                            where (
                                                    (ifnull(q_inner_cd.actual_value, 0) * ifnull( q_inner_c.exchange_rate, 1)) +
                                                    (ifnull((
                                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                                from amendments as q_inner_a
                                                                left join cost_amendments as q_inner_ca
                                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                                left join time_and_cost_amendments as q_inner_tca
                                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                                where q_inner_a.contract_id = q_inner_c.id
                                                            ), 0) * ifnull( q_inner_c.exchange_rate, 1))

                                                    )  ' . $condition . '
                                            ) ';
                    }
                    break;
                case (preg_match('/year.*/', $key) ? true : false):
                    if (!$fiscalYear) {
                        $fiscalYear = true;
                        $condition = ' between "' . $fiscal_start_year . '" and "' . $fiscal_end_year . '"';
                        $data['query_string'] .= '   and c.id in (
                                        select cs.id
                                        from contracts as cs
                                        left join contract_details as cd
                                            on cd.contract_id = cs.id 
                                        where cd.agreement_signature_date ' . $condition . '
                                        )  ';
                    }
                    break;
                case (preg_match('/cost_amendment.*/', $key) ? true : false) :
                    if (!$costAmendmentPercentage) {
                        $costAmendmentPercentage = true;
                        $condition = ' between ' . $queryArray['cost_amendment_start'] . ' and ' . $queryArray['cost_amendment_end'];
                        $data['query_string'] .= ' and c.id in
                                    (
                                        select q_inner_c.id
                                        from contracts as q_inner_c
                                        left join contract_details as q_inner_cd
                                        on q_inner_cd.contract_id = q_inner_c.id
                                        left join contract_details_verifications as q_inner_cdv
                                        on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                        and q_inner_cdv.is_approved = true
                                        where ((ifnull((
                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                from amendments as q_inner_a
                                                left join cost_amendments as q_inner_ca
                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                left join time_and_cost_amendments as q_inner_tca
                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true
                                            ) * ifnull(q_inner_c.exchange_rate,1), 0) * 100) /
                                            if(q_inner_cd.actual_value * ifnull(q_inner_c.exchange_rate, 1), q_inner_cd.actual_value * ifnull(q_inner_c.exchange_rate, 1), 1))  ' . $condition . '

                                     ) ';
                    }
                    break;
                case (preg_match('/time_amendment.*/', $key) ? true : false):

                    if (!$timeAmendmentPercentage) {
                        $timeAmendmentPercentage = true;
                        $condition = ' between ' . $queryArray['time_amendment_start'] . ' and ' . $queryArray['time_amendment_end'];
                        $data['query_string'] .= ' and c.id in (
                                        select   q_inner_c.id
                                        from contracts as q_inner_c
                                        left join contract_details as q_inner_cd
                                        on q_inner_cd.contract_id = q_inner_c.id
                                        left join contract_details_verifications as q_inner_cdv
                                        on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                        and q_inner_cdv.is_approved = true
                                        left join contract_progresses as q_inner_cp
                                        on q_inner_cp.contract_id = q_inner_c.id
                                        left join contract_progress_verifications as q_inner_cpv
                                        on q_inner_cpv.contract_progress_id = q_inner_cp.id
                                        and q_inner_cpv.is_approved = true
                                        where (
                                            ifnull(TIMESTAMPDIFF(MINUTE,q_inner_cd.planned_end_date,
                                                        (select if( max(q_inner_ta.amended_end_date) > max(q_inner_tca.amended_end_date),
                                                        max(q_inner_ta.amended_end_date) ,
                                                        max(q_inner_tca.amended_end_date)) as maxDate
                                                        from amendments as q_inner_a
                                                        left join time_amendments as q_inner_ta
                                                            on q_inner_ta.amendment_id = q_inner_a.id
                                                        left join time_and_cost_amendments as q_inner_tca
                                                            on q_inner_tca.amendment_id = q_inner_a.id
                                                        where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true
                                                        )
                                                ), 0) * 100 /
                                                if(TIMESTAMPDIFF(MINUTE,q_inner_cp.actual_start_date, q_inner_cd.planned_end_date),
                                                TIMESTAMPDIFF(MINUTE,q_inner_cp.actual_start_date, q_inner_cd.planned_end_date), 1))    ' . $condition . '
                                ) ';
                    }
                    break;
                case (preg_match('/actual_payment.*/', $key) ? true : false):

                    if (!$actualPaymentsValue) {
                        $actualPaymentsValue = true;
                        $condition = ' between ' . ((int)$queryArray['actual_payment_from'] * 1000000) . ' and ' . ((int)$queryArray['actual_payment_to'] * 1000000);
                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (
                                    select
                                        ifnull(sum(q_cpp_inner.amount), 0) *
                                        ifnull(q_inner_c.exchange_rate, 1)
                                    from contract_progress_payments as q_cpp_inner
                                    where q_cpp_inner.contract_id = q_inner_c.id
                                    and q_cpp_inner.is_approved = true)  ' . $condition . '

                                    ) ';
                    }
                    break;
                case (preg_match('/physical_progress.*/', $key) ? true : false):
                    if (!$actualPaymentsPhysicalPercentage) {
                        $actualPaymentsPhysicalPercentage = true;
                        $condition = ' between ' . ((int)$queryArray['physical_progress_index_from'] * 1000000) . ' and ' . ((int)$queryArray['physical_progress_index_to'] * 1000000);

                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (
                                        select
                                            (ifnull(sum(q_cpp_inner.physical_progress_percentage), 0) / 100) *
                                            (
                                                select
                                                    sum((inner_cd.actual_value * ifnull(q_inner_c.exchange_rate, 1)) + (ifnull((
                                                        select
                                                        sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                                        from amendments as a
                                                        left join cost_amendments as ca
                                                        on ca.amendment_id = a.id
                                                        left join time_and_cost_amendments as tca
                                                        on tca.amendment_id = a.id
                                                        where a.contract_id = inner_cd.contract_id
                                                    ), 0) * ifnull(q_inner_c.exchange_rate, 1))) as total_contract_value
                                                from contract_details as inner_cd
                                                join contract_details_verifications as inner_cdv
                                                on inner_cd.id = inner_cdv.contract_detail_id
                                                and inner_cdv.is_approved = true
                                                where inner_cd.contract_id = q_cpp_inner.contract_id
                                            )
                                        from contract_progress_payments as q_cpp_inner
                                        where q_cpp_inner.contract_id = q_inner_c.id
                                        and q_cpp_inner.is_approved = true
                                        group by q_cpp_inner.contract_id

                                    ) ' . $condition . '

                                    ) ';
                    }
                    break;
            }
        }
        return $data;

    }
    public function covid19DashboardData() {
        $data =DB::select(
            'select 
                    c.id as contract_id,
                    c.npa_identification_number,
                    c.contract_number,
                    cd.agreement_signature_date as contract_signing_date,
					c.currency_id,
                    cp.actual_start_date,
                    cd.planned_end_date,
                    comp.license_number,
                    ifnull(cs.status_id, 0) as contract_status_id,
					(ifnull(cd.actual_value , 0) * ifnull(c.exchange_rate, 1))  as contract_amount_initial,
                        ((
                    select 
                        (ifnull(cd.actual_value, 0) +  ifnull(amendment.amount, 0) + ifnull(psc.provisional_sum_and_contingency, 0)) as total_contract_value 
                    from contract_details as cd
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                    
                    ) as amendment
                          on cd.contract_id = amendment.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    where cd.contract_id = c.id
                ) * ifnull(c.exchange_rate, 1)) as contract_amount_final,
                 (ifnull(cpptotal.amount, 0) * ifnull(c.exchange_rate, 1)) as payment_to_date_afn,
                 ifnull(cpptotal.percentage, 0) as progress_to_date
                FROM contracts c
                left join contract_progresses cp on c.id = cp.contract_id
                left join contract_details cd on c.id = cd.contract_id
                left join contract_statuses cs on c.id = cs.contract_id
                left join company_general_informations cgi on c.id = cgi.contract_id
                left join companies as comp on comp.company_general_information_id = cgi.id

                left join (
                            select 
                                p.contract_id,
                                sum(ifnull(p.amount, 0)) as amount,
                                sum(ifnull(p.physical_progress_percentage, 0)) as percentage
                            from contract_progress_payments as p
                            where  p.is_published = true
                            group by p.contract_id
                            
                        ) as cpptotal
                            on cpptotal.contract_id = c.id
               where   c.is_published  = true
               order by c.id ASC'
        );


        return response()->json($data, 200);
    }
}
