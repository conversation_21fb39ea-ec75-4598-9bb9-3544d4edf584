<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class TriggerToCheckGeneralApprove extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_approval_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_approval_verifications_trigger_check_general_approve
        before update on contract_approval_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            declare contract_id_var integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select contract_id into contract_id_var from contract_approvals as ca 
            join contract_approval_verifications as cav on ca.id = cav.contract_approval_id
            where cav.contract_approval_id = new.contract_approval_id;
            select count(*) into is_general_approved from contracts as c where c.id = contract_id_var and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `p_e_contact_person_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger p_e_contact_person_verifications_trigger_check_general_approve
        before update on p_e_contact_person_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `domestic_contract_execution_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger domestic_contract_execution_trigger_check_general_approve
        before update on domestic_contract_execution_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_details_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_details_verifications_trigger_check_general_approve
        before update on contract_details_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            declare contract_id_var integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select contract_id into contract_id_var from contract_details as cd 
            join contract_details_verifications as cdv on cd.id = cdv.contract_detail_id
            where cdv.contract_detail_id = new.contract_detail_id;
            select count(*) into is_general_approved from contracts as c where c.id = contract_id_var and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `company_general_informations_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger company_general_informations_trigger_check_general_approve
        before update on company_general_informations
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `subcontract_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger subcontract_verifications_trigger_check_general_approve
        before update on subcontract_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_planning_f_a_a_p_p_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger contract_planning_f_a_a_p_p_trigger_check_general_approve
        before update on contract_planning_f_a_a_p_p_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `liquidated_damages_delivery_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec("
        create trigger liquidated_damages_delivery_trigger_check_general_approve
        before update on liquidated_damages_delivery_verifications
        for each row
        exit_label: begin
            declare is_general_approved integer default null;
            if  
                (new.is_approved  = old.is_approved ) or
                (new.is_approved  is null and old.is_approved  is null)
            then
                leave exit_label;
            end if;
            select count(*) into is_general_approved from contracts as c where c.id = new.contract_id and c.is_approved = true;
            if is_general_approved != 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0007|||';
            end if;
            set is_general_approved= null;
        end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_approval_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `p_e_contact_person_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `domestic_contract_execution_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_details_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `company_general_informations_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `subcontract_verifications_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `contract_planning_f_a_a_p_p_trigger_check_general_approve`');
        DB::connection()->getpdo()->exec('drop trigger if exists `liquidated_damages_delivery_trigger_check_general_approve`');
    }
}
