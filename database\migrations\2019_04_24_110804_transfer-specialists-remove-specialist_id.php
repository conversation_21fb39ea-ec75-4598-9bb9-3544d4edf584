<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class TransferSpecialistsRemoveSpecialistId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('  
            insert into contract_specialists (contract_id, specialist_id)
            select id, specialist_id
            from contracts
            where specialist_id is not null
              '
        );

        DB::statement('
            ALTER TABLE contracts DROP COLUMN specialist_id;
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('
            ALTER TABLE contracts ADD COLUMN specialist_id int(11) AFTER contract_manager_id;
        ');
    }
}
