<?php

namespace NPA\ACPMS\Http\Controllers\SpecialistMonitoringReport;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Helpers\SpecialistMonitoringGetReport;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Mail\SpecialistMonitoringReport;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\PARS\ParsAdvancePaymentGuarantie;
use NPA\ACPMS\Models\PARS\ParsChallengesAndRemark;
use NPA\ACPMS\Models\PARS\ParsChart;
use NPA\ACPMS\Models\PARS\ParsCheckDb;
use NPA\ACPMS\Models\PARS\ParsConditionAmendment;
use NPA\ACPMS\Models\PARS\ParsContractDonor;
use NPA\ACPMS\Models\PARS\ParsContractBudgetCode;
use NPA\ACPMS\Models\PARS\ParsContractDuration;
use NPA\ACPMS\Models\PARS\ParsContractExecutionLocation;
use NPA\ACPMS\Models\PARS\ParsContractPerformanceSecurity;
use NPA\ACPMS\Models\PARS\ParsContractProgress;
use NPA\ACPMS\Models\PARS\ParsContractProgressStop;
use NPA\ACPMS\Models\PARS\ParsContractValue;
use NPA\ACPMS\Models\PARS\ParsCostAmendment;
use NPA\ACPMS\Models\PARS\ParsDefectLiability;
use NPA\ACPMS\Models\PARS\ParsDelayPenaltie;
use NPA\ACPMS\Models\PARS\ParsGeneralInformation;
use NPA\ACPMS\Models\PARS\ParsRemark;
use NPA\ACPMS\Models\PARS\ParsTimeAmendment;
use NPA\ACPMS\Models\PARS\ParsCostAndTimeAmendment;
use NPA\ACPMS\Models\PARS\ParsVendor;
use NPA\ACPMS\Models\PARS\ProgressAnalysisReport;

class SpecialistMonitoringReportSnapshotController extends Controller
{
    public function createSnapshot(Request $request)
    {
        try {
            DB::beginTransaction();
            $contractId = $request->get('contract_id');
            $snapshot = SpecialistMonitoringReportController::aggregateReportData($contractId);
            $currentDate = Date::gregorianToJalali(Carbon::now()->toDateString());
            $currentDateArray = explode('-', $currentDate);
            $user = Helper::getUsersByIds([$request['user_id']]);
            $reports = ProgressAnalysisReport::where('contract_id', $snapshot['contract_id'])->get();
            $progressAnalysisReport = ProgressAnalysisReport::create([
                'serial_number' => sizeof($reports) + 1,
                'contract_id' => $snapshot['contract_id'],
                'confirmation_timestamp' => null,
                'is_auto_saved' => false,
                'month' => $currentDateArray[1],
                'year' => $currentDateArray[2],
                'creator_user_id' => $request['user_id'],
                'user_full_name' => $user[0]->full_name,
                'is_confirmed' => false,
                'is_approved' => false
            ]);

            $snapshot['general_information']['progress_analysis_reports_id'] = $progressAnalysisReport->id;
            $ParsGeneralInformation = ParsGeneralInformation::create(array_only($snapshot['general_information'], ParsGeneralInformation::$fields));
            foreach ($snapshot['vendors'] as $vendor) {
                $vendor['pars_general_informations_id'] = $ParsGeneralInformation['id'];
                ParsVendor::create($vendor, ParsVendor::$fields);
            }
            foreach ($snapshot['domestic_contract_execution_locations'] as $location) {
                $location['pars_general_info_id'] = $ParsGeneralInformation->id;
                ParsContractExecutionLocation::create(array_only($location, ParsContractExecutionLocation::$fields));
            }
            foreach ($snapshot['contract_donors'] as $donor) {
                $donor['pars_general_informations_id'] = $ParsGeneralInformation->id;
                ParsContractDonor::create(array_only($donor, ParsContractDonor::$fields));
            }
            $snapshot['budget']['pars_general_informations_id'] = $ParsGeneralInformation->id;
            ParsContractBudgetCode::create(array_only($snapshot['budget'], ParsContractBudgetCode::$fields));

            $snapshot['performance_security']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsContractPerformanceSecurity::create(array_only($snapshot['performance_security'], ParsContractPerformanceSecurity::$fields));

            $snapshot['values']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsContractValue::create(array_only($snapshot['values'], ParsContractValue::$fields));

            $snapshot['durations']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsContractDuration::create(array_only($snapshot['durations'], ParsContractDuration::$fields));

            $snapshot['progress']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsContractProgress::create(array_only($snapshot['progress'], ParsContractProgress::$fields));

            $snapshot['advance_payment_guaranty']['p_a_r_id'] = $progressAnalysisReport->id;
            $snapshot['advance_payment_guaranty']['expiration_date'] = null;
            ParsAdvancePaymentGuarantie::create(array_only($snapshot['advance_payment_guaranty'], ParsAdvancePaymentGuarantie::$fields));

            if ($snapshot['amendment'] && $snapshot['amendment']['time']) {
                foreach ($snapshot['amendment']['time'] as $timeAmendment) {
                    $timeAmendment['p_a_r_id'] = $progressAnalysisReport->id;
                    $timeAmendment['amendment_approval_date'] = null;
                    unset($timeAmendment['confirmed_status']);
                    ParsTimeAmendment::create(array_only($timeAmendment, ParsTimeAmendment::$fields));
                }
            }

            if ($snapshot['amendment'] && $snapshot['amendment']['cost']) {
                foreach ($snapshot['amendment']['cost'] as $costAmendment) {
                    $costAmendment['p_a_r_id'] = $progressAnalysisReport->id;
                    $costAmendment['amendment_approval_date'] = null;
                    unset($costAmendment['confirmed_status']);
                    ParsCostAmendment::create(array_only($costAmendment, ParsCostAmendment::$fields));
                }
            }
            if ($snapshot['amendment'] && $snapshot['amendment']['time_and_cost']) {
                foreach ($snapshot['amendment']['time_and_cost'] as $timeAndCostAmendment) {
                    $timeAndCostAmendment['p_a_r_id'] = $progressAnalysisReport->id;
                    $timeAndCostAmendment['amendment_approval_date'] = null;
                    unset($timeAndCostAmendment['confirmed_status']);
                    ParsCostAndTimeAmendment::create(array_only($timeAndCostAmendment, ParsCostAndTimeAmendment::$fields));
                }
            }
            if ($snapshot['amendment'] && $snapshot['amendment']['condition']) {
                foreach ($snapshot['amendment']['condition'] as $conditionAmendment) {
                    $conditionAmendment['p_a_r_id'] = $progressAnalysisReport->id;
                    $conditionAmendment['amendment_approval_date'] = null;
                    unset($conditionAmendment['confirmed_status']);
                    ParsConditionAmendment::create(array_only($conditionAmendment, ParsConditionAmendment::$fields));
                }
            }
            if ($snapshot['challenges_and_remarks']) {
                foreach ($snapshot['challenges_and_remarks'] as $challenges) {
                    $challenges['p_a_r_id'] = $progressAnalysisReport->id;
                    ParsChallengesAndRemark::create(array_only($challenges, ParsChallengesAndRemark::$fields));
                }
            }
            $snapshot['charts']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsChart::create(array_only($snapshot['charts'], ParsChart::$fields));
            $snapshot['check_dbs']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsCheckDb::create(array_only($snapshot['check_dbs'], ParsCheckDb::$fields));

            $snapshot['delay_penalties']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsDelayPenaltie::create(array_only($snapshot['delay_penalties'], ParsDelayPenaltie::$fields));

            $snapshot['defect_liability']['p_a_r_id'] = $progressAnalysisReport->id;
            ParsDefectLiability::create(array_only($snapshot['defect_liability'], ParsDefectLiability::$fields));

            foreach ($snapshot['stops_duration'] as $duration) {
                $duration['p_a_r_id'] = $progressAnalysisReport->id;
                ParsContractProgressStop::create(array_only($duration, ParsContractProgressStop::$fields));
            }
            $snapshot['id'] = $progressAnalysisReport->id;
            $snapshot['serial_number'] = $progressAnalysisReport->serial_number;
            $snapshot['contract_id'] = $progressAnalysisReport->contract_id;
            $snapshot['confirmation_timestamp'] = $progressAnalysisReport->confirmation_timestamp;
            $snapshot['generation_timestamp'] = $progressAnalysisReport->generation_timestamp;
            $snapshot['month'] = $progressAnalysisReport->month;
            $snapshot['year'] = $progressAnalysisReport->year;
            $snapshot['is_auto_saved'] = $progressAnalysisReport->is_auto_saved;
            $snapshot['creator_user_id'] = $progressAnalysisReport->creator_user_id;
            $snapshot['user_full_name'] = $progressAnalysisReport->user_full_name;
            DB::commit();
            return response()->json($snapshot);
        } catch (\Throwable $t) {
            DB::rollBack();
            $t->getMessage() === "|||NPA-ACPMS-0010|||" ? $code = "|||NPA-ACPMS-0010|||" : $code = null;
            return Error::composeResponse($t, $code);
        }
    }

    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $data = ProgressAnalysisReport::where('contract_id', $contractId)
            ->with(['general_information' => function ($query) {
                $query->with(['vendors', 'contract_execution_locations', 'donors', 'budget']);
            },
                'performance_security',
                'values',
                'progress',
                'durations',
                'advance_payment_guaranty',
                'time_amendments',
                'cost_amendments',
                'time_and_cost_amendments',
                'condition_amendments',
                'challenges_and_remarks',
                'charts',
                'check_dbs',
                'defect_liabilities',
                'delay_penalties',
                'progress_stops',
                'remarks'])->get();


        foreach ($data as &$item) {

            $attachments = $item->pdf_report()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
//                $index = $attachments[$key]['field_name'];
//                $att = [];
//                foreach ($attachments as $k => $v) {
//                    if ($v['field_name'] == $index && $v['table_name'] == 'progress_analysis_reports') {
//                        array_push($att, $v);
//                    }
//                }
//                $item[$attachments[$key]['field_name']] = $att;
                if ($value['field_name'] == 'specialist_report_pdf_version' && $value['table_name'] == 'progress_analysis_reports') {
                    $item[$value['field_name']] = $value;
                    \Log::info($value['field_name']);
                };
            }
        }
        return response()->json($data, 200);
    }

    public function showSnapshot($id)
    {
        $data = ProgressAnalysisReport::where('id', $id)
            ->with(['general_information' => function ($query) {
                $query->with(['vendors', 'contract_execution_locations', 'donors', 'budget']);
            },
                'performance_security',
                'values',
                'progress',
                'durations',
                'advance_payment_guaranty',
                'time_amendments',
                'cost_amendments',
                'time_and_cost_amendments',
                'condition_amendments',
                'challenges_and_remarks',
                'charts',
                'check_dbs',
                'defect_liabilities',
                'delay_penalties',
                'progress_stops',
                'remarks'])->first();
        $countContracts = ProgressAnalysisReport::where('contract_id', $data->contract_id)->count();
        $data['number_of_reports_in_this_contract'] = $countContracts;

        $contractAllRemarks = DB::table('progress_analysis_reports')
            ->join('pars_remarks', 'progress_analysis_reports.id', '=', 'pars_remarks.p_a_r_id')
            ->where('progress_analysis_reports.contract_id', $data->contract_id)
            ->where('progress_analysis_reports.id', '<>', $id)
            ->select('progress_analysis_reports.year',
                'progress_analysis_reports.month',
                'pars_remarks.old_suggestion',
                'pars_remarks.new_suggestion',
                'pars_remarks.implemented_suggestions',
                'pars_remarks.not_implemented_suggestions',
                'implemented_suggestions_vendor',
                'not_implemented_suggestions_vendor',
                'old_suggestions_vendor',
                'new_suggestions_vendor')
            ->orderBy('progress_analysis_reports.id', 'ASC')
            ->get();

        $data['contract_all_remarks'] = $contractAllRemarks;

        return response()->json($data, 200);
    }

    public function destroySnapshot($id)
    {
        $isDeleted = ProgressAnalysisReport::where('id', $id)->delete();
        if (!$isDeleted) {
            return response()->json([], 404);
        }
        return response()->json([], 204);
    }

    public function verification(Request $request)
    {

        try {

            $confirm = [
                'is_confirmed' => $request['is_confirmed'],
                'is_approved' => false,
            ];

            $approve = [
                'is_confirmed' => true,
                'is_approved' => $request['is_approved'],
            ];

            $parsRemarks = ParsRemark::where('p_a_r_id', $request['id'])->first();

            if ($parsRemarks) {
                if ($request['type_of_request'] === 'confirm') {
                    $progressAnalysisReport = ProgressAnalysisReport::where('id', $request['id'])->first();
                    $progressAnalysisReport->update(array_only($confirm, ProgressAnalysisReport::$fields));
                }

                if ($request['type_of_request'] === 'approve') {
                    $progressAnalysisReport = ProgressAnalysisReport::where('id', $request['id'])->first();
                    $progressAnalysisReport->update(array_only($approve, ProgressAnalysisReport::$fields));
                }
            } else {
                throw new \Exception('|||NPA-ACPMS-0011|||');
            }
        } catch (\Throwable $t) {
            $t->getMessage() === "|||NPA-ACPMS-0011|||" ? $code = "|||NPA-ACPMS-0011|||" : $code = null;
            return Error::composeResponse($t, $code);
        }
    }

    public function sendMail(Request $request)
    {

        try {
            $all = $request->all();
            if (is_array($request['data'])) {
                $emails = [];
                foreach ($request['data'] as $item) {
                    array_push($emails, $item['email']);
                }
                $progressAnalysisReport = ProgressAnalysisReport::where('id', $all['id'])->with(['general_information'])->first();
                $attachment = Attachment::find($all['file_id']);
                if (config('custom.CUSTOM_STORAGE_USED') === 's3') {
                    $data = Storage::disk('s3')->get($attachment['stored_path']);
                } else {
                    $data = Storage::get($attachment['stored_path']);
                }
                Mail::send('emails.specialist-monitoring-report',
                    [
                        'serial_number' => $progressAnalysisReport['serial_number'],
                        'month' => $progressAnalysisReport['month'],
                        'year' => $progressAnalysisReport['year'],
                        'contract_name' => $progressAnalysisReport['general_information']['contract_name'],
                        'contract_number' => $progressAnalysisReport['general_information']['contract_number']
                    ],
                    function ($message) use ($emails, $data, $progressAnalysisReport, $attachment) {
                        $message->to($emails)->subject('گزارش نظارت از پیشرفت قرارداد  ' . $progressAnalysisReport['general_information']['contract_number']);
                        $message->attachData($data, $attachment['original_name'], []);
                    });
            }

        } catch (\Throwable $t) {
            $t->getMessage() === "|||NPA-ACPMS-0011|||" ? $code = "|||NPA-ACPMS-0011|||" : $code = null;
            return Error::composeResponse($t, $code);
        }
    }
}
