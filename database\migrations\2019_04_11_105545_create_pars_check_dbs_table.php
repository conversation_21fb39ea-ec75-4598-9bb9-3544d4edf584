<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsCheckDbsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_check_dbs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->integer('actual_plan_physical_progress_diff')->nullable();
            $table->integer('actual_plan_payment_progress_diff')->nullable();
            $table->boolean('is_performance_guaranty_received')->nullable();
            $table->boolean('is_subcontracting_legal')->nullable();
            $table->boolean('is_performance_guaranty_renewed')->nullable();
            $table->boolean('is_completion_document_uploaded')->nullable();
            $table->boolean('is_contract_termination_doc_issued')->nullable();
            $table->boolean('is_supplies_legally_returned')->nullable();
            $table->boolean('is_main_contract_have_subcontracts')->nullable();
            $table->integer('renewed_performance_total_amount')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_check_dbs');
    }
}
