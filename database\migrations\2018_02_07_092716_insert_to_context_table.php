<?php

use Illuminate\Database\Migrations\Migration;
use NPA\ACPMS\Models\Context;

class InsertToContextTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
//        Context::insert([
//            ['name' => 'exception', 'role_id' => 1],
//
//            ['name' => 'exception', 'role_id' => 2],
//            ['name' => 'info-edit', 'role_id' => 2],
//            ['name' => 'info-add', 'role_id' => 2],
//            ['name' => 'edit', 'role_id' => 2],
//            ['name' => 'add', 'role_id' => 2],
//
//            ['name' => 'info-edit', 'role_id' => 3],
//            ['name' => 'info-add', 'role_id' => 3],
//            ['name' => 'edit', 'role_id' => 3],
//            ['name' => 'add', 'role_id' => 3],
//
//            ['name' => 'info-edit', 'role_id' => 4],
//            ['name' => 'info-add', 'role_id' => 4],
//            ['name' => 'edit', 'role_id' => 4],
//            ['name' => 'add', 'role_id' => 4],
//
//            ['name' => 'exception', 'role_id' => 5],
//            ['name' => 'info-edit', 'role_id' => 5],
//            ['name' => 'info-add', 'role_id' => 5],
//            ['name' => 'edit', 'role_id' => 5],
//            ['name' => 'add', 'role_id' => 5],
//
//
//            ['name' => 'info-edit', 'role_id' => 6],
//            ['name' => 'info-add', 'role_id' => 6],
//            ['name' => 'edit', 'role_id' => 6],
//            ['name' => 'add', 'role_id' => 6],
//
//            ['name' => 'info-edit', 'role_id' => 7],
//            ['name' => 'info-add', 'role_id' => 7],
//            ['name' => 'edit', 'role_id' => 7],
//            ['name' => 'add', 'role_id' => 7],
//        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('contexts')->truncate();
    }
}
