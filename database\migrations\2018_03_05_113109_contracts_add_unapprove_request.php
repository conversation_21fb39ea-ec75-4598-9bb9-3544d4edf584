<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ContractsAddUnapproveRequest extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropColumn('has_requested_to_unpublish');
        });

        Schema::table('contracts', function (Blueprint $table) {
            $table->boolean('has_requested_to_unpublish')
                ->after('is_published')
                ->default(false);
        });

        Schema::table('contracts', function (Blueprint $table) {
            $table->boolean('has_requested_to_unapprove')
                ->after('is_published')
                ->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropColumn('has_requested_to_unapprove');
        });
    }
}
