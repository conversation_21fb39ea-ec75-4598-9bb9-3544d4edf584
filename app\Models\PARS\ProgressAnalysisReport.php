<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ProgressAnalysisReport extends Model
{
    public static $fields = [
        'serial_number',
        'contract_id',
        'confirmation_timestamp',
        'generation_timestamp',
        'month',
        'year',
        'is_auto_saved',
        'creator_user_id',
        'user_full_name',
        'is_confirmed',
        'is_approved',
    ];

    public function pdf_report()
    {
        return $this->hasOne('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    protected $guarded = ['id'];

    public function general_information()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsGeneralInformation', 'progress_analysis_reports_id');
    }

    public function performance_security()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsContractPerformanceSecurity', 'p_a_r_id');
    }

    public function values()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsContractValue', 'p_a_r_id');
    }

    public function durations()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsContractDuration', 'p_a_r_id');
    }

    public function progress()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsContractProgress', 'p_a_r_id');
    }

    public function advance_payment_guaranty()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsAdvancePaymentGuarantie', 'p_a_r_id');
    }

    public function time_amendments()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsTimeAmendment', 'p_a_r_id');
    }

    public function cost_amendments()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsCostAmendment', 'p_a_r_id');
    }

    public function time_and_cost_amendments()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsCostAndTimeAmendment', 'p_a_r_id');
    }

    public function condition_amendments()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsConditionAmendment', 'p_a_r_id');
    }


    public function challenges_and_remarks()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsChallengesAndRemark', 'p_a_r_id');
    }

    public function charts()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsChart', 'p_a_r_id');
    }

    public function check_dbs()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsCheckDb', 'p_a_r_id');
    }

    public function defect_liabilities()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsDefectLiability', 'p_a_r_id');
    }

    public function delay_penalties()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsDelayPenaltie', 'p_a_r_id');
    }

    public function progress_stops()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsContractProgressStop', 'p_a_r_id');
    }

    public function remarks()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsRemark', 'p_a_r_id');
    }
}
