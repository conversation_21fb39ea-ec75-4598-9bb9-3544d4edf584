<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusLog extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'status_id',
        'date',
        'remarks',
        'contract_id',
        'contract_status_id'
    ];

    public function contract_statuses()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract_status_cancelled_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusCancelledLog', 'contract_status_log_id');
    }

    public function contract_status_suspended_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusSuspendedLog', 'contract_status_log_id');
    }

    public function contract_status_not_signed_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusNotSignedLog', 'contract_status_log_id');
    }

    public function contractor_performance_assessment_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractorPerformanceAssessmentLog', 'contract_status_log_id');
    }

    public function contract_retention_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionLog', 'contract_status_log_id');
    }
}
