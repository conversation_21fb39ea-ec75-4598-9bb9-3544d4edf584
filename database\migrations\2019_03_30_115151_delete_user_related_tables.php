<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DeleteUserRelatedTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('user_award_authorities');
        Schema::dropIfExists('user_contract_managers');
        Schema::dropIfExists('user_cpm_managers');
        Schema::dropIfExists('user_observers');
        Schema::dropIfExists('user_specialists');
        Schema::dropIfExists('user_companies');
        Schema::dropIfExists('recover_password_tokens');
        Schema::dropIfExists('users');
        Schema::dropIfExists('contexts');
        Schema::dropIfExists('roles');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
