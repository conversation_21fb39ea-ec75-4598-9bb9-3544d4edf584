<?php

namespace NPA\ACPMS\Http\Controllers\ChallengesAndRemarks;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use NPA\ACPMS\Helpers\Calculate;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\AmendmentInContractTerms;
use NPA\ACPMS\Models\ChallengesAndRemarks;
use NPA\ACPMS\Models\Company;
use NPA\ACPMS\Models\CompanyGeneralInformation;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractApproval;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\DomesticContractExecutionLocation;
use NPA\ACPMS\Models\NpaOtherComments;
use NPA\ACPMS\Models\ProcurementEntityContactPerson;
use NPA\ACPMS\Models\ProvisionalSumAndContingency;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;
use Throwable;


class NpaOtherCommentsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $npaOtherComments = DB::table('npa_other_comments')
            ->leftJoin('contracts', 'npa_other_comments.contract_id', '=', 'contracts.id')
            ->select('npa_other_comments.*', 'contracts.is_published as is_generally_published')
            ->where('npa_other_comments.contract_id', '=', $contractId)->get();
        if (!$npaOtherComments) {
            return response()->json([], 404);
        }

        return response()->json($npaOtherComments);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $npaOtherComment = NpaOtherComments::create(array_only($request->all(), NpaOtherComments::$fields));
            return response()->json([], 201, [
                'location' => $npaOtherComment['id']
            ]);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return response()->json($this->loadOtherComments($id));
    }

    /**
     * @param $id
     * @return array|\Illuminate\Http\JsonResponse
     */
    private function loadOtherComments($id)
    {

        try {
            $npaOtherComments = NpaOtherComments::where('id', $id)->first();
            $contractId = $npaOtherComments->contract_id;
            $contract = Contract::where('id', $contractId)->first();
            $contract_detail = ContractDetails::where('contract_id', $contractId)->first();

            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [], [
                'ids' => [$contract->project_id]
            ]);

            if ($request->status_code >= 300) {
                return Error::composeResponse(new Exception($request->body));
            }
            $request = json_decode($request->body, true);

            if ($request === []) {
                return [
                    'npa_other_comments' => $npaOtherComments,
                    'general_information' => $contract,
                    'contract_detail' => $contract_detail,
                ];
            }
            $PPMSData['name_da'] = $request && $request[0] ? $request[0]['project_name_da'] : '';
            $PPMSData['procurement_type'] = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType',
                $request[0]['procurement_type_id']
            );

            $PPMSData['procurement_entity'] = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity',
                $request[0]['procurement_entity_id']
            );

            $PPMSData['contract_type'] = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractType',
                $request[0]['contract_type_id']
            );

            $PPMSData['procurement_method'] = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementMethod',
                $request[0]['procurement_method_id']
            );

            $PPMSData['budget_code'] = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/code',
                $request[0]['budget_code_id']
            );


            $PPMSData['donors'] = $request[0]['v2_project_donors'];
            if ($PPMSData['donors']) {
                $donors = DropDowns::getAllValues(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sourceDonor'
                );

                foreach ($PPMSData['donors'] as $k => $v) {
                    $PPMSData['donors'][$k] = DropDowns::get(null, $v['donor_id'], $donors);
                }
            }

            $PPMSData['description_da'] = $request[0]['description_da'];
            $locations = DomesticContractExecutionLocation::where('contract_id', $contractId)->get();

            if (!$locations) {
                $locations = [];
            }

            $climateSituations = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/climateSituation');
            $districts = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/district');
            $provinces = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/province');
            $zones = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/zone');

            foreach ($locations as $l) {
                $l['climate_situation'] = DropDowns::get(null, $l['climate_situation_id'], $climateSituations);
                $l['district'] = DropDowns::get(null, $l['district_id'], $districts);
                $l['province'] = DropDowns::get(null, $l['district']['province_id'], $provinces);
                $l['zone'] = DropDowns::get(null, $l['province']['zone_id'], $zones);
            }

            $contacts = ProcurementEntityContactPerson::where('contract_id', $contractId)->get();
            if (!$contacts) {
                $contacts = [];
            }


            $contract_progress = ContractProgress::where('contract_id', $contractId)->first();
            $contract_progress['actual_start_date_miladi'] = ($contract_progress['actual_start_date']);
            $contract_progress['actual_start_date'] = Date::gregorianToJalali($contract_progress && $contract_progress['actual_start_date'] ? $contract_progress['actual_start_date'] : null);

            $provisional_sum_and_contingency = ProvisionalSumAndContingency::where('contract_detail_id', $contractId)->first();

            $contract_approvals = ContractApproval::where('id', $contractId)->first();

            $amendments = Amendment::where('contract_id', $contractId)->get();
            $timeAmendments = [];
            $costAmendments = [];
            $termsAmendments = [];
            $grandDuration = 0;
            $amendment_end_date = '';


            if ($amendments) {
                $amendments = $amendments->toArray();
                $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
                foreach ($amendments as $vAmendment) {
                    $typeSlug = null;
                    foreach ($amendmentTypes as $vType) {
                        if ($vAmendment['type_id'] == $vType['id']) {
                            $vAmendment['type_slug'] = $vType['slug'];
                            $vAmendment['type_name_da'] = $vType['name_da'];
                            $typeSlug = $vType['slug'];
                            continue;
                        }
                    }
                    if ($typeSlug === 'time-amendment' || $typeSlug === 'time-and-cost-amendment') {
                        if ($typeSlug === 'time-amendment') {
                            $timeAmendment = TimeAmendment::where('amendment_id', $vAmendment['id'])->first();
                            $timeAmendment['amended_duration'] =
                                Date::getDifferent($timeAmendment['replanning_start_date'], $timeAmendment['amended_end_date']);
                            $grandDuration += $timeAmendment['amended_duration'];
                            $timeAmendment['amended_end_date_miladi'] = $timeAmendment['amended_end_date'];
                            $amendment_end_date = $timeAmendment['amended_end_date'];
                            $timeAmendment['amended_end_date'] = Date::gregorianToJalali($timeAmendment['amended_end_date']);
                        } else {
                            $timeAmendment = TimeAndCostAmendment::where('amendment_id', $vAmendment['id'])->first();
                            $timeAmendment['amended_duration'] =
                                Date::getDifferent($timeAmendment['replanning_start_date'], $timeAmendment['amended_end_date']);
                            $grandDuration += $timeAmendment['amended_duration'];
                            $timeAmendment['amended_end_date_miladi'] = $timeAmendment['amended_end_date'];
                            $amendment_end_date = $timeAmendment['amended_end_date'];
                            $timeAmendment['amended_end_date'] = Date::gregorianToJalali($timeAmendment['amended_end_date']);
                        }
                        $timeAmendments[] = array_merge($vAmendment, $timeAmendment->toArray());
                    } else if ($typeSlug === 'cost-amendment' || $typeSlug === 'time-and-cost-amendment') {
                        if ($typeSlug === 'cost-amendment') {
                            $costAmendment = CostAmendment::where('amendment_id', $vAmendment['id'])->first();
                            $costAmendment['amended_end_date_miladi'] = $costAmendment['amended_end_date'];
                            $costAmendment['amended_end_date'] = Date::gregorianToJalali($costAmendment['amended_end_date']);
                        } else {
                            $costAmendment = TimeAndCostAmendment::where('amendment_id', $vAmendment['id'])->first();
                            $costAmendment['amended_end_date_miladi'] = $costAmendment['amended_end_date'];
                            $amendment_end_date = $costAmendment['amended_end_date'];
                            $costAmendment['amended_end_date'] = Date::gregorianToJalali($costAmendment['amended_end_date']);
                        }
                        $costAmendments[] = array_merge($vAmendment, $costAmendment->toArray());
                    } else if ($typeSlug === 'amendment-in-contract-conditions') {
                        $termAmendment = AmendmentInContractTerms::where('amendment_id', $vAmendment['id'])->first();
                        $termAmendment['amended_end_date_miladi'] = $termAmendment['amended_end_date'];
                        $termAmendment['amended_end_date'] = Date::gregorianToJalali($termAmendment['amended_end_date']);
                        $termsAmendments[] = array_merge($vAmendment, $termAmendment->toArray());
                    }
                }
            }

            $contractProgress = ContractProgress::where('id', $contractId)->first();
            $contractStatuses = ContractStatus::where('contract_id', $contractId)->first();
            $contractStatuses['date_miladi'] = $contractStatuses['date'];
            $contractStatuses['date'] = Date::gregorianToJalali($contractStatuses['date']);
            $contractStatuses['status'] = DropDowns::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus', $contractStatuses['status_id']);


            $actualPayments = DB::select('select 
                                        sum(amount * if(c.exchange_rate, c.exchange_rate, 1)) as total_payment_amount,
                                        sum(physical_progress_percentage) as total_progress_percentage
                                        from contract_progress_payments as cpp
                                        join contracts as c
                                          on c.id = cpp.contract_id
                                        where contract_id= :contract_id', ['contract_id' => $contractId]);
            $contractProgressPaymentAfterDueDate = DB::select('select 
                                        sum(amount * if(c.exchange_rate, c.exchange_rate, 1)) as total_amount
                                        from contract_progress_payment_after_due_dates as cppadd
                                        join contracts as c
                                          on c.id = cppadd.contract_id
                                        where contract_id= :contract_id ', ['contract_id' => $contractId]);
            $actualPayments[0]->total_payment_amount += $contractProgressPaymentAfterDueDate[0]->total_amount;

            $CompanyGeneralInformationId = CompanyGeneralInformation::where('contract_id', $contractId)->first()->toArray();

            $remoteRequest = null;
            if ($CompanyGeneralInformationId) {
                if ($CompanyGeneralInformationId['is_joint_venture']) {
                    $companiesLicenseNumber = Company::select('license_number')
                        ->where('company_general_information_id', $CompanyGeneralInformationId['id'])
                        ->where('joint_venture_company_role_id', 1)
                        ->get()->toArray();
                } else {
                    $companiesLicenseNumber = Company::select('license_number')
                        ->where('company_general_information_id', $CompanyGeneralInformationId['id'])
                        ->get()->toArray();
                }
                $companyLicenseNumber = $companiesLicenseNumber && $companiesLicenseNumber[0] ? $companiesLicenseNumber[0]['license_number'] : null;
                if (!is_null($companyLicenseNumber)) {
                    $remoteRequest = DropDowns::getWithCondition(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . '/api/specific/company/', 'licence_number', $companyLicenseNumber);
                }
            }

            $remoteRequest = $remoteRequest ? $remoteRequest : '';

            $challengesAndRemarks = ChallengesAndRemarks::where('contract_id', $contractId)->get();
            if ($challengesAndRemarks) {
                $ids = [];
                foreach ($challengesAndRemarks as $challengesAndRemark) {
                    array_push($ids, $challengesAndRemark['creator_id']);
                }
                $roleList = Helper::getRolesByIds($ids);
                foreach ($challengesAndRemarks as $challengesAndRemark) {
                    foreach ($roleList as $role) {
                        if ($role['id'] == $challengesAndRemark['creator_id']) {
                            $challengesAndRemark['creator'] = $role['name'];
                        }
                    }
                }
            }

            // TODO calculate the value
            $grandTotalValue = Calculate::totalAmount($contractId);
            $grandDuration += Date::getDifferent($contract_detail['planned_start_date'], $contract_detail['planned_end_date']);
            $exchange_rate = Contract::select('exchange_rate')->where('id', $contractId)->first();
            $exchange_rate = is_null($exchange_rate->exchange_rate) ? 1 : $exchange_rate->exchange_rate;

            return [
                'company_name' => $remoteRequest,
                'general_information' => $contract,
                'npa_other_comments' => $npaOtherComments,
                'ppms_data' => $PPMSData,
                'contract_execution_location' => $locations,
                'contract_progress' => $contract_progress,
                'contract_progress_payment' => $actualPayments,
                'p_e_contact_person' => $contacts,
                'contract_detail' => $contract_detail,
                'agreement_signature_date' => Date::gregorianToJalali($contract_detail['agreement_signature_date']),
                'planned_start_date' => Date::gregorianToJalali($contract_detail['planned_start_date']),
                'planned_end_date' => Date::gregorianToJalali($contract_detail['planned_end_date']),
                'contract_approvals' => $contract_approvals,
                'contractProgress' => $contractProgress,
                'contract_statuses' => $contractStatuses,
                'amendments' => $amendments,
                'time_amendments' => $timeAmendments,
                'cost_amendments' => $costAmendments,
                'terms_amendments' => $termsAmendments,
                'is_above_threshold' => $contract->is_above_threshold,
                'signed_date' => Date::gregorianToJalali($contract_detail['agreement_signature_date']),
                'first_plan_duration' => Date::getDifferent($contract_detail['planned_start_date'], $contract_detail['planned_end_date']),
                'provisional_sum_and_contingency' => $provisional_sum_and_contingency,
                'grand_total_value' => $grandTotalValue,
                'grand_duration' => $grandDuration,
                'challenges_and_remarks' => $challengesAndRemarks,
                'exchange_rate' => $exchange_rate,
                'amendment_end_date' => $amendment_end_date,
            ];
        } catch
        (Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $query = $request->all();
        try {
            $rowsCount = NpaOtherComments::where('id', $id)->update(array_only($query, NpaOtherComments::$fields));
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response()->json('ok', 201);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function takeAction(Request $request)
    {
        $id = $request->input('id');
        $confirmType = $request->input('type');
        try {
            $npaOtherComments = NpaOtherComments::find($id);
            $npaOtherComments->$confirmType = true;
            $npaOtherComments->save();
            if ($npaOtherComments) {
                return response()->json([]);
            }
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return void
     */
    public function destroy($id)
    {
        //
    }

    public function downloadDocx(Request $request)
    {
        $request = Http::post(
            config('custom.CUSTOM_API_INTERNAL_RM_BASE_URL') . 'api/report',
            [],
            [
                'template' => [
                    'shortid' => config('custom.CUSTOM_RM_SHORT_ID_DOCX')
                ],
                'data' => [
                    'htmlContents' => View::make('reports/contract-progress-analysis-report',
                        $this->loadOtherComments($request->get('id'))
                    )->render()
                ]
            ]
        );
        if ($request->status_code > 300) {

            //TODO: Handle
            throw new \Exception($request->body);
        }
        return response($request->body, 200, [
            'Content-Type' => $request->headers['content-type']
        ]);
    }

    public function downloadPdf(Request $request)
    {
        $requestToJsReport = Http::post(
            config('custom.CUSTOM_API_INTERNAL_RM_BASE_URL') . 'api/report',
            [],
            [
                'template' => [
                    'shortid' => config('custom.CUSTOM_RM_SHORT_ID_PDF')
                ],
                'data' => [
                    'htmlContents' => View::make('reports/contract-progress-analysis-report',
                        $this->loadOtherComments($request->get('id'))
                    )->render()
                ]
            ]
        );
        if ($requestToJsReport->status_code > 300) {
            //TODO: Handle
            throw new \Exception($requestToJsReport->body);
        }
        return response($requestToJsReport->body, 200, [
            'Content-Type' => $request->header('responsetype')
        ]);
    }

    /**
     * @param $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLastRecord($contractId)
    {
        $last_record = DB::table('npa_other_comments')
            ->where('contract_id', $contractId)
            ->latest()
            ->first();
        return response()->json($last_record);
    }
}
