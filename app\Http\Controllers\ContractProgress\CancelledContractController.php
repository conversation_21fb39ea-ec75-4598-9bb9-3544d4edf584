<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractStatusCancelled;
use NPA\ACPMS\Models\ContractStatusCancelledLog;

class CancelledContractController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        if ($data['is_contractor_deprived'] === NULL) {
            $data['is_contractor_deprived'] = 0;
        }
        try {
            $contractCancelled = ContractStatusCancelled::create(array_only($data, ContractStatusCancelled::$fields));
            if ($contractCancelled) {
                return response()->json([], 201);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $cancelledContracts = ContractStatusCancelled::where('contract_status_id', $id)->first();
            return response()->json($cancelledContracts);
        } catch (\Throwable $t) {
            return Error::composeResponse(response(), $t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        if ($data['is_contractor_deprived'] === NULL) {
            $data['is_contractor_deprived'] = 0;
        }
        try {
            DB::beginTransaction();
            ContractStatusCancelled::where('id', $id)->update(array_only($data, ContractStatusCancelled::$fields));
            DB::commit();
            return response()->json([], 201);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
//        try {
//
//
//        } catch (\Throwable $t) {
//            return Error::composeResponse($t);
//        }
    }
}
