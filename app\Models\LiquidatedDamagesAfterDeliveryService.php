<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class LiquidatedDamagesAfterDeliveryService extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'contract_id',
        'is_liquidated_damages_period_applicable',
        'is_after_delivery_service_period_applicable',
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
