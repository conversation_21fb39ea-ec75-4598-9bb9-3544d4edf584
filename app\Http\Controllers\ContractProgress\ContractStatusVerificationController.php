<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\AttachmentLog;
use NPA\ACPMS\Models\ContractorPerformanceAssessment;
use NPA\ACPMS\Models\ContractorPerformanceAssessmentLog;
use NPA\ACPMS\Models\ContractRetention;
use NPA\ACPMS\Models\ContractRetentionApplicable;
use NPA\ACPMS\Models\ContractRetentionApplicableLog;
use NPA\ACPMS\Models\ContractRetentionLog;
use NPA\ACPMS\Models\ContractRetentionNotReturned;
use NPA\ACPMS\Models\ContractRetentionNotReturnedLog;
use NPA\ACPMS\Models\ContractRetentionReturned;
use NPA\ACPMS\Models\ContractRetentionReturnedLog;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\ContractStatusCancelled;
use NPA\ACPMS\Models\ContractStatusCancelledLog;
use NPA\ACPMS\Models\ContractStatusLog;
use NPA\ACPMS\Models\ContractStatusNotSigned;
use NPA\ACPMS\Models\ContractStatusNotSignedLog;
use NPA\ACPMS\Models\ContractStatusSuspend;
use NPA\ACPMS\Models\ContractStatusSuspendedLog;
use NPA\ACPMS\Models\ContractStatusVerification;
use NPA\ACPMS\Models\ContractStatusVerificationLog;

class ContractStatusVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_status_id'] = $data['parent_id'];
        unset($data['parent_id']);
        unset($data['has_requested_to_unpublish']);
        try {
            $contractStatusConfirmation = ContractStatusVerification::Create(array_only($data, ContractStatusVerification::$fields));
            return response()->json($contractStatusConfirmation, 201, [
                'location' => $contractStatusConfirmation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractStatusVerification $contractStatusVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractStatusId)
    {
        $contractStatusVerification = ContractStatusVerification::where('contract_status_id', $contractStatusId)->first();
        return response()->json($contractStatusVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractStatusVerification $contractStatusVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractStatusVerification $contractStatusVerification)
    {

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractStatusVerification $contractStatusVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $data = $request->all();
        $data['contract_status_id'] = $data['parent_id'];
        unset($data['parent_id']);
        unset($data['has_requested_to_unpublish']);
        $contractStatusConfirmationOrVerification = null;
        try {
            DB::beginTransaction();
            if (isset($data['is_confirmed']) && $data['is_approved'] && !$data['is_published']) {
                $id = $data['contract_status_id'];
                $contractStatusPreviews = ContractStatus::where('id', $id)->first();
                if ($contractStatusPreviews) {
                    $contractStatusPreviews = $contractStatusPreviews->toArray();
                    $contractStatusPreviews['contract_status_id'] = $id;
                    $contractId = $contractStatusPreviews['contract_id'];
                    $contractStatusLogCreated = ContractStatusLog::create(array_only($contractStatusPreviews, ContractStatusLog::$fields));
                    $attachments = Attachment::where('foreign_key', $contractStatusPreviews['id'])
                        ->where('table_name', 'contract_statuses')
                        ->get();
                    foreach ($attachments as $attachment) {
                        $attachment['log_foreign_key'] = $contractStatusLogCreated['id'];
                        $attachment['log_table_name'] = 'contract_status_logs';
                        $attachment = $attachment->toArray();
                        AttachmentLog::create(array_only($attachment, AttachmentLog::$fields));
                    }
                }
                //Send request to CDM
                $queryString = 'search[id][match]=full&search[id][value]=' . $contractStatusPreviews['status_id'];
                $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus?' . $queryString, []);
                if ($request->status_code > 400) {
                    throw new Exception($request->body);
                }
                $slug = json_decode($request->body, true)[0]['slug'];
                //end
                if (isset($slug) && $slug === 'suspended') {
                    $contractStatusSuspendPreviews = ContractStatusSuspend::where('contract_status_id', $id)->first();
                    if ($contractStatusSuspendPreviews) {
                        $contractStatusSuspendPreviews = $contractStatusSuspendPreviews->toArray();
                        $contractStatusSuspendPreviews['contract_status_log_id'] = $contractStatusLogCreated['id'];
                        $contractStatusSuspendPreviews['contract_status_suspended_id'] = $contractStatusSuspendPreviews['id'];
                        ContractStatusSuspendedLog::create(array_only($contractStatusSuspendPreviews, ContractStatusSuspendedLog::$fields));
                    }
                } else if (isset($slug) && $slug === 'not-signed') {
                    $contractStatusNotSignedPreviews = ContractStatusNotSigned::where('contract_status_id', $id)->first();
                    if ($contractStatusNotSignedPreviews) {
                        $contractStatusNotSignedPreviews = $contractStatusNotSignedPreviews->toArray();
                        $contractStatusNotSignedPreviews['contract_status_log_id'] = $contractStatusLogCreated['id'];
                        $contractStatusNotSignedPreviews['contract_status_not_signed_id'] = $contractStatusNotSignedPreviews['id'];
                        ContractStatusNotSignedLog::create(array_only($contractStatusNotSignedPreviews, ContractStatusNotSignedLog::$fields));
                    }
                } else if (isset($slug) && $slug === 'cancelled') {
                    $cancelledContractsPreviews = ContractStatusCancelled::where('contract_status_id', $id)->first();
                    if ($cancelledContractsPreviews) {
                        $cancelledContractsPreviews = $cancelledContractsPreviews->toArray();
                        $cancelledContractsPreviews['contract_status_log_id'] = $contractStatusLogCreated['id'];
                        $cancelledContractsPreviews['contract_status_cancelled_id'] = $cancelledContractsPreviews['id'];
                        ContractStatusCancelledLog::create(array_only($cancelledContractsPreviews, ContractStatusCancelledLog::$fields));
                    }
                }
                if (isset($slug) && ($slug === 'cancelled' || $slug === 'contract-close-out')) {
                    $contractorPerformanceAssessmentPreviews = ContractorPerformanceAssessment::where('contract_status_id', $id)->first();
                    if ($contractorPerformanceAssessmentPreviews) {
                        $contractorPerformanceAssessmentPreviews = $contractorPerformanceAssessmentPreviews->toArray();
                        $contractorPerformanceAssessmentPreviews['contract_status_log_id'] = $contractStatusLogCreated['id'];
                        $contractorPerformanceAssessmentPreviews['contractor_performance_assessment_id'] = $contractorPerformanceAssessmentPreviews['id'];
                        ContractorPerformanceAssessmentLog::create(array_only($contractorPerformanceAssessmentPreviews, ContractorPerformanceAssessmentLog::$fields));
                    }
                }
                /*create contract retention log*/
                $contractRetentionPreviews = ContractRetention::where('contract_id', $contractId)->first();
                if ($contractRetentionPreviews) {
                    $contractRetentionPreviews['contract_retention_id'] = $contractRetentionPreviews['id'];
                    $contractRetentionPreviews['contract_status_log_id'] = $contractStatusLogCreated['id'];
                    $contractRetentionPreviews = $contractRetentionPreviews->toArray();
                    $contractRetentionLogCreated = ContractRetentionLog::create(array_only($contractRetentionPreviews, ContractRetentionLog::$fields));
                    if ($contractRetentionPreviews['is_applicable']) {
                        $contractRetentionApplicablePreviews = ContractRetentionApplicable::where('contract_retention_id', $contractRetentionPreviews['id'])
                            ->first()
                            ->toArray();
                        $contractRetentionApplicablePreviews['contract_retention_log_id'] = $contractRetentionLogCreated['id'];
                        $contractRetentionApplicablePreviews['contract_retention_applicable_id'] = $contractRetentionApplicablePreviews['id'];
                        ContractRetentionApplicableLog::create(array_only($contractRetentionApplicablePreviews, ContractRetentionApplicableLog::$fields));
                        if ($contractRetentionApplicablePreviews['is_returned']) {
                            $contractRetentionReturnedPreviews = ContractRetentionReturned::where('contract_retention_applicable_id', $contractRetentionApplicablePreviews['id'])
                                ->first()
                                ->toArray();
                            $contractRetentionReturnedPreviews['contract_retention_log_id'] = $contractRetentionLogCreated['id'];
                            $contractRetentionReturnedPreviews['contract_retention_returned_id'] = $contractRetentionReturnedPreviews['id'];
                            $contractRetentionReturnedLogCreated = ContractRetentionReturnedLog::create(array_only($contractRetentionReturnedPreviews, ContractRetentionReturnedLog::$fields));
                            $attachments = Attachment::where('foreign_key', $contractRetentionReturnedPreviews['id'])
                                ->where('table_name', 'contract_retention_returneds')
                                ->get();
                            foreach ($attachments as $attachment) {
                                $attachment['log_foreign_key'] = $contractRetentionReturnedLogCreated['id'];
                                $attachment['log_table_name'] = 'contract_retention_returned_logs';
                                $attachment = $attachment->toArray();
                                AttachmentLog::create(array_only($attachment, AttachmentLog::$fields));
                            }
                        } else {
                            $contractRetentionNotReturnedPreviews = ContractRetentionNotReturned::where('contract_retention_applicable_id', $contractRetentionApplicablePreviews['id'])
                                ->first()
                                ->toArray();
                            $contractRetentionNotReturnedPreviews['contract_retention_log_id'] = $contractRetentionLogCreated['id'];
                            $contractRetentionNotReturnedPreviews['contract_retention_not_returned_id'] = $contractRetentionNotReturnedPreviews['id'];
                            ContractRetentionNotReturnedLog::create(array_only($contractRetentionNotReturnedPreviews, ContractRetentionNotReturnedLog::$fields));
                        }
                    }
                }
                $contractStatusVerificationPreviews = ContractStatusVerification::where('contract_status_id', $data['contract_status_id'])->first();
                if ($contractStatusVerificationPreviews) {
                    $contractStatusVerificationPreviews['contract_status_verification_id'] = $contractStatusVerificationPreviews['id'];
                    $contractStatusVerificationPreviews = $contractStatusVerificationPreviews->toArray();
                    unset($contractStatusVerificationPreviews['id']);
                    ContractStatusVerificationLog::create(array_only($contractStatusVerificationPreviews, ContractStatusVerificationLog::$fields));
                }

                unset($data['id']);
                $currentDate = Carbon::now()->toDateTimeString();
                $data['published_date'] = $currentDate;
                $contractStatusConfirmationOrVerification = ContractStatusVerification::where('contract_status_id', $data['contract_status_id'])->update(array_only($data, ContractStatusVerification::$fields));
            } else {
                if ($data['is_published']) {
                    ContractStatusVerificationLog::where('contract_status_id', $data['contract_status_id'])->update(array_only($data, ContractStatusVerificationLog::$fields));
                    $data['is_published'] = false;
                    $data['is_approved'] = false;
                    $data['is_confirmed'] = true;
                    ContractStatusVerification::where('contract_status_id', $data['contract_status_id'])->update(array_only($data, ContractStatusVerification::$fields));
                    $data['is_confirmed'] = false;
                    ContractStatusVerification::where('contract_status_id', $data['contract_status_id'])->update(array_only($data, ContractStatusVerification::$fields));
                } else {
                    unset($data['id']);
                    $contractStatusConfirmationOrVerification = ContractStatusVerification::where('contract_status_id', $data['contract_status_id'])->update(array_only($data, ContractStatusVerification::$fields));
                }
            }
            DB::commit();
            return response()->json([], 204, [
                'location' => $contractStatusConfirmationOrVerification
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractStatusVerification $contractStatusVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractStatusVerification $contractStatusVerification)
    {
        //
    }
}
