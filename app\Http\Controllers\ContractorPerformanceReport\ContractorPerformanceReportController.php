<?php

namespace NPA\ACPMS\Http\Controllers\ContractorPerformanceReport;


use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Calculate;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Company;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractStatusLog;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ContractorPerformanceReportController extends Controller
{

    public function show($id)
    {
        try {
            if (!$id) {
                return Error::composeResponse(new \Exception('Contract ID is required'));
            }
            $contractPerformanceReport = self::aggregateReportData($id);
            return response()->json($contractPerformanceReport);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public static function aggregateReportData($id)
    {
        $contract = DB::select('
                select 
                    c.id,
                    c.contract_number,
                    c.project_id,
                    c.procurement_entity_id,
                    c.exchange_rate,
                    c.is_published,
                    c.currency_id,
                    cgi.id as company_general_information_id,
                    round(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1), 3) as actual_value,
                    ca.award_date,
                    cd.agreement_signature_date
                from contracts as c
                left join company_general_informations as cgi
                  on c.id = cgi.contract_id
                left join contract_details as cd
                  on c.id = cd.contract_id 
                left join contract_approvals as ca
                  on c.id = ca.contract_id   
                where c.id = ' . $id . ' 
                  and c.is_published is true;
        ');
        if (!isset($contract[0])) {
            throw new \Exception('|||NPA-ACPMS-0010|||');
        }
        $contractDetails = ContractDetails::where('contract_id', $id)->first();
        $contractProgress = ContractProgress::where('contract_id', $id)->first();
        $contractLastStatus = self::getLastStatus($id);
        $APPMSRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info'
            , [],
            ['ids' => [$contract[0]->project_id]]);
        if ($APPMSRequest->status_code > 400) {
            throw new \Exception($APPMSRequest->body);
        }
        $projects = json_decode($APPMSRequest->body, true);
        $procurement_entity = DropDowns::getById(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity',
            $contract[0]->procurement_entity_id)['name_da'];
        $currency = '';
        if (isset($contract[0]->currency_id)) {
            $currency = DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency',
                $contract[0]->currency_id)['name_da'];
        }
        if (count($projects) === 1) {
            $awarded_contract_days_count = Carbon::parse($contract[0]->award_date)->diffInDays(Carbon::parse($contract[0]->agreement_signature_date));
            $awarded_contract = '';

            if ($awarded_contract_days_count <= 10) {
                $awarded_contract = 'اعلی';
            } elseif ($awarded_contract_days_count > 10 && $awarded_contract_days_count <= 20) {
                $awarded_contract = 'عالی';
            } elseif ($awarded_contract_days_count > 20 && $awarded_contract_days_count <= 28) {
                $awarded_contract = 'عادی';
            } elseif ($awarded_contract_days_count > 28 && $awarded_contract_days_count <= 40) {
                $awarded_contract = 'ضعیف';
            } else {
                $awarded_contract = 'غیر قابل قبول';
            }
            $physical_work_start_days_count = Carbon::parse($contract[0]->agreement_signature_date)->diffInDays(Carbon::parse($contractProgress['actual_start_date']));
            $physical_work_start = '';
            if ($physical_work_start_days_count <= 5) {
                $physical_work_start = 'اعلی';
            } elseif ($physical_work_start_days_count > 5 && $physical_work_start_days_count <= 15) {
                $physical_work_start = 'عالی';
            } elseif ($physical_work_start_days_count > 15 && $physical_work_start_days_count <= 28) {
                $physical_work_start = 'عادی';
            } elseif ($physical_work_start_days_count > 28 && $physical_work_start_days_count <= 40) {
                $physical_work_start = 'ضعیف';
            } else {
                $physical_work_start = 'غیر قابل قبول';
            }
            $total_contract_value_with_amendments = Calculate::totalAmount($id);
            $actual_value = $contract[0]->actual_value;
            $difference_of_total_with_actual_value = $total_contract_value_with_amendments - $actual_value;
            $percentage_of_total_and_actual_value = abs(round((($difference_of_total_with_actual_value * 100) / $actual_value), 2));
            $cost_level_amendment = '';
            if ($percentage_of_total_and_actual_value < 2) {
                $cost_level_amendment = 'اعلی';
            } elseif ($percentage_of_total_and_actual_value >= 2 && $percentage_of_total_and_actual_value <= 5) {
                $cost_level_amendment = 'عالی';
            } elseif ($percentage_of_total_and_actual_value > 5 && $percentage_of_total_and_actual_value <= 10) {
                $cost_level_amendment = 'عادی';
            } elseif ($percentage_of_total_and_actual_value > 10 && $percentage_of_total_and_actual_value <= 25) {
                $cost_level_amendment = 'ضعیف';
            } else {
                $cost_level_amendment = 'غیر قابل قبول';
            }
            $project = $projects[0];
            $data['general_information'] = [
                'contract_id' => $contract[0]->id,
                'contract_name' => $project['project_name_da'],
                'contract_number' => $contract[0]->contract_number,
                'procurement_entity' => $procurement_entity,
                'currency' => $currency,
                'actual_value' => $contract[0]->actual_value,
                'contract_donor' => self::getDonorList($project),
                'vendors' => self::getVendorList($contract[0]->company_general_information_id),
                'total_contract_value_with_amendments' => Calculate::totalAmount($id),
                'approval_date' => $contract[0]->award_date,
                'contract_award_date' => $contract[0]->agreement_signature_date,
                'durations' => self::prepareContractDurations($contractDetails, $contractProgress['actual_start_date'], $contractLastStatus),
                'progress' => self::prepareContractProgress($id, $contractLastStatus),
                'awarded_contract' => $awarded_contract,
                'physical_work_start' => $physical_work_start,
                'cost_level_amendment' => $cost_level_amendment
            ];
            return $data;
        } else {
            throw new \Exception('project not found');
        }
    }

    public static function getDonorList($project)
    {
        $list = [];
        foreach ($project['v2_project_donors'] as $project_donor) {
            $list[] = array('contract_donor' => DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sourceDonor',
                $project_donor['donor_id'])['name_da']);
        }
        return $list;
    }

    public static function getVendorList($companyGeneralInformation_id)
    {
        $list = [];
        if (!isset($companyGeneralInformation_id)) {
            return $list;
        }
        $companies = Company::select('joint_venture_company_role_id', 'license_number')->where('company_general_information_id', $companyGeneralInformation_id)->get();
        if (count($companies) < 1) {
            return $list;
        }
        $queryString = '?search[license_number][match]=in&';
        foreach ($companies as $company) {
            $queryString = $queryString . 'search[license_number][value][]=' . $company['license_number'] . '&';
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
        $remoteRequestData = json_decode($remoteRequest->body, true);
        foreach ($companies as $company) {
            foreach ($remoteRequestData as $element) {
                if ($company['license_number'] == $element['license_number']) {
                    $company['vendor_name'] = $element['full_name'];
                }
            }
            $companyRole = null;
            if (isset($company['joint_venture_company_role_id'])) {
                $companyRole = DropDowns::getById(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/jointVentureCompanyRole',
                    $company['joint_venture_company_role_id'])['slug'];
            }
            $companyRole === 'leader' ? $company['is_leader'] = 1 : $company['is_leader'] = 0;
        }
        foreach ($companies as $company) {
            $list[] = array('vendor_name' => $company['vendor_name'],
                'is_leader' => $company['is_leader']);
        }
        return $list;
    }

    public static function getPercentageDifference($estimatedValue, $actualValue)
    {
        if (!$estimatedValue && !$actualValue) {
            return 0;
        }
        if ($estimatedValue > $actualValue) {
            $planActualDifference = ($estimatedValue - $actualValue);
            $planActualDifferencePercentage = ($planActualDifference / $estimatedValue) * 100;
        } else {
            $planActualDifference = ($estimatedValue - $actualValue);
            $planActualDifferencePercentage = ($planActualDifference / $actualValue) * 100;
        }
        return $planActualDifferencePercentage;
    }

    public static function getLastStatus($id)
    {
        $contractLastStatusLog = ContractStatusLog::where('contract_id', $id)->orderBy('id', 'desc')->first();
        if (!$contractLastStatusLog) {
            return [];
        }
        $status = DropDowns::getById(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            $contractLastStatusLog['status_id']);
        $contractLastStatusLog['slug'] = $status['slug'];
        $contractLastStatusLog['name_da'] = $status['name_da'];

        return $contractLastStatusLog;
    }

    public static function prepareContractDurations($contractDetails, $actualStartDate, $contractLastStatus)
    {
        $actualCloseOutDate = null;
        if ($contractLastStatus['slug'] == 'contract-close-out') {
            $actualCloseOutDate = $contractLastStatus['date'];

        }
        $actual_completion_date = self::getEndDate($contractDetails['contract_id'], $contractLastStatus, $contractDetails['planned_end_date']);
        return [
            'planned_start_date' => $contractDetails['planned_start_date'],
            'planned_end_date' => $contractDetails['planned_end_date'],
            'planned_duration_days' => Carbon::parse($contractDetails['planned_start_date'])->diffInDays(Carbon::parse($contractDetails['planned_end_date'])),
            'actual_start_date' => $actualStartDate,
            'actual_completion_date' => self::getEndDate($contractDetails['contract_id'], $contractLastStatus, $contractDetails['planned_end_date']),
            'actual_close_out_date' => $actualCloseOutDate,
            'actual_duration_days' => Carbon::parse($actualStartDate)->diffInDays(Carbon::parse($actual_completion_date))
        ];
    }

    public static function getEndDate($id, $contractLastStatus, $plannedEndDate)
    {


        if ($contractLastStatus['slug'] == 'contract-completion-defect-liability-period') {
            return $contractLastStatus['date'];
        } else {
            $amendments = Amendment::where('contract_id', $id)->orderBy('id', 'desc')->get();
            if ($amendments) {
                $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
                foreach ($amendments as $amendment) {
                    $typeSlug = '';
                    foreach ($amendmentTypes as $amendmentType) {
                        if ($amendment['type_id'] == $amendmentType['id']) {
                            $typeSlug = $amendmentType['slug'];
                            continue;
                        }
                    }

                    if ($typeSlug == 'time-amendment') {
                        $lastAmendmentEndDate = TimeAmendment::where('amendment_id', $amendment['id'])->first();
                        return $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }
                    if ($typeSlug == 'time-and-cost-amendment') {
                        $lastAmendmentEndDate = TimeAndCostAmendment::where('amendment_id', $amendment['id'])->first();
                        return $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }

                }
            }
            return $plannedEndDate;

        }
    }

    public static function prepareContractProgress($id, $contractLastStatus)
    {
        $currentDate = Date::gregorianToJalali(Carbon::now()->toDateString());
        $split_date = explode('-', $currentDate);
        $currentYear = $split_date[2];
        $currentMonth = $split_date[1];
        $planning = DB::select('
                      select 
                        sum(amount) as amount , 
                        sum(physical_progress_percentage) as progress 
                      from contract_planning_finance_affairs_and_physical_progresses 
                      where (contract_id = ' . $id . ') and 
                      (`year` < ' . (int)$currentYear . ' or 
                      (`year` = ' . (int)$currentYear . ' and month_id <= ' . (int)$currentMonth . '))
                        ');
        $actual = DB::select('
                      select 
                        sum(amount) as amount , 
                        sum(physical_progress_percentage) as progress
                      from contract_progress_payments 
                      where (contract_id = ' . $id . ') and 
                      (`year` < ' . (int)$currentYear . ' or 
                      (`year` = ' . (int)$currentYear . ' and month_id <= ' . (int)$currentMonth . '))
                        ');
        return [
            'contract_status' => $contractLastStatus['name_da'],
            'planned_payments_till_now' => $planning[0]->amount,
            'actual_payments_till_now' => $actual[0]->amount,
            'planned_physical_progress_till_now' => $planning[0]->progress,
            'actual_physical_progress_till_now' => $actual[0]->progress,
        ];
    }
}
