<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractProgressPaymentAfterDueDate extends Model
{

    public static $fields = [
        'amount',
        'physical_progress_percentage',
        'remarks',
        'is_approved',
        'is_confirmed',
        'year',
        'month_id',
        'contract_id'
    ];

    protected $guarded = ['id'];

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

}
