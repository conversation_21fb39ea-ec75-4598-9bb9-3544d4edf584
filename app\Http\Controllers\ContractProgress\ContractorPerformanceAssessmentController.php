<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractorPerformanceAssessment;
use NPA\ACPMS\Models\ContractorPerformanceAssessmentLog;

class ContractorPerformanceAssessmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['execution_management_grade_id'] = $data['execution_management_grade_id']['id'];
        $data['work_quality_grade_id'] = $data['work_quality_grade_id']['id'];
        $data['execution_based_on_contract_grade_id'] = $data['execution_based_on_contract_grade_id']['id'];
        $data['work_progress_based_on_plan_grade_id'] = $data['work_progress_based_on_plan_grade_id']['id'];
        try {
            $contractorPerformanceAssessment = ContractorPerformanceAssessment::create(array_only($data, ContractorPerformanceAssessment::$fields));
            if ($contractorPerformanceAssessment) {
                return response()->json([], 201);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $contractorAssessment = ContractorPerformanceAssessment::where('contract_status_id', $id)->first();
            return response()->json($contractorAssessment);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['execution_management_grade_id'] = $data['execution_management_grade_id']['id'];
        $data['work_quality_grade_id'] = $data['work_quality_grade_id']['id'];
        $data['execution_based_on_contract_grade_id'] = $data['execution_based_on_contract_grade_id']['id'];
        $data['work_progress_based_on_plan_grade_id'] = $data['work_progress_based_on_plan_grade_id']['id'];
        try {
            DB::beginTransaction();
            ContractorPerformanceAssessment::where('id', $id)
                ->update(array_only($data, ContractorPerformanceAssessment::$fields));
            DB::commit();
            return response()->json([], 201);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public
    function destroy($id)
    {
        try {

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
