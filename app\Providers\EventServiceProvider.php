<?php

namespace NPA\ACPMS\Providers;

use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'NPA\ACPMS\Events\Event' => [
            'NPA\ACPMS\Listeners\EventListener',
        ],
        'NPA\ACPMS\Events\TestEvent'=>[
            'NPA\ACPMS\Listeners\TestEventListener'
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
