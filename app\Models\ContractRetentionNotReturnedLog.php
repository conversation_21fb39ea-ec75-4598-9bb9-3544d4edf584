<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionNotReturnedLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'reason',
        'contract_retention_applicable_id',
        'contract_retention_not_returned_id',
        'contract_retention_log_id'
    ];

    public function contract_retention_not_returned()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionNotReturned');
    }

    public function contract_retention_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionLogs');
    }
}
