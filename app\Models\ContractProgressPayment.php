<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractProgressPayment extends Model
{
    public static $fields = [
        'status_id',
        'amount',
        'physical_progress_percentage',
        'major_activities',
        'remarks',
        'is_approved',
        'is_confirmed',
        'month_id',
        'year',
        'contract_id'
    ];

    protected $guarded = ['id'];

    public function contract_progress_payment_attachment()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

}
