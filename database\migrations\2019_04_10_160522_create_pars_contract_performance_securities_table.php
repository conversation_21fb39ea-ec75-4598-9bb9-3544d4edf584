<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractPerformanceSecuritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_performance_securities', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->double('amount')->nullable();
            $table->float('percentage')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->date('extended_end_date')->nullable();
            $table->double('extended_amount')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_performance_securities');
    }
}
