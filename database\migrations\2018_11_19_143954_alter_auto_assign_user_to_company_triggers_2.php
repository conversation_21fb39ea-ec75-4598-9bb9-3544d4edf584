<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterAutoAssignUserToCompanyTriggers2 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

      DB::connection()->getPdo()->exec('drop trigger operation_companies_user_after_update_auto_assignment');
      DB::connection()->getPdo()->exec("
            create trigger operation_companies_user_after_update_auto_assignment
            after update
            on companies
            for each row
      exit_label:begin
            
                declare username varchar(255) default null;
                declare user_id int default null;
                declare previous_user_id int default null;
                declare contract_id int default null;
                declare id_for_previously_assgined_company_record int default null;
                declare auto_generated_user_assigned_contracts_count int default null;

                -- Check if we need to proceed
                
                if old.company_id = new.company_id then
                    leave exit_label;
                end if;
            
                if new.company_id is null and old.company_id is null then
                    leave exit_label;
                end if;
            
                -- Get contract_id
                
                select c.id
                into contract_id
                from contracts as c
                join company_general_informations as cgi 
                    on (
                        cgi.contract_id = c.id and
                        cgi.id = new.company_general_information_id
                    );
                
            
               
                -- Get user_id and username
                
                select 
                    u.id,
                    uc.id
                into 
                     user_id,
                    id_for_previously_assgined_company_record
                from users as u
                join 
                    user_companies as uc on uc.user_id = u.id
                where uc.company_id = old.company_id
                 and uc.contract_id = contract_id ;

               set previous_user_id = user_id;
               
                -- Check if the company field is nullified
                
                if new.company_id is null and old.company_id is not null then
                    delete from user_companies
                    where 
                        user_id = user_id and
                        company_id = old.company_id and
                        contract_id = contract_id;
                
                    -- Delete dangling users
                    
                    select count(uc.id)
                    into auto_generated_user_assigned_contracts_count
                    from user_companies as uc
                    join users as u on u.id = uc.user_id
                    where 
                        uc.user_id = user_id and
                        left(u.username, 21) = 'auto_generated_user_';
                        
                    if auto_generated_user_assigned_contracts_count = 0 then 
                        delete from users where id = user_id;
                    end if;
                        
                    leave exit_label;
                end if;
                
                
                 
               
                
                -- See if we need to create an auto generated user
              
                select 
                    u.username,
                    u.id
                into 
                    username,
                    user_id
                from users as u
                join 
                    user_companies as uc on uc.user_id = u.id
                where uc.company_id = new.company_id  limit 1;
                
             
                if username is null then
                    set username = concat('auto_generated_user_', new.company_id);
                    
                    insert users (
                        full_name,
                        username,
                        email,
                        phone_number,
                        password,
                        status,
                        role_id,
                        created_at,
                        updated_at
                    ) values (
                        'auto_generated_full_name',
                        username,
                        concat('dummy_', new.company_id),
                        'dummy',
                        'dummy',
                        'inactive',
                        (
                            select id 
                            from roles 
                            where name = 'company'
                        ),
                        now(),
                        now()
                    );
                    
                    set user_id = last_insert_id();
                end if;
                
                           
                -- Assign or re-assign the contract to a company
                
                if id_for_previously_assgined_company_record is null then
                    insert into user_companies (
                        user_id,
                        contract_id,
                        company_id,
                        created_at,
                        updated_at
                    ) values (
                        user_id,
                        contract_id,
                        new.company_id,
                        now(),
                        now()
                    );
                else
                    update user_companies as uc
                    set uc.company_id = new.company_id ,
                        uc.user_id = user_id
                    where id = id_for_previously_assgined_company_record;
                    
                 -- Delete dangling users
                    select count(uc.id)
                    into auto_generated_user_assigned_contracts_count
                    from user_companies as uc
                    join users as u on u.id = uc.user_id
                    where 
                        uc.user_id = previous_user_id ;
                                         
                     if auto_generated_user_assigned_contracts_count = 0 then 
                          delete from users where id = previous_user_id;
                     end if;
                end if;
                
            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      DB::connection()->getPdo()->exec('drop trigger operation_companies_user_after_update_auto_assignment');

    }
}
