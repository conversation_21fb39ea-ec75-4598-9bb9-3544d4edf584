<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractProgressPaymentsLog;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPP;
use NPA\ACPMS\Models\ContractRePlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ContractRePlanningDateFAAPPController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $contractRePlanningDateFAAPPs = ContractRePlanningDateFAAPP::
            select('id', 're_planning_date', 'type', 'contract_id', 'iteration_count')
                ->where('contract_id', $request->query('contract_id'))->get();
            return response()->json($contractRePlanningDateFAAPPs, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $data = $request->all();

        $contractLastAmendment = Amendment::where('contract_id', $data['contract_id'])
            ->where('is_confirmed', 1)
            ->whereNull('re_plan_id')
            ->orderBy('id', 'desc')
            ->first();

        $data['type'] = 're-planned';
        $data['re_planning_date'] = isset($data['re_planning_date']) ? $data['re_planning_date'] : Carbon::now()->toDateString();
        $iterationCount = ContractRePlanningDateFAAPP::where('contract_id', $data['contract_id'])
            ->orderBy('id', 'desc')
            ->first();

        $iterationCount === null ? $iterationCount['iteration_count'] = 1 : $iterationCount['iteration_count'] += 1;
        $data['iteration_count'] = $iterationCount['iteration_count'];

        try {
            DB::beginTransaction();
            if (
                $contractLastAmendment &&
                $contractLastAmendment['is_confirmed'] &&
                $contractLastAmendment['type_id'] !== $this->matchedAmendmentId('amendment-in-contract-conditions')
            ) {
                $data['type'] = 'amended';
                $created = ContractRePlanningDateFAAPP::create(array_only($data, ContractRePlanningDateFAAPP::$fields));
                $contractLastAmendment->update(['re_plan_id' => $created['id']]);
            } else {
                $created = ContractRePlanningDateFAAPP::create(array_only($data, ContractRePlanningDateFAAPP::$fields));
            }
            $this->setAndAdjustAllPaymentsAndLog($data['contract_id']);
            DB::commit();
            return response()->json($created, 201, [
                'location' => $created['id'],
            ]);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {

        try {
            $contractRePlanningDateFAAPP = ContractRePlanningDateFAAPP::where('contract_id', $contractId)
                ->orderBy('id', 'desc')
                ->first();

            return response()->json($contractRePlanningDateFAAPP, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * @param $contract_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDateRange($contract_id)
    {
        $data['end_date'] = ContractDetails::select('planned_end_date')
            ->where('contract_id', $contract_id)
            ->first()['planned_end_date'];

        $d = ContractProgressPayment::where('contract_id', $contract_id)
            ->orderBy('id', 'desc')
            ->first();
        if ($d) {
            $month = $d['month_id'] + 1;
            $year = $d['year'];
            if ($month > 12) {
                $year = $year + 1;
            }
            $data['start_date'] = implode('-', jDateTime::toGregorian($year, $month, 0));
        } else {
            $d = ContractProgress::where('contract_id', $contract_id)
                ->orderBy('id', 'desc')
                ->first();
            $data['start_date'] = $d['actual_start_date'];
        }
        return response()->json($data, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $id = $request->all()['id'];

        try {
            $contractRePlanningDateFAAPP = ContractRePlanningDateFAAPP::where('id', $id)->first();
            $contractRePlanningDateFAAPP->update(array_only($data, ContractRePlanningDateFAAPP::$fields));
            return response()->json([], 204, [
                'location' => $contractRePlanningDateFAAPP['id'],
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    public function getAmendmentTypeData($contractAmendment)
    {

        switch ($contractAmendment['type_id']) {
            case DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), 'cost-amendment')['id']:
                return CostAmendment::where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), 'time-amendment')['id']:
                return TimeAmendment::where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), 'time-and-cost-amendment')['id']:
                return TimeAndCostAmendment::where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            default:
                throw new \Exception('Invalid amendment type.');
                break;

        }
    }

    public function matchedAmendmentId($slug)
    {
        return DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), $slug)['id'];
    }

    public function getAmendmentTypeId($slug)
    {
        return DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), $slug)['id'];
    }

    public function setAndAdjustAllPaymentsAndLog($contract_id)
    {

        $actualStatusData = DropDowns::getBySlug(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/cpmsProgressScheduleStatus?path=api/dropDown/',
            'actual');


        $rePlanDate = ContractRePlanningDateFAAPP
            ::where('contract_id', $contract_id)
            ->whereDoesntHave('contract_re_plan', function ($query) use ($contract_id) {
                $query->where('contract_id', $contract_id);
            })
            ->orderBy('id', 'desc')
            ->first();

        $actualPayments = ContractProgressPayment::
        select('id', 'status_id', 'month_id', 'year', 'amount', 'physical_progress_percentage', 'major_activities', 'remarks')
            ->where('contract_id', $contract_id)
            ->where('is_confirmed', true)
            ->orderBy('id')
            ->get();

        $amount = $this->totalAndActualAmount($contract_id);

        foreach ($actualPayments as $payment) {
            $actualPaymentId = $payment['id'];
            unset($payment['id']);
            $paymentsLogData = $payment->toArray();
            if ($amount['amendment_id']) {
                $paymentsLogData['amendment_id'] = $amount['amendment_id'];
                $paymentsLog = ContractProgressPaymentsLog::create($paymentsLogData);
                if (!$paymentsLog) {
                    throw new \Exception();
                }
            }

            $payment['contract_re_planning_date_f_a_a_p_p_id'] = $rePlanDate['id'];
            $payment['physical_progress_percentage'] = ($payment['physical_progress_percentage'] * $amount['actual_value']) / $amount['total_value'];
            $payment['status_id'] = $actualStatusData['id'];
            $rePlanPayment = ContractRePlanningFinanceAffairsAndPhysicalProgress::create($payment->toArray());
            if (!$rePlanPayment) {
                throw new \Exception();
            }
            $isUpdated = ContractProgressPayment::where('id', $actualPaymentId)
                ->update(['physical_progress_percentage' => $payment['physical_progress_percentage']]);
            if (!$isUpdated) {
                throw new \Exception();
            }
        }

        return true;

    }

    public function totalAndActualAmount($contract_id)
    {
        $contractDetails = ContractDetails::
        where('contract_id', $contract_id)
            ->first()
            ->toArray();

        $contractLastAmendment = Amendment::where('contract_id', $contract_id)
            ->where('is_confirmed', true)
            ->orderBy('id', 'desc')
            ->first();

        $timeAndCostAmendmentId = $this->getAmendmentTypeId('time-and-cost-amendment');
        $costAmendmentId = $this->getAmendmentTypeId('cost-amendment');
        $amendedAmount = 0;
        $amendmentId = null;
        if (in_array($contractLastAmendment['type_id'], [$timeAndCostAmendmentId, $costAmendmentId])) {
            $amendmentId = $contractLastAmendment['id'];
            if ($contractLastAmendment['type_id'] === $costAmendmentId) {
                $amendedAmount = CostAmendment::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amendment_amount'];
            } else {
                $amendedAmount = TimeAndCostAmendment::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amendment_amount'];
            }

        }

        return [
            'actual_value' => $contractDetails['actual_value'],
            'total_value' => $contractDetails['actual_value'] + $amendedAmount,
            'amendment_id' => $amendmentId
        ];
    }


}
