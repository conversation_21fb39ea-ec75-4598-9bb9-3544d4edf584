<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress   ;

use Illuminate\Http\Request;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractProgressPaymentsLog;

class ContractProgressPaymentsLogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressPaymentsLog  $contractProgressPaymentsLog
     * @return \Illuminate\Http\Response
     */
    public function show(ContractProgressPaymentsLog $contractProgressPaymentsLog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressPaymentsLog  $contractProgressPaymentsLog
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractProgressPaymentsLog $contractProgressPaymentsLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \NPA\ACPMS\Models\ContractProgressPaymentsLog  $contractProgressPaymentsLog
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ContractProgressPaymentsLog $contractProgressPaymentsLog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressPaymentsLog  $contractProgressPaymentsLog
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractProgressPaymentsLog $contractProgressPaymentsLog)
    {
        //
    }
}
