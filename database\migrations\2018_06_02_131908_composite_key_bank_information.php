<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CompositeKeyBankInformation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('company_bank_informations', function (Blueprint $table) {
            $table->dropUnique('company_bank_informations_email_unique');
            $table->unique([
                'email',
                'bank_id',
                'company_id',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('company_bank_informations', function (Blueprint $table) {
            $table->dropUnique('company_bank_informations_email_bank_id_company_id_unique');
            $table->unique('email');
        });
    }
}
