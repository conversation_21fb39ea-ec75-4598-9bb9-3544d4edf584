<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractPlanningAdvance;
use Illuminate\Http\Request;

class ContractPlanningAdvanceController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $advance = ContractPlanningAdvance::where('contract_id', $contractId)->first();
        if (!$advance) {
            return response()->json([], 404);
        }

        $attachments = $advance->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
        foreach ($attachments as $key => $value) {
            $index = $attachments[$key]['field_name'];
            $att = [];
            foreach ($attachments as $k => $v) {
                if ($v['field_name'] == $index && $v['table_name'] == 'contract_planning_advances') {
                    array_push($att, $v);
                }
            }
            $advance[$attachments[$key]['field_name']] = $att;
        }
        return response()->json($advance);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        try {
            DB::beginTransaction();
            $data = $request->all();
            $data['guarantee_type_id'] = $data['guarantee_type']['id'];
            unset($data['guarantee_type']);
            $files['advance_payment_guarantee'] = $data['advance_payment_guarantee'];
            unset($data['advance_payment_guarantee']);
            $advance = ContractPlanningAdvance::create(array_only($data, ContractPlanningAdvance::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $advance, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $advance['id'],
            ]);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningAdvance $contractPlanningAdvance
     * @return \Illuminate\Http\Response
     */
    public function show(ContractPlanningAdvance $contractPlanningAdvance)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningAdvance $contractPlanningAdvance
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractPlanningAdvance $contractPlanningAdvance)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractPlanningAdvance $contractPlanningAdvance
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractPlanningAdvanceId)
    {
        try {

            DB::beginTransaction();
            $data = $request->all();
            $data['guarantee_type_id'] = $data['guarantee_type']['id'];
            unset($data['guarantee_type']);
            $files['advance_payment_guarantee'] = $data['advance_payment_guarantee'];
            unset($data['advance_payment_guarantee']);
            $advance = ContractPlanningAdvance::where('id', $contractPlanningAdvanceId)->first();
            ContractPlanningAdvance::where('id', $contractPlanningAdvanceId)->update(array_only($data, ContractPlanningAdvance::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $advance, $fieldName, $fileContent);
                }
            }
            DB::commit();

            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::callBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningAdvance $contractPlanningAdvance
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractPlanningAdvance $contractPlanningAdvance)
    {
        //
    }
}
