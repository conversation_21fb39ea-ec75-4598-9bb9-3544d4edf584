<?php

namespace NPA\ACPMS\Helpers;

use Exception;

class DropDowns
{

    public static function get($fullPath, $id, $data = null)
    {

        if (!$id) {
            throw new Exception('id is required.');
        }

        if ($data) {

            foreach ($data as $d) {
                if ($d['id'] == $id) {
                    return $d;
                }
            }
            return [];

        } else {

            $fullPath .= '?search[id][match]=full&';
            $fullPath .= 'search[id][value]=' . $id;

            $request = Http::get($fullPath, []);

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            return json_decode($request->body, true)[0];

        }
    }

    public static function getBySlug($fullPath, $slug, $data = null)
    {

        if (!$slug) {
            throw new Exception('slug is required.');
        }

        if ($data) {

            foreach ($data as $d) {
                if ($d['slug'] == $slug) {
                    return $d;
                }
            }
            return [];

        } else {

            $fullPath .= '?search[slug][match]=full&';
            $fullPath .= 'search[slug][value]=' . $slug;

            $request = Http::get($fullPath, []);

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            return json_decode($request->body, true)[0];
        }
    }

    public static function getById($fullPath, $id, $data = null)
    {

        if (!$id) {
            throw new Exception('id is required.');
        }

        if ($data) {

            foreach ($data as $d) {
                if ($d['id'] == $id) {
                    return $d;
                }
            }
            return [];

        } else {

            $fullPath .= '?search[id][match]=full&';
            $fullPath .= 'search[id][value]=' . $id;

            $request = Http::get($fullPath, []);

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            return json_decode($request->body, true)[0];
        }
    }

    public static function getBySlugs($fullPath, $slugs)
    {

        if (!$slugs) {
            throw new Exception('slugs is required.');
        }
        if (!is_array($slugs)) {
            throw new Exception('slugs needs to be array.');
        }
        if (count($slugs) === 0) {
            throw new Exception('slugs should not be an empty array.');
        }

        $fullPath .= '?search[slug][match]=in';
        foreach ($slugs as $slug) {
            $fullPath .= '&search[slug][value][]=' . $slug;
        }

        $request = Http::get($fullPath, []);

        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        return json_decode($request->body, true);

    }
    public static function getByIds($fullPath, $ids)
    {

        if (!$ids) {
            throw new Exception('$ids is required.');
        }
        if (!is_array($ids)) {
            throw new Exception('$ids needs to be array.');
        }
        if (count($ids) === 0) {
            throw new Exception('$ids should not be an empty array.');
        }

        $fullPath .= '?search[id][match]=in';
        foreach ($ids as $id) {
            $fullPath .= '&search[id][value][]=' . $id;
        }

        $request = Http::get($fullPath, []);

        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        return json_decode($request->body, true);

    }
    public static function getAllValues($fullPath)
    {


        $request = Http::get($fullPath, []);

        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        return json_decode($request->body, true);

    }

    public static function getWithCondition($fullPath, $fieldName, $fieldValue, $data = [])
    {

        if ($data) {

            $returnValues = [];

            foreach ($data as $d) {
                if ($d[$fieldName] == $fieldValue) {
                    $returnValues[] = $d;
                }
            }
            return $returnValues;

        } else {

            $fullPath .= '?search[' . $fieldName . '][match]=full&';
            $fullPath .= 'search[' . $fieldName . '][value]=' . $fieldValue;

            $request = Http::get($fullPath, []);

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            return json_decode($request->body, true);
        }

    }

    public static function getAllAmendmentType()
    {
        $path = 'api/dropDown/amendmentType?path=api/dropDown/';

        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);

        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        return json_decode($request->body, true);
    }

    public static function getAllPaymentsStatus()
    {
        $path = 'api/dropDown/cpmsProgressScheduleStatus?path=api/dropDown/';

        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        return json_decode($request->body, true);
    }

    public static function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public static function getElementTypeById($list, $id)
    {
        return array_values(array_filter($list,
            function ($var) use ($id) {
                return $var['id'] === $id;
            })); // this array will always have one element
    }


}
