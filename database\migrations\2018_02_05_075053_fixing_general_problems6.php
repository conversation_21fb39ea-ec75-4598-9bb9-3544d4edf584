<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingGeneralProblems6 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_planning_finance_affairs_and_physical_progresses', function (Blueprint $table) {
            $table->float('physical_progress_percentage')->change();
        });
        Schema::table('contract_re_planning_finance_affairs_and_physical_progresses', function (Blueprint $table) {
            $table->float('physical_progress_percentage')->change();
        });
        Schema::table('contract_progress_payments', function (Blueprint $table) {
            $table->float('physical_progress_percentage')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
