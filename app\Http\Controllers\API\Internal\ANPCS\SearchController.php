<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\ANPCS;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;

class SearchController extends Controller
{
    public function searchContracts(Request $request)
    {
        $key = $request->query('key');
        $key = rtrim($key, ' ');
        if ($key === '') {
            return response()->json([], 200);
        }
        $procurementEntities = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity');
        $sectors = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/sector');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $procurementMethods = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementMethod');
        $currencies = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency');
        $contractTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractType');
        $donors = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sourceDonor');
        $budgetCodes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/code');
        $PPMSRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') .
            'api/matched-title-contracts?match_string=' . $key, []);
        if ($PPMSRequest->status_code >= 300) {
            throw new Exception('Problem getting data from APPMS');
        }

        $projectsId = json_decode($PPMSRequest->body, true);
        $projectIds = -1;
        if ($projectsId !== []) {
            $projectIds = implode(', ', array_flatten($projectsId));
        }
        $contracts = DB::select('
               select
                c.project_id,
                c.contract_number,
                c.sector_id,
                c.procurement_type_id,
                c.procurement_entity_id,
                c.procurement_method_id,
                c.currency_id,
                 (select
                        ta.amended_end_date as amended_end_date
                 from amendments as inner_a
                 join time_amendments as ta
                     on ta.amendment_id = inner_a.id    
                 where inner_a.is_approved = true and
                 inner_a.contract_id = c.id
                 order by inner_a.id DESC
                 limit 1) as amended_end_date,
                (ifnull(amendment.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) as amendment_amount,
                ((ifnull(cd.actual_value, 0)) * if(c.exchange_rate, c.exchange_rate, 1)) as actual_contract_price,
                        ((ifnull(cd.actual_value, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                        ) as final_contract_price,
                cp.actual_start_date as planned_start_date,
                cd.planned_end_date,
                p.name_da as province_name_prs,
                p.name_pa as province_name_ps,
                p.name_en as province_name_en,
                d.name_da as district_name,
                company.license_number as license_number
            from contracts as c
             left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_amendments as ta
                     on ta.amendment_id = a.id    
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on c.id = amendment.contract_id   
            join contract_details as cd
                on cd.contract_id = c.id
             join contract_progresses as cp
                on cp.contract_id = c.id        
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            join domestic_contract_execution_locations as dcel 
                    on dcel.contract_id = c.id 
                join temp_districts as d
                    on d.id = dcel.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
            left join company_general_informations as cgi
                on cgi.contract_id = c.id
            left join companies as company
                on company.company_general_information_id = cgi.id                    
            where c.is_published = true 
            and c.contract_number like concat("%", ? , "%") or c.project_id in (' . $projectIds . ')
        ', [$key]);
        $procurementNumbers = [];
        $projectIds = [];
        $companyLicenses = [];
        foreach ($contracts as $contract) {
            $projectIds[] = $contract->project_id;
            if ($contract->license_number) {
                $companyLicenses[] = $contract->license_number;
            }
        }
        $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info',
            [],
            [
                'ids' => $projectIds
            ]
        );
        $projects = json_decode($request->body, true);
        if ($request->status_code >= 300) {
            throw new Exception('Problem getting data from APPMS');
        }
        $companies = [];
        if (count($companyLicenses) !== 0) {
            $companyRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company/searchByLicenses', [],
                ['licenses' => $companyLicenses]
            );

            if ($companyRequest->status_code >= 300) {
                throw new Exception('Problem getting data from CDM');
            }
            $decodedData = json_decode($companyRequest->body, true);
            $companies = $decodedData ? $decodedData : [];
        }


        foreach ($contracts as $contract) {
//            $entity = DropDowns::getElementTypeById($procurementEntities, $contract->procurement_entity_id);
            $contract->procurement_entity_name_prs = isset($contract->procurement_entity_id) && $contract->procurement_entity_id ?
            (DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities) ? 
            DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities)['name_da'] : '') : null;
            $contract->procurement_entity_name_ps = isset($contract->procurement_entity_id) && $contract->procurement_entity_id ?
            (DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities) ? 
            DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities)['name_pa'] : '') : null;
            $contract->procurement_entity_name_en = isset($contract->procurement_entity_id) && $contract->procurement_entity_id ?
            (DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities) ? 
            DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities)['name_en'] : '') : null;
            $contract->procurement_entity_slug = isset($contract->procurement_entity_id) && $contract->procurement_entity_id ?
            (DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities) ? 
            DropDowns::getById('', $contract->procurement_entity_id, $procurementEntities)['slug'] : '') : null;

            $contract->procurement_type_name_prs = isset($contract->procurement_type_id) && $contract->procurement_type_id ?
                DropDowns::getById('', $contract->procurement_type_id, $procurementTypes)['name_da'] : null;
            $contract->procurement_type_name_ps = isset($contract->procurement_type_id) && $contract->procurement_type_id ?
                DropDowns::getById('', $contract->procurement_type_id, $procurementTypes)['name_pa'] : null;
            $contract->procurement_type_name_en = isset($contract->procurement_type_id) && $contract->procurement_type_id ?
                DropDowns::getById('', $contract->procurement_type_id, $procurementTypes)['name_en'] : null;

            $contract->sector_name_prs = isset($contract->sector_id) && $contract->sector_id ?
                DropDowns::getById('', $contract->sector_id, $sectors)['name_da'] : null;
            $contract->sector_name_ps = isset($contract->sector_id) && $contract->sector_id ?
                DropDowns::getById('', $contract->sector_id, $sectors)['name_pa'] : null;
            $contract->sector_name_en = isset($contract->sector_id) && $contract->sector_id ?
                DropDowns::getById('', $contract->sector_id, $sectors)['name_en'] : null;
//
//            $contract->donor_name_prs = isset($contract->donor_id) && $contract->donor_id ?
//                DropDowns::getById('', $contract->donor_id, $donors)['name_da'] : null;
//            $contract->donor_name_ps = isset($contract->donor_id) && $contract->donor_id ?
//                DropDowns::getById('', $contract->donor_id, $donors)['name_pa'] : null;
//            $contract->donor_name_en = isset($contract->donor_id) && $contract->donor_id ?
//                DropDowns::getById('', $contract->donor_id, $donors)['name_en'] : null;
//            $contract->donor_slug = isset($contract->donor_id) && $contract->donor_id ?
//                DropDowns::getById('', $contract->donor_id, $donors)['slug'] : null;

            $contract->procurement_method_name_prs = isset($contract->procurement_method_id) && $contract->procurement_method_id ?
                DropDowns::getById('', $contract->procurement_type_id, $procurementMethods)['name_da'] : null;
            $contract->procurement_method_name_ps = isset($contract->procurement_method_id) && $contract->procurement_method_id ?
                DropDowns::getById('', $contract->procurement_method_id, $procurementMethods)['name_pa'] : null;
            $contract->procurement_method_name_en = isset($contract->procurement_method_id) && $contract->procurement_method_id ?
                DropDowns::getById('', $contract->procurement_method_id, $procurementMethods)['name_en'] : null;


            $contract->currency_name_prs = isset($contract->currency_id) && $contract->currency_id ?
                DropDowns::getById('', $contract->currency_id, $currencies)['name_da'] : null;
            $contract->currency_name_ps = isset($contract->currency_id) && $contract->currency_id ?
                DropDowns::getById('', $contract->currency_id, $currencies)['name_pa'] : null;
            $contract->currency_name_en = isset($contract->currency_id) && $contract->currency_id ?
                DropDowns::getById('', $contract->currency_id, $currencies)['name_en'] : null;

//            if (sizeof($entity) > 0) {
//                $entity = $entity[0];
//                $contract->procurement_entity_name = $entity['name_da'];
//                unset($contract->procurement_entity_id);
//            } else {
//                $contract->procurment_entity_name = 'نا معلوم';
//                unset($contract->procurement_entity_id);
//
//            }
            $contract->contractor_tin = null;
            $contract->contractor_name_prs = null;
            $contract->contractor_name_en = null;
            $contract->contractor_name_ps = null;
            foreach ($companies as $company) {
                if ($contract->license_number == $company['licence_number']) {
                    $contract->contractor_tin = $company['tin'];
                    $contract->contractor_name_prs = $company['name_da'];
                    $contract->contractor_name_en = $company['name_en'];
                    $contract->contractor_name_ps = $company['name_pa'];
                }
            }
            $contract->procurement_title_prs = '-';
            $contract->procurement_title_ps = '-';
            $contract->procurement_title_en = '-';
            foreach ($projects as $value) {
                if ($contract->project_id == $value['project_id']) {
                    $contract->procurement_title_prs = $value['project_name_da'] ? $value['project_name_da'] : '-';
                    $contract->procurement_title_ps = $value['project_name_pa'] ? $value['project_name_pa'] : '-';
                    $contract->procurement_title_en = $value['project_name_en'] ? $value['project_name_en'] : '-';
                    $contract->contract_type_name_prs = isset($value['contract_type_id']) && $value['contract_type_id'] ?
                        DropDowns::getById('', $value['contract_type_id'], $contractTypes)['name_da'] : null;
                    $contract->contract_type_name_ps = isset($value['contract_type_id']) && $value['contract_type_id'] ?
                        DropDowns::getById('', $value['contract_type_id'], $contractTypes)['name_pa'] : null;
                    $contract->contract_type_name_en = isset($value['contract_type_id']) && $value['contract_type_id'] ?
                        DropDowns::getById('', $value['contract_type_id'], $contractTypes)['name_en'] : null;
                    $contract->contract_type_slug = isset($value['contract_type_id']) && $value['contract_type_id'] ?
                        DropDowns::getById('', $value['contract_type_id'], $contractTypes)['slug'] : null;

                    $FoundBudget = isset($value['budget_code_id']) && $value['budget_code_id'] ?
                        DropDowns::getById('', $value['budget_code_id'], $budgetCodes) : null;
                    $FoundDonor = isset($value['v2_project_donors'][0]) && isset($value['v2_project_donors'][0]['donor_id'])
                    && $value['v2_project_donors'][0]['donor_id'] ?
                        DropDowns::getById('', $value['v2_project_donors'][0]['donor_id'], $donors) : null;

                    $contract->budget_code = isset($FoundBudget['budget_code']) || $FoundBudget['slug'] !== 'not-available' ? $FoundBudget['budget_code'] : null;
                    $contract->budget_type_prs = isset($FoundBudget['budget_code_fund_type']['name_da']) || $FoundBudget['slug'] !== 'not-available' ? $FoundBudget['budget_code_fund_type']['name_da'] : null;
                    $contract->budget_type_ps = isset($FoundBudget['budget_code_fund_type']['name_pa']) || $FoundBudget['slug'] !== 'not-available' ? $FoundBudget['budget_code_fund_type']['name_pa'] : null;
                    $contract->budget_type_en = isset($FoundBudget['budget_code_fund_type']['name_en']) || $FoundBudget['slug'] !== 'not-available' ? $FoundBudget['budget_code_fund_type']['name_en'] : null;

                    $contract->donor_name_prs = isset($FoundDonor['name_da']) ? $FoundDonor['name_da'] : null;
                    $contract->donor_name_ps = isset($FoundDonor['name_pa']) ? $FoundDonor['name_pa'] : null;
                    $contract->donor_name_en = isset($FoundDonor['name_en']) ? $FoundDonor['name_en'] : null;
                    $contract->donor_slug = isset($FoundDonor['slug']) ? $FoundDonor['slug'] : null;
                }
            }
        }
        return response()->json($contracts, 200);
    }
}
