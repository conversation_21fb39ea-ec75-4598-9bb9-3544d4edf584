<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractProgressAdvance extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'amount',
        'remarks',
        'guarantee_type_id',
        'date',
        'is_approved',
        'is_confirmed',
        'is_published'
    ];

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
