<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ChallengesAndRemarks extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'title',
        'creator_id', // unlike it's name this one stores role_id
        'category_id',
        'status_id',
        'resolution_id',
        'start_date',
        'end_date',
        'description',
        'suggested_solution_procurement_entity',
        'suggested_solution_company',
        'applied_solution',
        'is_confirmed',
        'is_approved',
        'contract_id'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
