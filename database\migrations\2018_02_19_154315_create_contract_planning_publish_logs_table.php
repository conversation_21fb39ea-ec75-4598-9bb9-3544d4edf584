<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateContractPlanningPublishLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_planning_publish_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('contract_id');
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->boolean('has_published')->default(false);
            $table->timestamps();
        });
        DB::connection()->getPdo()->exec('
            create trigger log_contracts_publishment_status
            after update 
            on contracts
            for each row
            begin
                if old.is_published != new.is_published then
                    insert into contract_planning_publish_logs (
                        contract_id,
                        has_published,
                        created_at
                    ) values (
                        new.id,
                        new.is_published,
                        new.updated_at
                    );
                end if;
            end 
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_planning_publish_logs');
        DB::connection()->getPdo()->exec('drop trigger log_contracts_publishment_status');
    }
}
