<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\AttachmentLog;

class  AttachmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $parentId = $request->query('id');
        $attachments = Attachment::where('foreign_key', (int)$parentId)->get();
        return response()->json($attachments, 200);

    }

    public function debarmentFileDownload()
    {
        try {
            ob_end_clean();
            return response()->download(storage_path('debarmentFile\debarmentForm.docx'));
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {


    }


    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Attachment $attachment
     * @return \Illuminate\Http\Response
     */
    public function show(Attachment $attachment)
    {
        Log::useFiles(storage_path() . '/logs/s3FilesDownloading.log');
        try {
            if (config('custom.CUSTOM_STORAGE_USED') === 'S3') {
                return response()->json(config('custom.AWS_S3_BASE_URL') . $attachment['stored_path']);
            }
            return response()->download(storage_path('app' . $attachment['stored_path']));
        } catch (\Throwable $t) {
            Log::info($t);
            return Error::composeResponse($t);
        }

    }

    public function downloadLogFile($id)
    {
        Log::useFiles(storage_path() . '/logs/s3FilesDownloading.log');
        $attachment = AttachmentLog::find($id);
        try {
            if (config('custom.CUSTOM_STORAGE_USED') === 'S3') {
                return response()->json(config('custom.AWS_S3_BASE_URL') . $attachment['stored_path']);
            }
            return response()->download(storage_path('app' . $attachment['stored_path']));
        } catch (\Throwable $t) {
            Log::info($t);
            return Error::composeResponse($t);
        }

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Attachment $attachment
     * @return \Illuminate\Http\Response
     */
    public function edit(Attachment $attachment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Attachment $attachment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Attachment $attachment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Attachment $attachment
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $rowsCount = Attachment::where('id', $id)->delete();
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response()->json([], 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * @param $path
     */
    public static function logFilePath($path)
    {
        Log::useFiles(storage_path() . '/logs/files-path.log');
        Log::info('path: ' . $path);
    }

    /**
     *
     *  TODO
     * the middleware auth:api should be added in order to save with user ID
     * @param $originalRequest
     * @param $enteredParentRecord
     * @param $fieldName
     * @param $file
     * @return void
     */

    static public function saveFile($originalRequest, $enteredParentRecord, $fieldName, $file)
    {

        Log::useFiles(storage_path() . '/logs/s3FilesUpload.log');

        Log::info('-------------------------------------------------------------');
        Log::info('Before the try and catch block');
        Log::info('baseUrl :' . URL::to('/'));
        Log::info('path :' . str_replace(URL::to('/'), '', $originalRequest->url()));
        Log::info('-------------------------------------------------------------');

        try {
            DB::beginTransaction();
            $baseUrl = URL::to('/');
            $userId = $originalRequest['user_id'];

            $fileName = Carbon::now()->timestamp . '-' . $userId . '-' . str_replace( array("#", "'", ";", "*", "$", "&", "%", "@","^", "!", "/", "+"), '', $file['name']);
            $path = str_replace($baseUrl, '', $originalRequest->url());
            if (config('custom.CUSTOM_STORAGE_USED') === 'S3') {
                $file_upload = Storage::disk('s3')->putFileAs($path, $originalRequest->file('__' . $fieldName), $fileName, "public");
            } else {
                $file_upload = Storage::disk('local')->putFileAs($path, $originalRequest->file('__' . $fieldName), $fileName);
            }

            // just to log the precess
            Log::info('baseUrl :' . $baseUrl);
            Log::info('userId :' . $userId);
            Log::info('fileName :' . $fileName);
            Log::info('path :' . $path);
            Log::info('Upload to s3 result :' . $file_upload);

            //end
            Attachment::create([
                'original_name' => $file['name'],
                'assigned_name' => $fileName,
                'language_id' => $file['language_id'],
                'format' => $file['type'],
                'stored_path' => $path . '/' . $fileName,
                'foreign_key' => $enteredParentRecord['id'],
                'field_name' => $fieldName,
                'table_name' => with($enteredParentRecord)->getTable(),
            ]);
            DB::commit();
        } catch (\Throwable $t) {
            DB::rollBack();
            Log::info($t);
            return Error::composeResponse($t);
        }

    }

}
