<?php

namespace NPA\ACPMS\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use NPA\ACPMS\Console\Commands\GenerateSyncWithAETSCommand;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        GenerateSyncWithAETSCommand::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
         $aets = config('custom.SYNC_INTERVAL_AETS');
         $googleSheet = config('custom.CUSTOM_GOOGLE_SHEET_DATA_INTERVAL');
         list($aetsInterval, $aetsArgs) = explode(':', $aets ? $aets : ':');
         list($googleSheetInterval, $googleSheetArgs) = explode(':', $googleSheet ? $googleSheet : ':');
         $tasks = [
           [
             'class' => 'NPA\ACPMS\Types\SyncWithAETS',
             'interval' => $aetsInterval,
             'args' => $aetsArgs
           ],
             [
                 'class' => 'NPA\ACPMS\Types\SyncDataWithGoogleSheet',
                 'interval' => $googleSheetInterval,
                 'args' => $googleSheetArgs
             ],
         ];

         foreach ($tasks as $task) {
           if (!$task['interval']) {
             continue;
           }
           $schedule->call(function () use ($task) {
              call_user_func([$task['class'], 'operate']);
           })->{$task['interval']}($task['args']);
         }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
