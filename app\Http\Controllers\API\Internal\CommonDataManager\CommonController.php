<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\CommonDataManager;

use Exception;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;

class CommonController extends Controller
{
    public function lastUpdateTimestamp()
    {
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . '/api/common/lastUpdateTimestamp', []);
        if ($remoteRequest->status_code > 300) {
            return Error::composeResponse(new Exception($remoteRequest->body));
        } else {
            return response()->json(json_decode($remoteRequest->body, true));
        }

    }
}
