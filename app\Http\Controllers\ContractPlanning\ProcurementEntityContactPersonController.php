<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Support\Facades\Log;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ProcurementEntityContactPerson;
use Illuminate\Http\Request;

class ProcurementEntityContactPersonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request['email'] = $request['contact_person_email'];
        unset($request['contact_person_email']);
        try {
            $createdId = Contract::find($request->input('contract_id'))
                ->procurement_entity_contact_person()
                ->create(array_only($request->all(), ProcurementEntityContactPerson::$fields))->id;
            return response()->json([], 201, [
                'location' => $createdId
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ProcurementEntityContactPerson $procurementEntityContactPerson
     * @return \Illuminate\Http\Response
     */
    public function show($procurementEntityContactPerson)
    {
        try {
            $contactData = ProcurementEntityContactPerson::where('contract_id', $procurementEntityContactPerson)->get();
            return response()->json($contactData, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ProcurementEntityContactPerson $procurementEntityContactPerson
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $procurementEntityContactPerson)
    {
        $request['email'] = $request['contact_person_email'];
        unset($request['contact_person_email']);
        try {
            $rowsCount = ProcurementEntityContactPerson::where('id', $procurementEntityContactPerson)
                ->update(array_only($request->all(), ProcurementEntityContactPerson::$fields));
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response()->json($rowsCount, 201, [
                'location' => $rowsCount
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ProcurementEntityContactPerson $procurementEntityContactPerson
     * @return \Illuminate\Http\Response
     */
    public function destroy($procurementEntityContactPerson)
    {
        try {
            $rowsCount = ProcurementEntityContactPerson::destroy($procurementEntityContactPerson);
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response(null, 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function showOne($procurementEntityContactPerson)
    {
        try {
            $contactData = ProcurementEntityContactPerson::where('id', $procurementEntityContactPerson)->first();
            return response()->json($contactData, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }
}
