<?php
/**
 * Created by PhpStorm.
 * User: Wahrez
 * Date: 3/18/2018
 * Time: 11:46 AM
 */

namespace NPA\ACPMS\Helpers;


use Morilog\Jalali\jDateTime;

class Date
{
    public static function gregorianToJalali($date)
    {
        if (!is_string($date)) {
            return '';
        }
        $split_date = explode('-', $date);
        $year = $split_date[0];
        $month = $split_date[1];
        $day = $split_date[2];
        $date = jDateTime::toJalali($year, $month, $day);
        return $date = $date[2] . '-' . $date[1] . '-' . $date[0];
    }

    public static function gregorianToJalaliInSlashFormat($date)
    {
        if (!is_string($date)) {
            return '';
        }
        $split_date = explode('-', $date);
        $year = $split_date[0];
        $month = $split_date[1];
        $day = explode(' ', $split_date[2])[0];
        $date = jDateTime::to<PERSON><PERSON><PERSON>($year, $month, $day);
        return $date = $date[0] . '/' . $date[1] . '/' . $date[2];
    }

    public static function getDifferent($start_date, $end_date)
    {
        $start_date = $start_date;
        $end_date = $end_date;

        $start_date = strtotime($start_date);
        $end_date = strtotime($end_date);

        $year1 = date('Y', $start_date);
        $year2 = date('Y', $end_date);

        $month1 = date('m', $start_date);
        $month2 = date('m', $end_date);

        return $diff = (($year2 - $year1) * 12) + ($month2 - $month1);
    }

    public static function getDifferentDateInDays($start_date, $end_date)
    {
        $temp_start_date = explode('-', $start_date);
        $year1 = $temp_start_date && $temp_start_date[0] ? $temp_start_date[0] : null;
        $month1 = $temp_start_date && $temp_start_date[1] ? $temp_start_date[1] : null;
        $day1 = $temp_start_date && $temp_start_date[2] ? $temp_start_date[2] : null;

        $temp_end_date = explode('-', $end_date);
        $year2 = $temp_end_date && $temp_end_date[0] ? $temp_end_date[0] : null;
        $month2 = $temp_end_date && $temp_end_date[1] ? $temp_end_date[1] : null;
        $day2 = $temp_end_date && $temp_end_date[2] ? $temp_end_date[2] : null;

        $years = $year2 - $year1;
        $month1 = (12 - $month1);
        $month2 = (12 - $month2);
        $months = $month1 + $month2;
        $days = (30 - $day1) + (30 - $day2);
        if ($years > 0) {
            $result = ((($years * 12) + $months) * 30) + $days;
        } else {
            $result = ($months * 30) + $days;
        }
        return $result;
    }

    public static function getJalaliMonthName($index)
    {
        switch ($index) {
            case 1:
                return 'حمل';
                break;
            case 2:
                return 'ثور';
                break;
            case 3:
                return 'جوزا';
                break;
            case 4:
                return 'سرطان';
                break;
            case 5:
                return 'اسد';
                break;
            case 6:
                return 'سنبله';
                break;
            case 7:
                return 'میزان';
                break;
            case 8:
                return 'عقرب';
                break;
            case 9:
                return 'قوس';
                break;
            case 10:
                return 'جدی';
                break;
            case 11:
                return 'دلو';
                break;
            case 12:
                return 'حوت';
                break;
            default:
                throw new \Exception('invalid month id');

        }
    }
}
