<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRePlanningDateFAAPP extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        're_planning_date',
        'type',
        'contract_id',
        'iteration_count',
    ];

    public function contract_re_plan()
    {
        $relation = $this->hasMany('NPA\ACPMS\Models\ContractRePlanningFinanceAffairsAndPhysicalProgress');
        $relation
            ->getQuery()
            ->orderBy('year', 'asc')
            ->orderBy('month_id', 'asc');
        return $relation;
    }

    public function contract_re_plan_verification()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification');
    }

    public function contract_re_plan_progress()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRePlanningFinanceAffairsAndPhysicalProgress');
    }
}
