<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Carbon\Carbon;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractProgressVerification;
use Illuminate\Http\Request;

class ContractProgressVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        try {
            if (isset($data['is_confirmed'])) {
                $data['contract_progress_id'] = $data['parent_id'];
                unset($data['parent_id']);
                unset($data['has_requested_to_unpublish']);
                ContractProgressVerification::create(array_only($data, ContractProgressVerification::$fields));
                return response()->json([], 201);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressVerification $contractProgressVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractProgressId)
    {
        try {
            $contractProgressVerification = ContractProgressVerification::where('contract_progress_id', $contractProgressId)->first();
            return response()->json($contractProgressVerification, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressVerification $contractProgressVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractProgressVerification $contractProgressVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractProgressVerification $contractProgressVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractProgressId)
    {
        $data = $request->all();
        $currentDate = Carbon::now()->toDateTimeString();
        try {
            if (isset($data['is_published']) && $data['is_published']) {
                ContractProgressVerification::where('contract_progress_id', $contractProgressId)
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );

            } else {
                ContractProgressVerification::where('contract_progress_id', $contractProgressId)
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                        ]
                    );
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressVerification $contractProgressVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractProgressVerification $contractProgressVerification)
    {
        //
    }
}
