<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class CostAmendment extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'amendment_id',
        'is_above_threshold',
        'current_cost_threshold_value',
        'amendment_amount',
        'replanning_start_date',
        'amended_end_date',
        'amended_performance_security_amount',
        'amendment_reasons'
    ];
}
