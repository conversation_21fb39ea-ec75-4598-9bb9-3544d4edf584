<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractBudgetTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_budget_types', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('pars_general_informations_id');
            $table->foreign('pars_general_informations_id')
                ->references('id')
                ->on('pars_general_informations')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('contract_budget_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_budget_types');
    }
}
