<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class MonitoringReport extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'report_title',
        'report_date',
        'contract_id'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }


    public function pdf_report()
    {
        return $this->hasOne('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
