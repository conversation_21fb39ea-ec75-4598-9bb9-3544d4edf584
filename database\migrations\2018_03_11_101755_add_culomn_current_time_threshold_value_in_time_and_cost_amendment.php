<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCulomnCurrentTimeThresholdValueInTimeAndCostAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->float('current_time_threshold_value')
            ->after('current_cost_threshold_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('time_and_cost_amendments', 'current_time_threshold_value')) {
            Schema::table('time_and_cost_amendments', function (Blueprint $table) {
                $table->dropColumn('current_time_threshold_value');
            });
        }
    }
}
