<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractDetailsDelayPenalty;
use NPA\ACPMS\Models\ContractPerformanceSecurity;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ProvisionalSumAndContingency;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ContractDetailsController extends Controller
{

    private $files = [
        'agreement',
        'contract_general_conditions',
        'contract_special_conditions',
        'no_objection_later_of_donor',
        'reference_terms',
        'key_staffs',
        'financial_proposal',
        'action_plan',
        'contract_performance_guarantee',
        'insurance_document',
        'contract_signature_authorization_letter',
        'negotiation_meeting_minutes',
        'delay_penalty_document',
        'letters_and_requests',
        'bidder_conflict_of_interest',
        'technical_specification',
        'technical_maps',
        'quantities_table_price_bill',
        'work_start_letter',
        'drawings',
        'other_1',
        'other_2',
        'other_3',
        'other_4',
        'other_5',
        'other_6',
        'other_7',
        'other_8',
        'other_9',
        'other_10',
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);

        try {
            DB::beginTransaction();
            $contractDetails = ContractDetails::create(array_only($data, ContractDetails::$fields));
            if (
                $data['is_provisional_sum_and_contingency_applicable'] &&
                !$data['is_provisional_sum_and_contingency_included']
            ) {
                $data['contract_detail_id'] = $contractDetails->id;
                ProvisionalSumAndContingency::create(array_only($data, ProvisionalSumAndContingency::$fields));
            }

            if ($data['is_performance_security_applicable']) {
                $data['guarantee_type_id'] = $data['guarantee_type']['id'];
                $data['contract_detail_id'] = $contractDetails->id;
                $data['currency_id'] = $data['currency']['id'];
                ContractPerformanceSecurity::create(array_only($data, ContractPerformanceSecurity::$fields));
            }

            if ($data['is_delay_penalty_applicable'] &&
                !$data['is_contract_category_transfers_or_rental_of_vehicles']) {
                $data['contract_detail_id'] = $contractDetails->id;
                $data['period_id'] = $data['delay_penalty_period']['id'];
                $data['percentage'] = $data['delay_penalty_percentage'];
                ContractDetailsDelayPenalty::create(array_only($data, ContractDetailsDelayPenalty::$fields));
            }

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractDetails, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 201,
                [
                    'location' => $contractDetails['id'],
                ]
            );

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     * @param $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($contractId)
    {
        try {
            $contractDetails = ContractDetails::where('contract_id', $contractId)->first();
            if (!isset($contractDetails)) {
                return response()->json([], 200);
            }
            $contractDetails['contract_exchange_rate'] = Contract::select('exchange_rate')->where('id', $contractId)->first();
            if ($contractDetails['contract_exchange_rate']) {
                $contractDetails['contract_exchange_rate'] = $contractDetails['contract_exchange_rate']['exchange_rate'];
            }
            if ($contractDetails['contract_exchange_rate'] === null) {
                $contractDetails['contract_exchange_rate'] = 1;
            }
            if ($contractDetails['is_performance_security_applicable']) {
                $contractPerformanceSecurity = ContractPerformanceSecurity::where('contract_detail_id', $contractDetails['id'])->first();
                $contractDetails['guarantee_type_id'] = $contractPerformanceSecurity['guarantee_type_id'];
                $contractDetails['start_date'] = $contractPerformanceSecurity['start_date'];
                $contractDetails['end_date'] = $contractPerformanceSecurity['end_date'];
                $contractDetails['amount'] = $contractPerformanceSecurity['amount'];
                $contractDetails['currency_id'] = $contractPerformanceSecurity['currency_id'];
                $contractDetails['exchange_rate'] = $contractPerformanceSecurity['exchange_rate'];
                $contractDetails['contract_detail_id'] = $contractPerformanceSecurity['contract_detail_id'];
            }

            if ($contractDetails['is_provisional_sum_and_contingency_applicable'] &&
                !$contractDetails['is_provisional_sum_and_contingency_included']) {
                $provisionalSumContingency = ProvisionalSumAndContingency::where('contract_detail_id', $contractDetails['id'])->first();
                $contractDetails['provisional_sum_and_contingency'] = $provisionalSumContingency['provisional_sum_and_contingency'];
                $contractDetails['contract_detail_id'] = $provisionalSumContingency['contract_detail_id'];
            }

            if ($contractDetails['is_delay_penalty_applicable'] &&
                !$contractDetails['is_contract_category_transfers_or_rental_of_vehicles']) {
                $provisionalSumContingency = ContractDetailsDelayPenalty::where('contract_detail_id', $contractDetails['id'])->first();
                $contractDetails['delay_penalty_percentage'] = $provisionalSumContingency['percentage'];
                $contractDetails['delay_penalty_period_id'] = $provisionalSumContingency['period_id'];
            }

            $attachments = $contractDetails->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index && $v['table_name'] == 'contract_details') {
                        array_push($att, $v);
                    }
                }
                $contractDetails[$attachments[$key]['field_name']] = $att;
            }

            $contractProgress = ContractProgress::where('contract_id', $contractId)->first();
            if ($contractProgress) {
                $contractDetails['actual_start_date'] = $contractProgress['actual_start_date'];
            }

            $amendments = Amendment::where('contract_id', $contractId)->orderBy('id', 'desc')->get();
            if ($amendments) {
                $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
                foreach ($amendments as $amendment) {

                    foreach ($amendmentTypes as $amendmentType) {
                        if ($amendment['type_id'] == $amendmentType['id']) {
                            $typeSlug = $amendmentType['slug'];
                            continue;
                        }
                    }

                    if ($typeSlug == 'time-amendment') {
                        $lastAmendmentEndDate = TimeAmendment::where('amendment_id', $amendment['id'])->first();
                        $contractDetails['last_amended_end_date'] = $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }
                    if ($typeSlug == 'time-and-cost-amendment') {
                        $lastAmendmentEndDate = TimeAndCostAmendment::where('amendment_id', $amendment['id'])->first();
                        $contractDetails['last_amended_end_date'] = $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }

                }
            }
            return response()->json($contractDetails);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractDetails $contractDetails
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractDetails $contractDetails)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {

        $data = $request->all();
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();

            $contractDetails = ContractDetails::where('contract_id', $id)->first();
            $data['contract_detail_id'] = $contractDetails->id;

            ContractDetails::where('contract_id', $id)->update(array_only($data, ContractDetails::$fields));

            if ($data['is_provisional_sum_and_contingency_applicable'] && !$data['is_provisional_sum_and_contingency_included']) {
                ProvisionalSumAndContingency::
                updateOrCreate(['contract_detail_id' => $contractDetails->id], array_only($data, ProvisionalSumAndContingency::$fields));
            } else {
                $checkProvisionalSumAndContingency = ProvisionalSumAndContingency::where('contract_detail_id', $contractDetails->id)->first();
                if (isset($checkProvisionalSumAndContingency) && !empty($checkProvisionalSumAndContingency)) {
                    ProvisionalSumAndContingency::where('contract_detail_id', $contractDetails->id)->delete();
                }
            }

            if ($data['is_delay_penalty_applicable'] &&
                !$data['is_contract_category_transfers_or_rental_of_vehicles']) {
                $data['period_id'] = $data['delay_penalty_period']['id'];
                $data['percentage'] = $data['delay_penalty_percentage'];
                ContractDetailsDelayPenalty::updateOrCreate([
                    'contract_detail_id' => $contractDetails->id
                ], array_only($data, ContractDetailsDelayPenalty::$fields));
            } else {
                $checkContractDetailsDelayPenalty = ContractDetailsDelayPenalty::where('contract_detail_id', $contractDetails->id)->first();
                if (isset($checkContractDetailsDelayPenalty) && !empty($checkContractDetailsDelayPenalty)) {
                    ContractDetailsDelayPenalty::where('contract_detail_id', $contractDetails->id)->delete();
                }
            }

            if ($data['is_performance_security_applicable']) {
                $data['guarantee_type_id'] = $data['guarantee_type']['id'];
                $data['currency_id'] = $data['currency']['id'];
                ContractPerformanceSecurity::updateOrCreate([
                    'contract_detail_id' => $contractDetails->id
                ], array_only($data, ContractPerformanceSecurity::$fields));

            } else {
                $checkContractPerformanceSecurity = ContractPerformanceSecurity::where('contract_detail_id', $contractDetails->id)->first();
                if (isset($checkContractPerformanceSecurity) && !empty($checkContractPerformanceSecurity)) {
                    ContractPerformanceSecurity::where('contract_detail_id', $contractDetails->id)->delete();
                }
            }

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractDetails, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractDetails $contractDetails
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractDetails $contractDetails)
    {
        //
    }
}
