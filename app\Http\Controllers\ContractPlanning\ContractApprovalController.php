<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractApproval;

class ContractApprovalController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return void
     */
    public function index()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)

    {
        $isContractAboveThreshold = Contract::select('is_above_threshold')
            ->where('id', $request['contract_id'])
            ->get()
            ->implode('is_above_threshold');

        $isContractAboveThreshold ?
            $request['approval_type'] = 'npc' :
            $request['approval_type'] = 'award-authority';


        $files['contract_approval_form'] = $request['contract_approval_form'];

        unset($request['contract_approval_form']);


        try {
            $contractApproval = ContractApproval::create(array_only($request->all(), ContractApproval::$fields));

            foreach ($files as $fieldName => $FileContent) {
                if ($FileContent !== null) {
                    AttachmentController::saveFile($request, $contractApproval, $fieldName, $FileContent);
                }
            }

            return response()->json([], 201, [
                'location' => $contractApproval['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param $contractId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($contractId)
    {

        $contractApproval = ContractApproval::
        select('id', 'award_number', 'award_date', 'description', 'approval_type', 'contract_id')
            ->where('contract_id', $contractId)
            ->first();
        if (!$contractApproval) {
            return response()->json([], 404);
        }
        $attachments = $contractApproval->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
        foreach ($attachments as $key => $value) {
            $index = $attachments[$key]['field_name'];
            $att = [];
            foreach ($attachments as $k => $v) {
                if ($v['field_name'] == $index) {
                    array_push($att, $v);
                }
            }
            $contractApproval[$attachments[$key]['field_name']] = $att;
        }
        return response()->json($contractApproval, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param ContractApproval $contractApproval
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, ContractApproval $contractApproval)
    {
        $data = $request->all();
        $files['contract_approval_form'] = $data['contract_approval_form'];
        unset($data['contract_approval_form']);


        try {
            $contractApproval->update(array_only($data, ContractApproval::$fields));

            foreach ($files as $fieldName => $FileContent) {
                if ($FileContent !== null) {
                    AttachmentController::saveFile($request, $contractApproval, $fieldName, $FileContent);
                }
            }

            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ContractApproval $contractApproval
     * @return void
     */
    public function destroy(ContractApproval $contractApproval)
    {
        //
    }
}
