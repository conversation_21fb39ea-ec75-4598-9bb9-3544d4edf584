<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusCancelled extends Model
{

    protected $guarded = ['id'];
    public static $fields = [
        'cancelled_reason',
        'is_contractor_deprived',
        'contract_status_id'
    ];

    public function contract_status()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function contract_status_cancelled_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusCancelledLog');
    }
}
