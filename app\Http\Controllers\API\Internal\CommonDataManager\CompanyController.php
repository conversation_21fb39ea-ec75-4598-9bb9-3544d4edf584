<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\CommonDataManager;

use Illuminate\Http\Request;
use Mockery\Exception;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;

class CompanyController extends Controller
{

    public function searchCdm(Request $request)
    {

        $query = $request->query();
        $queryString = '';
        if (array_has($query, ['id']) && $query['id'] !== 'null' && $query['id']) {
            $queryString .= 'search[id][match]=full&';
            $queryString .= 'search[id][value]=' . $query['id'] . '&';
        }
        if (array_has($query, ['name_da']) && $query['name_da'] !== 'null' && $query['name_da']) {
            $queryString .= 'search[name_da][match]=partial&';
            $queryString .= 'search[name_da][value]=' . $query['name_da'] . '&';
        }

        if (array_has($query, ['name_pa']) && $query['name_pa'] !== 'null' && $query['name_pa']) {
            $queryString .= 'search[name_pa][match]=partial&';
            $queryString .= 'search[name_pa][value]=' . $query['name_pa'] . '&';
        }

        if (array_has($query, ['name_en']) && $query['name_en'] !== 'null' && $query['name_en']) {
            $queryString .= 'search[name_en][match]=partial&';
            $queryString .= 'search[name_en][value]=' . $query['name_en'] . '&';
        }

        if (array_has($query, ['licence_number']) && $query['licence_number'] !== 'null' && $query['licence_number']) {
            $queryString .= 'search[licence_number][match]=full&';
            $queryString .= 'search[licence_number][value]=' . $query['licence_number'] . '&';
        }

        if (array_has($query, ['tin']) && $query['tin'] !== 'null' && $query['tin']) {
            $queryString .= 'search[tin][match]=full&';
            $queryString .= 'search[tin][value]=' . $query['tin'] . '&';
        }

        if (strlen($queryString) > 0) {
            $queryString = '?' . substr($queryString, 0, strlen($queryString) - 1);
        } else {
            return Error::responseInsufficientParameters(new Exception('Insufficient parameters'));
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . '/api/specific/company' . $queryString);
        if ($remoteRequest->status_code > 400) {
            return Error::composeResponse(new Exception($remoteRequest->body));
        } else {
            return response()->json(json_decode($remoteRequest->body, true));
        }
    }

}
