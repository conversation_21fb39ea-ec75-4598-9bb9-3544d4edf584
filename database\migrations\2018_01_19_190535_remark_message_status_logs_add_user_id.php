<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemarkMessageStatusLogsAddUserId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('remark_message_status_logs', function (Blueprint $table) {
            $table->unsignedInteger('user_id')->after('remark_message_status_id');
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('remark_message_status_logs', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign('remark_message_status_logs_user_id_foreign');
            $table->dropColumn('user_id');
            Schema::enableForeignKeyConstraints();
        });
    }
}
