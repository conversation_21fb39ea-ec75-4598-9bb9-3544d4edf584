<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAmendmentValidPrecentageColumnToTimeAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->boolean('is_above_threshold')->after('amendment_id');
            $table->float('current_threshold_value')->after('is_above_threshold');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('time_amendments', 'is_above_threshold')) {
            Schema::table('time_amendments', function (Blueprint $table) {
                $table->dropColumn('is_above_threshold');
                $table->dropColumn('current_threshold_value');
            });
        }
    }
}
