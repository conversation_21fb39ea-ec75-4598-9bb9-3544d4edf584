<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetention extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'is_applicable'
    ];

    public function contract_retention_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRetentionLog');
    }

    public function contract_retention_applicable()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionApplicable');
    }

    public function contracts()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
