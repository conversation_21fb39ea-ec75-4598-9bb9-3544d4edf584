<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DelayPanaltyDecimalProblem extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_progress_delay_penalties', function (Blueprint $table) {
            $table->float('days_count', 8, 2)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_progress_delay_penalties', function (Blueprint $table) {
            $table->integer('days_count')->change();
        });
    }
}
