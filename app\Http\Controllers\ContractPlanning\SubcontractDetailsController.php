<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\SubcontractDetails;
use Illuminate\Http\Request;

class SubcontractDetailsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\SubcontractDetails $subcontractDetails
     * @return \Illuminate\Http\Response
     */
    public function show(SubcontractDetails $subcontractDetails)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\SubcontractDetails $subcontractDetails
     * @return \Illuminate\Http\Response
     */
    public function edit(SubcontractDetails $subcontractDetails)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\SubcontractDetails $subcontractDetails
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SubcontractDetails $subcontractDetails)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\SubcontractDetails $subcontractDetails
     * @return \Illuminate\Http\Response
     */
    public function destroy(SubcontractDetails $subcontractDetails)
    {
        //
    }
}
