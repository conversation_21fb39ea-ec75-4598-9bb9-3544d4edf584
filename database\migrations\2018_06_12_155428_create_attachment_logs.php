<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAttachmentLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attachment_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('original_name');
            $table->string('assigned_name');
            $table->string('stored_path');
            $table->string('field_name');
            $table->integer('log_foreign_key');
            $table->string('log_table_name');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attachment_logs');
    }
}
