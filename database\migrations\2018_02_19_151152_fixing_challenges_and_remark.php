<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingChallengesAndRemark extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('challenges_and_remarks', function (Blueprint $table) {
            $table
                ->renameColumn('suggested_solution', 'suggested_solution_procurement_entity')
                ->change();
            $table
                ->text('suggested_solution_company')
                ->after('suggested_solution')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('challenges_and_remarks', function (Blueprint $table) {
            $table->renameColumn('suggested_solution_procurement_entity', 'suggested_solution')->change();
            $table->dropColumn('suggested_solution_company');
        });
    }
}
