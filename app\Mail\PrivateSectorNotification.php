<?php

namespace NPA\ACPMS\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PrivateSectorNotification extends Mailable
{
    use Queueable, SerializesModels;
    public $npaIdentificationNo;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($npaContractIdentificationNo)
    {
        $this->npaIdentificationNo = $npaContractIdentificationNo;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.private-sector-notification')
            ->subject('NPA-ACPMS-Private Sector New Issue');
    }
}
