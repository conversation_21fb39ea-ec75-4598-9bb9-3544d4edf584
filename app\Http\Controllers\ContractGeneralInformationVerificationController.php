<?php

namespace NPA\ACPMS\Http\Controllers;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Models\ContractGeneralInformationVerification;

class ContractGeneralInformationVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $data['contract_id'] = $data['parent_id'];
            unset($data['parent_id']);
            $test = ContractGeneralInformationVerification::create(array_only($data, ContractGeneralInformationVerification::$fields));

            return response()->json($test, 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\ContractGeneralInformationVerification $contractGeneralInformationVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractGeneralInformationVerification)
    {
        $contractGeneralInformationVerification = ContractGeneralInformationVerification::where('contract_id', $contractGeneralInformationVerification)->first();
        return response()->json($contractGeneralInformationVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\ContractGeneralInformationVerification $contractGeneralInformationVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractGeneralInformationVerification $contractGeneralInformationVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\ContractGeneralInformationVerification $contractGeneralInformationVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractGeneralInformationVerification)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contractGeneralInformationVerification =
                ContractGeneralInformationVerification::where('contract_id', $contractGeneralInformationVerification)->first();
            $isUpdated = $contractGeneralInformationVerification->update(array_only($data, ContractGeneralInformationVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\ContractGeneralInformationVerification $contractGeneralInformationVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractGeneralInformationVerification $contractGeneralInformationVerification)
    {
        //
    }
}
