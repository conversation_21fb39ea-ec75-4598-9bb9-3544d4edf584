<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use Illuminate\Http\Request;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Subcontract;

class SubcontractApplicableController extends Controller
{

    public function store(Request $request)
    {

        try {
            $companyGeneralInformation = Subcontract::create(array_only($request->all(), Subcontract::$fields));

            return response()->json([], 201, [
                'location' => $companyGeneralInformation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

//    public function update(Request $request)
//    {
//        $data = $request->all();
//        $subcontractApplicable = SubcontractApplicable::where('id', $data['id'])->first();
//        if (array_key_exists('is_subcontract_applicable', $data)) {
//            if ($data['is_subcontract_applicable'] !== $subcontractApplicable['is_joint_venture']) {
//                $companies = $subcontractApplicable->companies();
//                $companies->delete();
//            }
//
//        } else {
//            $companyGeneralInformation = SubcontractApplicable::where('contract_id', $data['parent_id'])->first();
//            unset($data['parent_id']);
//        }
//        try {
//            $companyGeneralInformation->update($data);
//
//            return response()->json( [], 204, [
//                'location' => $companyGeneralInformation['id']
//            ]);
//        } catch (\Throwable $t) {
//            return Error::composeResponse($t);
//        }
//    }
}
