<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractRetention;
use NPA\ACPMS\Models\ContractRetentionApplicable;
use NPA\ACPMS\Models\ContractRetentionNotReturned;
use NPA\ACPMS\Models\ContractRetentionReturned;

class ContractRetentionController extends Controller
{
    private $files = [
        'security_repayment_document'
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();
            $retention = ContractRetention::create(array_only($data, ContractRetention::$fields));
            $retention = $retention->toArray();
            $retentionId = $retention['id'];
            if ($data['is_applicable']) {
                $data['contract_retention_id'] = $retentionId;
                $isApplicable = ContractRetentionApplicable::create(array_only($data, ContractRetentionApplicable::$fields));
                $isApplicable = $isApplicable->toArray();
                $isApplicableId = $isApplicable['id'];
                $data['contract_retention_applicable_id'] = $isApplicableId;
                if ($data['is_returned']) {
                    $contractRetentionReturned = ContractRetentionReturned::create(array_only($data, ContractRetentionReturned::$fields));
                    foreach ($files as $fieldName => $fileContent) {
                        if ($fileContent !== null) {
                            AttachmentController::saveFile($request, $contractRetentionReturned, $fieldName, $fileContent);
                        }
//                        if (is_array($files[$fieldName])) {
//                            if (array_key_exists('value', $files[$fieldName])) {
//                                AttachmentController::saveFile($request, $contractRetentionReturned, $fieldName, $fileContent);
//                            }
//                        }
                    }
                } else {
                    ContractRetentionNotReturned::create(array_only($data, ContractRetentionNotReturned::$fields));
                }
            }
            DB::commit();
            return response()->json([true], 201);
        } catch (\Throwable $t) {
            DB::rollback();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = [];
        $contract_id = $id;
        $data['contract_retention'] = ContractRetention::where('contract_id', $contract_id)->first();
        if(!isset($data['contract_retention']))
        {
            return response()->json([]);
        }
        if ($data['contract_retention']['is_applicable']) {
            $data['contract_retention_applicable'] = ContractRetentionApplicable::
            where('contract_retention_id', $data['contract_retention']['id'])->first();
            if ($data['contract_retention_applicable']['is_returned']) {
                $data['contract_retention_returned'] = ContractRetentionReturned::
                where('contract_retention_applicable_id', $data['contract_retention_applicable']['id'])->first();
                if (isset($data['contract_retention_returned'])) {
                    $attachments = $data['contract_retention_returned']->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
                    foreach ($attachments as $key => $value) {
                        $index = $attachments[$key]['field_name'];
                        $att = [];
                        foreach ($attachments as $k => $v) {
                            if ($v['field_name'] == $index) {
                                array_push($att, $v);
                            }
                        }
                        $data['contract_retention_returned'][$attachments[$key]['field_name']] = $att;
                    }
                }
            } else {
                $data['contract_retention_not_returned'] = ContractRetentionNotReturned::
                where('contract_retention_applicable_id', $data['contract_retention_applicable']['id'])->first();
            }
        }
        return response()->json($data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            $retention = ContractRetention::where('id', $id)->update(array_only($data, ContractRetention::$fields));
            if ($data['is_applicable']) {
                $data['contract_retention_id'] = $id;
                if (ContractRetentionApplicable::where('contract_retention_id', $data['contract_retention_id'])->exists()) {
                    ContractRetentionApplicable::where('contract_retention_id', $data['contract_retention_id'])
                        ->update(array_only($data, ContractRetentionApplicable::$fields));
                    $retention_applicable = ContractRetentionApplicable::where('contract_retention_id', $data['contract_retention_id'])->first();
                    $retention_applicable = $retention_applicable['id'];
                } else {
                    $retention_applicable = ContractRetentionApplicable::create(array_only($data, ContractRetentionApplicable::$fields));
                    $retention_applicable = $retention_applicable['id'];
                }
                if ($data['is_returned']) {
                    $data['contract_retention_applicable_id'] = $retention_applicable;
                    if (ContractRetentionReturned::where('contract_retention_applicable_id', $retention_applicable)->exists()) {
                        ContractRetentionReturned::where('contract_retention_applicable_id', $retention_applicable)->update(array_only($data, ContractRetentionReturned::$fields));
                    } else {
                        ContractRetentionReturned::create(array_only($data, ContractRetentionReturned::$fields));
                    }
                    $contractRetentionReturned = ContractRetentionReturned::where('contract_retention_applicable_id', $retention_applicable)->first();
                    $files = array_only($data, $this->files);
                    foreach ($files as $fieldName => $fileContent) {
                        if ($fileContent !== null) {
                            AttachmentController::saveFile($request, $contractRetentionReturned, $fieldName, $fileContent);
                        }
//                        if (is_array($files[$fieldName])) {
//                            if (array_key_exists('value', $files[$fieldName])) {
//                                AttachmentController::saveFile($request, $contractRetentionReturned, $fieldName, $fileContent);
//                            }
//                        }
                    }
                    if (ContractRetentionNotReturned::where('contract_retention_applicable_id', $retention_applicable)->exists()) {
                        ContractRetentionNotReturned::where('contract_retention_applicable_id', $retention_applicable)->delete();
                    }
                } else {
                    $data['contract_retention_applicable_id'] = $retention_applicable;
                    if (ContractRetentionReturned::where('contract_retention_applicable_id', $retention_applicable)->exists()) {
                        ContractRetentionReturned::where('contract_retention_applicable_id', $retention_applicable)->delete();
                    }
                    if (ContractRetentionNotReturned::where('contract_retention_applicable_id', $retention_applicable)->exists()) {
                        ContractRetentionNotReturned::where('contract_retention_applicable_id', $retention_applicable)->update(array_only($data, ContractRetentionNotReturned::$fields));
                    } else {
                        ContractRetentionNotReturned::create(array_only($data, ContractRetentionNotReturned::$fields));
                    }
                }
            } else {
                if (ContractRetentionApplicable::where('contract_retention_id', $id)->exists()) {
                    $contract_applicable = ContractRetentionApplicable::where('contract_retention_id', $retention)->first();
                    if (ContractRetentionReturned::where('contract_retention_applicable_id', $contract_applicable['id'])->exists()) {
                        ContractRetentionReturned::where('contract_retention_applicable_id', $contract_applicable['id'])->delete();
                    }
                    if (ContractRetentionNotReturned::where('contract_retention_applicable_id', $contract_applicable['id'])->exists()) {
                        ContractRetentionNotReturned::where('contract_retention_applicable_id', $contract_applicable['id'])->delete();
                    }
                    ContractRetentionApplicable::where('contract_retention_id', $retention)->delete();
                }
            }
            DB::commit();
            return response()->json([true], 201);
        } catch (\Throwable $t) {
            DB::rollback();
            return Error::composeResponse($t);
        }
        return response()->json($request->all());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
