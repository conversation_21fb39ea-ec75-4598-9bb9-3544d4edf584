<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractProgressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_progresses', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('contract_status')->nullable();
            $table->string('contract_status_slug')->nullable();
            $table->double('planned_payments_till_now')->nullable();
            $table->double('actual_payments_till_now')->nullable();
            $table->float('planned_physical_progress_till_now')->nullable();
            $table->float('actual_physical_progress_till_now')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_progresses');
    }
}
