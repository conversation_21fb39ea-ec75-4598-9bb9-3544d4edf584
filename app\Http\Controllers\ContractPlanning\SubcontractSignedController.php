<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Subcontract;
use NPA\ACPMS\Models\SubcontractApplicable;
use NPA\ACPMS\Models\SubcontractSigned;

class SubcontractSignedController extends Controller
{
    private $files = [
        'subcontract',
        'licence_document',
        'procurement_entity_written_agreement'
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['currency_type_id'] = $data['currency_type']['id'];
        unset($data['currency_type']);
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();
            $subcontract_id = Subcontract::where('contract_id', $data['contract_id'])->get()->toArray();
            $subcontract_applicable_id = SubcontractApplicable::where('subcontract_id', $subcontract_id[0]['id'])->get()->toArray();
            $data['subcontract_applicable_id'] = $subcontract_applicable_id[0]['id'];
            $subContractSigned = SubcontractSigned::create(array_only($data, SubcontractSigned::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if (is_array($files[$fieldName])) {
                    if (array_key_exists('value', $files[$fieldName])) {
                        AttachmentController::saveFile($request, $subContractSigned, $fieldName, $fileContent);
                    }
                }
            }
            DB::commit();
            return response()->json([], 204, [
                'location' => $subContractSigned['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $subcontract_id = Subcontract::where('contract_id', $id)->get()->toArray();
        if ($subcontract_id[0]['id']) {
            $subcontract_applicable_id = SubcontractApplicable::where('subcontract_id', $subcontract_id[0]['id'])->get()->toArray();
            if ($subcontract_applicable_id != []) {
                $subcontract_signed = SubcontractSigned::where('subcontract_applicable_id', $subcontract_applicable_id[0]['id'])->get();
                $queryString = '?search[vendors.id][match]=in&';
                foreach ($subcontract_signed as $signed) {
                    $queryString = $queryString . 'search[vendors.id][value][]=' . $signed['company_id'] . '&';
                }
                $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
                $remoteRequestData = json_decode($remoteRequest->body, true);
                foreach ($subcontract_signed as $signed) {
                    foreach ($remoteRequestData as $element) {
                        if ($signed['company_id'] == $element['id']) {
                            $signed['full_name'] = $element['full_name'];
                            // $signed['name_pa'] = $element['name_pa'];
                            // $signed['name_en'] = $element['name_en'];
                            $signed['license_number'] = $element['license_number'];
                            $signed['tin'] = $element['tin'];
                        }
                    }
                }
                foreach ($subcontract_signed as &$signed) {
                    $attachments = $signed->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
                    foreach ($attachments as $key => $value) {
                        $index = $attachments[$key]['field_name'];
                        $att = [];
                        foreach ($attachments as $k => $v) {
                            if ($v['field_name'] == $index) {
                                array_push($att, $v);
                            }
                        }
                        $signed[$attachments[$key]['field_name']] = $att;
                    }
                }
                return response()->json($subcontract_signed, 200);
            }
            return response()->json([], 200);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['currency_type_id'] = $data['currency_type']['id'];
        unset($data['currency_type']);
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();
            $subContractSigned = SubcontractSigned::where('id', $id)->update(array_only($data, SubcontractSigned::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if (is_array($files[$fieldName])) {
                    if (array_key_exists('value', $files[$fieldName])) {
                        AttachmentController::saveFile($request, $subContractSigned, $fieldName, $fileContent);
                    }
                }
            }
            DB::commit();
            return response()->json([], 204, [
                'location' => $subContractSigned['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function getSingedSubcontract($id)
    {
        $subcontract_signed = SubcontractSigned::where('id', $id)->first();
        $queryString = '?search[vendors.id][match]=in&';
        $queryString = $queryString . 'search[vendors.id][value][]=' . $subcontract_signed['company_id'] . '&';
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
        $remoteRequestData = json_decode($remoteRequest->body, true);

        foreach ($remoteRequestData as $element) {
            if ($subcontract_signed['company_id'] == $element['id']) {
                $subcontract_signed['full_name'] = $element['full_name'];
                // $subcontract_signed['name_pa'] = $element['name_pa'];
                // $subcontract_signed['name_en'] = $element['name_en'];
                $subcontract_signed['license_number'] = $element['license_number'];
                $subcontract_signed['tin'] = $element['tin'];
                $subcontract_signed['v_pin'] = $element['v_pin'];
            }
        }

        $attachments = $subcontract_signed->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
        foreach ($attachments as $key => $value) {
            $index = $attachments[$key]['field_name'];
            $att = [];
            foreach ($attachments as $k => $v) {
                if ($v['field_name'] == $index) {
                    array_push($att, $v);
                }
            }
            $subcontract_signed[$attachments[$key]['field_name']] = $att;
        }
        return response()->json($subcontract_signed, 200);
    }

}
