<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use <PERSON>rilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\AmendmentInContractTerms;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPP;
use NPA\ACPMS\Models\ContractRePlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;
use Illuminate\Support\Facades\Log;

class ContractRePlanningFinanceAffairsAndPhysicalProgressController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contract_id = $request->query('contract_id');
        $data = [];
        $data['total_contract_actual_value'] = 0;
        $rePlanningDateData = ContractRePlanningDateFAAPP::where('id', $request->query('id'))->first();
        $data['contract_re_plan_type'] = $rePlanningDateData['type'];
        $data['actual_payments'] = $rePlanningDateData->contract_re_plan;
        $timeAmendmentId = $this->getAmendmentTypeId('time-amendment');
        $timeAndCostAmendmentId = $this->getAmendmentTypeId('time-and-cost-amendment');
        $costAmendmentId = $this->getAmendmentTypeId('cost-amendment');
        $amendmentInContractTermsId = $this->getAmendmentTypeId('amendment-in-contract-conditions');
        if ($data['actual_payments']->count() > 0) {
            $contractLastAmendment = Amendment
                ::where('re_plan_id', $rePlanningDateData['id'])
                ->where('is_confirmed', 1)
                ->first();
            $amendment = Amendment
                ::where('contract_id', $contract_id)
                ->where('is_confirmed', 1)
                ->orderBy('id', 'asc')
                ->first();

            $data['planned_start_date'] = implode('-', jDateTime::
            toGregorian($data['actual_payments']->first()['year'], $data['actual_payments']->first()['month_id'], 1)
            );
            $contractDetails = ContractDetails::where('contract_id', $contract_id)
                ->first()
                ->toArray();

            if ($contractLastAmendment && in_array($contractLastAmendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId, $costAmendmentId])) {
                if (in_array($contractLastAmendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId])) {
                    $contractLastAmendment['type_id'] === $timeAmendmentId ?
                        $data['planned_end_date'] = TimeAmendment::where('amendment_id', $contractLastAmendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amended_end_date'] :
                        $data['planned_end_date'] = TimeAndCostAmendment::where('amendment_id', $contractLastAmendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amended_end_date'];
                } else if (in_array($contractLastAmendment['type_id'], [$costAmendmentId])) {
                    $data['planned_end_date'] = CostAmendment::where('amendment_id', $contractLastAmendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'];
                }
            } else if ($amendment && in_array($amendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId, $costAmendmentId, $amendmentInContractTermsId])) {
                if (in_array($amendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId])) {
                    $amendment['type_id'] === $timeAmendmentId ?
                        $data['planned_end_date'] = TimeAmendment::where('amendment_id', $amendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amended_end_date'] :
                        $data['planned_end_date'] = TimeAndCostAmendment::where('amendment_id', $amendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amended_end_date'];
                } else if (in_array($amendment['type_id'], [$costAmendmentId])) {
                    $data['planned_end_date'] = CostAmendment::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'];
                } else if (in_array($amendment['type_id'], [$amendmentInContractTermsId])) {
                    $data['planned_end_date'] = AmendmentInContractTerms::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'];
                }
            } else {
                $data['planned_end_date'] = $contractDetails['planned_end_date'];
            }

            $data['total_contract_actual_value'] = $contractDetails['actual_value'];

            $amendments = Amendment::where([
                ['contract_id', '=', $contract_id],
                ['is_confirmed', '=', 1]
            ])
                ->orWhere('id', $contractLastAmendment['id'])
                ->get();
            if ($amendments !== []) {
                foreach ($amendments as $amendment) {
                    in_array($amendment['type_id'], [$costAmendmentId, $timeAndCostAmendmentId]) ?
                        $amendment['type_id'] == $timeAndCostAmendmentId ?
                            $data['total_contract_actual_value'] += TimeAndCostAmendment::where('amendment_id', $amendment['id'])
                                ->orderBy('id', 'desc')
                                ->first()['amendment_amount'] :
                            $data['total_contract_actual_value'] += CostAmendment::where('amendment_id', $amendment['id'])
                                ->orderBy('id', 'desc')
                                ->first()['amendment_amount'] :
                        $data['total_contract_actual_value'];
                }
            }
        }
        try {
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $data = $request->all();
        $rePlanDateData = ContractRePlanningDateFAAPP::where('id', $data['contract_re_planning_date_f_a_a_p_p_id'])->first();

        $totalAmendedAndActualAmount = $this->calculateCostAndPhysicalProgressInAmendedRePlan(
            $rePlanDateData,
            $data,
            $rePlanDateData['contract_id']
        )['total_amount'];

        $rePlanPayment = ContractRePlanningFinanceAffairsAndPhysicalProgress
            ::where('contract_re_planning_date_f_a_a_p_p_id', $rePlanDateData['id'])
            ->get();
        $total['physical_progress_percentage'] = $rePlanPayment->sum('physical_progress_percentage') + $data['physical_progress_percentage'];
        $total['amount'] = $rePlanPayment->sum('amount') + $data['amount'];
        $data['status_id'] = $data['status']['id'];
        $data['month_id'] = $data['month']['id'];
        unset($data['status']);
        unset($data['month']);


        try {
            if ($total['physical_progress_percentage'] <= 100 && $data['amount'] <= $totalAmendedAndActualAmount) {
                $contractRePlan = ContractRePlanningFinanceAffairsAndPhysicalProgress
                    ::create(array_only($data, ContractRePlanningFinanceAffairsAndPhysicalProgress::$fields));
                return response()->json([], 201, [
                    'location' => $contractRePlan['id']
                ]);
            } else {
                throw new \Exception('input out of range');
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param $id
     * @return void
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $rePlanDateData = ContractRePlanningDateFAAPP::where('id', $data['contract_re_planning_date_f_a_a_p_p_id'])->first();

        $totalAmendedAndActualAmount = $this->calculateCostAndPhysicalProgressInAmendedRePlan(
            $rePlanDateData,
            $data,
            $rePlanDateData['contract_id']
        )['total_amount'];

        $rePlanPayment = ContractRePlanningFinanceAffairsAndPhysicalProgress
            ::where('contract_re_planning_date_f_a_a_p_p_id', $rePlanDateData['id'])
            ->get();

        $total['physical_progress_percentage'] = $rePlanPayment->where('id', '!=', $id)->sum('physical_progress_percentage') + $data['physical_progress_percentage'];
        $total['physical_progress_percentage'] = round($total['physical_progress_percentage'], 2);
        $total['amount'] = $rePlanPayment->sum('amount') + $data['amount'];
        $data['status_id'] = $data['status']['id'];
        $data['month_id'] = $data['month']['id'];
        unset($data['status']);
        unset($data['month']);

        try {
            if ($total['physical_progress_percentage'] <= 100 && $data['amount'] <= $totalAmendedAndActualAmount) {
                ContractRePlanningFinanceAffairsAndPhysicalProgress::where(['id' => $id])
                    ->update(array_only($data, ContractRePlanningFinanceAffairsAndPhysicalProgress::$fields));
                return response()->json([], 204);
            } else {
                throw new \Exception('input out of range');
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function calculateCostAndPhysicalProgressInAmendedRePlan($rePlanDate, $actualPayment, $contract_id)
    {

        $contractDetails = ContractDetails::
        where('contract_id', $contract_id)
            ->first()
            ->toArray();

        $contractLastAmendment = Amendment::where('contract_id', $contract_id)
            ->orderBy('id', 'desc')
            ->first();

        $timeAndCostAmendmentId = $this->getAmendmentTypeId('time-and-cost-amendment');
        $costAmendmentId = $this->getAmendmentTypeId('cost-amendment');

        if (in_array($contractLastAmendment['type_id'], [$timeAndCostAmendmentId, $costAmendmentId])) {
            $contractLastAmendment['type_id'] === $costAmendmentId ?
                $amendedAmount = CostAmendment::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amendment_amount'] :

                $amendedAmount = TimeAndCostAmendment::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amendment_amount'];

            $totalAmount = $contractDetails['actual_value'] + $amendedAmount;
            $calculatedPhysicalProgress = ($actualPayment['physical_progress_percentage'] * $contractDetails['actual_value']) / $totalAmount;

            return ['total_amount' => $totalAmount, 'physical_progress_percentage' => $calculatedPhysicalProgress];

        } else {
            return ['total_amount' => $contractDetails['actual_value'], 'physical_progress_percentage' => $actualPayment['physical_progress_percentage']];
        }

    }

    public function getAmendmentTypeId($slug)
    {
        return DropDowns::getElementTypeBySlug(DropDowns::getAllAmendmentType(), $slug)['id'];
    }

}
