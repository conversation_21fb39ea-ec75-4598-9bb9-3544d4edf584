import {RouterModule, Routes} from '@angular/router';
import {ProcurementEntityActionsComponent} from './procurement-entity-actions/procurement-entity-actions.component';
import {ChallengesAndRemarksComponent} from './challenges-and-remarks.component';
import {ProcurementEntityActionsDialogComponent} from './procurement-entity-actions/procurement-entity-actions-dialog/procurement-entity-actions-dialog.component';
import {ProcurementEntityViewDialogComponent} from './procurement-entity-actions/procurement-entity-view-dialog/procurement-entity-view-dialog.component';
import {NpaOtherCommentsComponent} from './npa-other-comments/npa-other-comments.component';
import {SpecialistMonitoringReportComponent} from './specialist-monitoring-report/specialist-monitoring-report.component';
import {SpecialistMonitoringReportListComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-list.component';
import {NpaOtherCommentAddDialogComponent} from './npa-other-comments/npa-other-comment-add-dialog/npa-other-comment-dialog.component';
import {ContractIntroductionComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/contract-introduction/contract-introduction.component';
import {NpaOtherCommentViewDialogComponent} from './npa-other-comments/npa-other-comment-view-dialog/npa-other-comment-view-dialog.component';
import {SpecialistMonitoringReportViewDialogComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-view-dialog/specialist-monitoring-report-view-dialog.component';
import {SpecialistMonitoringReportEditDialogComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-edit-dialog/specialist-monitoring-report-edit-dialog.component';
import {ContractorPerformanceReportComponent} from './contractor-performance-report/contractor-performance-report.component';
import {ContractorPerformanceReportListComponent} from './contractor-performance-report/contractor-performance-report-list/contractor-performance-report-list.component';
import {ContractorPerformanceReportViewDialogComponent} from './contractor-performance-report/contractor-performance-report-list/contractor-performance-report-view-dialog/contractor-performance-report-view-dialog.component';
import {ContractorPerformanceReportEditDialogComponent} from './contractor-performance-report/contractor-performance-report-list/contractor-performance-report-edit-dialog/contractor-performance-report-edit-dialog.component';
import {ConfirmationDialogComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/confirmation-dialog/confirmation-dialog.component';
import {MonitoringReportAddEditDialogComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/monitoring-report-add-edit-dialog/monitoring-report-add-edit-dialog.component';
import {MonitoringReportViewDialogComponent} from './specialist-monitoring-report/specialist-monitoring-report-list/monitoring-report-view-dialog/monitoring-report-view-dialog.component';

export const ContractChallengesAndRemarks = [
    ProcurementEntityActionsComponent,
    ChallengesAndRemarksComponent,
    ProcurementEntityActionsDialogComponent,
    ProcurementEntityViewDialogComponent,
    NpaOtherCommentsComponent,
    NpaOtherCommentAddDialogComponent,
    // Specialist monitoring report components moved to a separate module
    // ContractIntroductionComponent,
    NpaOtherCommentViewDialogComponent,
    // SpecialistMonitoringReportComponent,
    // SpecialistMonitoringReportListComponent,
    // SpecialistMonitoringReportViewDialogComponent,
    // SpecialistMonitoringReportEditDialogComponent,
    // ConfirmationDialogComponent,
    // MonitoringReportAddEditDialogComponent,
    // MonitoringReportViewDialogComponent,
    ContractorPerformanceReportComponent,
    ContractorPerformanceReportListComponent,
    ContractorPerformanceReportViewDialogComponent,
    ContractorPerformanceReportEditDialogComponent
];

const routes: Routes = [
    {
        path: '',
        redirectTo: 'procurement-entity-actions',
        pathMatch: 'full'
    },
    {
        path: 'procurement-entity-actions',
        component: ProcurementEntityActionsComponent,

    },
    {
        path: 'contractor-performance-report',
        component: ContractorPerformanceReportComponent,
        children: [
            {
                path: '',
                redirectTo: 'list',
                pathMatch: 'full'
            },
            {
                path: 'list',
                component: ContractorPerformanceReportListComponent,
            }
        ]
    }
];

export const ChallengesAndRemarksRoutes = RouterModule.forChild(routes);
