<?php

namespace NPA\ACPMS\Providers;

use Laravel\Passport\Passport;
use Carbon\Carbon;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        'NPA\ACPMS\Model' => 'NPA\ACPMS\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
        Passport::routes();

        Passport::tokensExpireIn(Carbon::now()->addSeconds(30));

        Passport::refreshTokensExpireIn(Carbon::now()->addDays(30));
    }
}
