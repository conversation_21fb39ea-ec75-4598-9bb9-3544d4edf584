<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsRemarkTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_remarks', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->unique('p_a_r_id');
            $table->boolean('is_agreement_based_on_monitoring_guideline_3_2_8')->nullable();
            $table->boolean('is_contract_started_based_on_conditions')->nullable();
            $table->boolean('is_p_p_p_based_on_agreement_and_plan')->nullable();
            $table->boolean('is_plan_payment_document_uploaded')->nullable();
            $table->boolean('is_p_payment_based_on_schedule_and_docs')->nullable();
            $table->boolean('is_advanced_payment_legally_funded')->nullable();
            $table->boolean('is_termination_cause_of_contractor_violation')->nullable();
            $table->boolean('is_contract_performance_security_rejected')->nullable();
            $table->boolean('is_contract_performance_security_uploaded')->nullable();
            $table->boolean('is_contract_info_and_documents_uploaded')->nullable();
            $table->boolean('is_p_e_got_action_on_suggestions')->nullable();
            $table->text('contract_agreement')->nullable();
            $table->text('actual_progress_start')->nullable();
            $table->text('physical_progress_plan')->nullable();
            $table->text('payments_plan')->nullable();
            $table->text('actual_physical_progress')->nullable();
            $table->text('actual_payments')->nullable();
            $table->text('performance_security')->nullable();
            $table->text('advanced_payments')->nullable();
            $table->text('advanced_payments_guaranty_legally_funded')->nullable();
            $table->text('subcontracts')->nullable();
            $table->text('amendments_reasons')->nullable();
            $table->text('contract_performance_security_renewal')->nullable();
            $table->text('contract_termination_reasons')->nullable();
            $table->text('contract_info_and_documents_uploaded_publicly')->nullable();
            $table->text('conclusion')->nullable();
            $table->text('old_suggestion')->nullable();
            $table->text('new_suggestion')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_remarks');
    }
}
