<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsRemark extends Model
{
    public static $fields = [
        'p_a_r_id',
        'is_performance_security_applicable',
        'contract_progress_remark',
        'is_performance_security_legally',
        'is_advance_payment_applicable',
        'is_advance_payment_legally',
        'is_agreement_based_on_monitoring_guideline_3_2_8',
        'is_termination_cause_of_contractor_violation',
        'is_contract_performance_security_rejected',
        'is_contract_performance_security_uploaded',
        'is_contract_info_and_documents_uploaded',
        'contract_agreement',
        'actual_progress_start',
        'payments_plan',
        'actual_physical_progress',
        'actual_payments',
        'performance_security',
        'advanced_payments_guaranty_legally_funded',
        'subcontracts',
        'amendments_reasons',
        'contract_performance_security_renewal',
        'contract_termination_reasons',
        'contract_info_and_documents_uploaded_publicly',
        'conclusion',
        'old_suggestion',
        'physical_progress_plan',
        'new_suggestion',
        'implemented_suggestions',
        'not_implemented_suggestions',
        'performance_security_percentage_change_reasons',
        'advance_payment_guaranty_expiration_date',
        'implemented_suggestions_vendor',
        'not_implemented_suggestions_vendor',
        'old_suggestions_vendor',
        'new_suggestions_vendor',
    ];

    protected $guarded = ['id'];
}
