<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractProgressDelayPenalty extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'days_count',
        'date',
        'remarks'
    ];

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

}
