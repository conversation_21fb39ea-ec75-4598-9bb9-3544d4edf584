<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ValidationContractsContractPlanningVerificationIsConfirmed extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec("
            create trigger validation_contracts_contract_planning_verification_is_confirmed
            before update
            on contracts
            for each row
            exit_label:begin
            
                declare is_confirmed boolean default null;
               
                select count(*)
                into is_confirmed 
                from contract_approval_verifications as cav
                join contract_approvals as ca
                    on ca.id = cav.contract_approval_id
                join contracts as c
                on c.id = ca.contract_id
                where c.id = new.id and cav.is_confirmed=1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed 
                from p_e_contact_person_verifications as pecpv
                where pecpv.contract_id = new.id and pecpv.is_confirmed=1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text ='|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed
                from domestic_contract_execution_verifications as dcev
                where dcev.contract_id = new.id and dcev.is_confirmed=1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed 
                from foreign_contract_execution_verifications as fcelv
                join foreign_contract_execution_locations as fcel
                    on fcel.id = fcelv.foreign_exec_id
                join contracts as c
                on c.id = fcel.contract_id
                where c.id = new.id and fcelv.is_confirmed=1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed 
                from contract_details_verifications as cdv
                join contract_details as cd
                    on cd.id = cdv.contract_detail_id
                join contracts as c
                on c.id = cd.contract_id
                where c.id = new.id and cdv.is_confirmed=1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed
                from company_general_informations as cgi
                where cgi.contract_id = new.id and cgi.is_confirmed = 1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
                
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed
                from subcontract_verifications as sv
                where sv.contract_id = new.id and sv.is_confirmed = 1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
            
                set is_confirmed = null;
                
                select count(*)
                into is_confirmed
                from contract_planning_f_a_a_p_p_verifications as cpfaappv
                where cpfaappv.contract_id = new.id and cpfaappv.is_confirmed = 1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed
                from liquidated_damages_delivery_verifications as lddv
                where lddv.contract_id = new.id and lddv.is_confirmed = 1;
                
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0001|||';
                end if;
            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getPdo()->exec('drop trigger validation_contracts_contract_planning_verification_is_confirmed');
    }
}
