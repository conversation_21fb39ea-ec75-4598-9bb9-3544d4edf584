<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAmendmentValidPrecentageColumnToCostAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->float('current_threshold_value')->after('is_above_threshold');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('cost_amendments', 'current_threshold_value')) {
            Schema::table('cost_amendments', function (Blueprint $table) {
                $table->dropColumn('current_threshold_value');
            });
        }
    }
}
