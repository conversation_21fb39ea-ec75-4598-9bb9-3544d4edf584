<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ForeignContractExecutionVerification extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'is_approved',
        'is_confirmed',
        'foreign_exec_id'
    ];

    public function foreign_contract_execution_location()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ForeignContractExecutionLocation');
    }
}
