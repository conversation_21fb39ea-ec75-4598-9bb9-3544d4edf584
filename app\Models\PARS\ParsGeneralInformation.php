<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsGeneralInformation extends Model
{
    public static $fields = [
        'progress_analysis_reports_id',
        'contract_name',
        'contract_number',
        'currency_name',
        'procurement_method',
        'selection_method',
        'contract_type',
        'procurement_type',
        'contract_value_with_amendments',
        'contract_durations_with_amendments',
        'sector',
        'procurement_entity',
        'award_number',
        'award_date',
        'agreement_signature_date',
        'liquidated_damages_period',
        'after_delivery_service_period',
        'progress_cancelled_reasons'
    ];

    protected $guarded = ['id'];

    public function budget()
    {
        return $this->hasOne('NPA\ACPMS\Models\PARS\ParsContractBudgetCode', 'pars_general_informations_id');
    }

    public function donors()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsContractDonor', 'pars_general_informations_id');
    }

    public function contract_execution_locations()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsContractExecutionLocation', 'pars_general_info_id');
    }

    public function vendors()
    {
        return $this->hasMany('NPA\ACPMS\Models\PARS\ParsVendor', 'pars_general_informations_id');
    }
}
