<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixesContractStatusDuplicationAndUniqueKey extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::select('
        DELETE c1 FROM 
        contract_statuses as c1
        inner join contract_statuses as c2
        where c1.id < c2.id and c1.contract_id = c2.contract_id
        ');
        Schema::table('contract_statuses', function (Blueprint $table) {
            $table->unique([
                'contract_id'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_statuses', function (Blueprint $table) {
            $table->dropUnique('contract_statuses_contract_id_unique');
        });
    }
}
