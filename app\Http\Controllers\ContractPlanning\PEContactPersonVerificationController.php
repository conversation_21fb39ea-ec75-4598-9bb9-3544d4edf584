<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\PEContactPersonVerification;
use Illuminate\Http\Request;

class PEContactPersonVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $getData = Contract::find($data['contract_id'])
                ->p_e_contact_person_verification()
                ->create(array_only($data, PEContactPersonVerification::$fields));
            return response()->json($getData, 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\PEContactPersonVerification $pEContactPersonVerification
     * @return \Illuminate\Http\Response
     */
    public function show($pEContactPersonVerification)
    {
        $contactPersonsVerification = PEContactPersonVerification::where('contract_id', $pEContactPersonVerification)->first();
        return response()->json($contactPersonsVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\PEContactPersonVerification $pEContactPersonVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(PEContactPersonVerification $pEContactPersonVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\PEContactPersonVerification $pEContactPersonVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractId)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contactPersonsVerification =
                PEContactPersonVerification::where('contract_id', $contractId)->first();
            $isUpdated = $contactPersonsVerification->update(array_only($data, PEContactPersonVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\PEContactPersonVerification $pEContactPersonVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(PEContactPersonVerification $pEContactPersonVerification)
    {
        //
    }
}
