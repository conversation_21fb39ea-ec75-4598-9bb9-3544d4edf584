<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixExchangeRateTypeIssue extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::select('
            update contracts 
            set exchange_rate = -1
            where  exchange_rate is null
        ');
        Schema::table('contracts', function (Blueprint $table) {
            $table->float('exchange_rate')->default(null)->change();
        });
        DB::select('
            update contracts 
            set exchange_rate = null
            where  exchange_rate = -1
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
