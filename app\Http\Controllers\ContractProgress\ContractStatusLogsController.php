<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\AttachmentLog;
use NPA\ACPMS\Models\ContractStatusLog;

class ContractStatusLogsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request['contract_id'];
        $data = ContractStatusLog::where('contract_id', $contractId)->get();
        return response()->json($data, 200);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $data = array();
        try {
            $contract_statuses_logs = ContractStatusLog
                ::with(
                    'contract_status_cancelled_log',
                    'contractor_performance_assessment_log',
                    'contract_status_suspended_log',
                    'contract_status_not_signed_log',
                    'contract_retention_log',
                    'contract_retention_log.contract_retention_applicable_log',
                    'contract_retention_log.contract_retention_return_log',
                    'contract_retention_log.contract_retention_not_return_log')
                ->where('id', $id)
                ->first();
            if ($contract_statuses_logs && isset($contract_statuses_logs)) {
                $data = $contract_statuses_logs;
                $attachments = AttachmentLog::select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')
                    ->where('log_foreign_key', $data['id'])
                    ->where('log_table_name', 'contract_status_logs')
                    ->get();
                foreach ($attachments as $key => $value) {
                    $index = $attachments[$key]['field_name'];
                    $att = [];
                    foreach ($attachments as $k => $v) {
                        if ($v['field_name'] == $index) {
                            array_push($att, $v);
                        }
                    }
                    $data[$attachments[$key]['field_name']] = $att;
                }
                if ($data['contract_retention_log']['contract_retention_applicable_log']['is_returned'] === 1) {
                    $attachments = AttachmentLog::select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')
                        ->where('log_foreign_key', $data['contract_retention_log']['contract_retention_return_log']['id'])
                        ->where('log_table_name', 'contract_retention_returned_logs')
                        ->get();
                    foreach ($attachments as $key => $value) {
                        $index = $attachments[$key]['field_name'];
                        $att = [];
                        foreach ($attachments as $k => $v) {
                            if ($v['field_name'] == $index) {
                                array_push($att, $v);
                            }
                        }
                        $data[$attachments[$key]['field_name']] = $att;
                    }
                }
            }
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
