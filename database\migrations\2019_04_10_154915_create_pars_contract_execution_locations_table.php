<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractExecutionLocationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_execution_locations', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('pars_general_info_id');
            $table->foreign('pars_general_info_id')
                ->references('id')
                ->on('pars_general_informations')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('zone')->nullable();
            $table->string('province')->nullable();
            $table->string('district')->nullable();
            $table->string('village')->nullable();
            $table->string('climate_situation')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_execution_locations');
    }
}
