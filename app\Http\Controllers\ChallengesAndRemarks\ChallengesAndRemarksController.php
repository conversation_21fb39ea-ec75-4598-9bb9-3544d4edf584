<?php

namespace NPA\ACPMS\Http\Controllers\ChallengesAndRemarks;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\ArrayAndObject;
use NPA\ACPMS\Helpers\EmailService;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Http\Controllers\API\Internal\CommonDataManager\DropDowns\DropDownsController;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\ChallengesAndRemarks;
use NPA\ACPMS\Models\Contract;


class ChallengesAndRemarksController extends Controller
{
    private $files = ['document'];

    public function store(Request $request)
    {


        $data = $request->all();
        $files = array_only($data, $this->files);

        $data['category_id'] = $data['category']['id'];

        $data['status_id'] = $data['status']['id'];
        if( $data['status']['slug'] === 'closed') {
            $data['resolution_id'] = $data['resolution']['id'];
            $data['end_date'] = $data['end_date'] !== 'undefined' && $data['end_date'] ? $data['end_date'] : null;
        }
       


        try {
            DB::beginTransaction();
            $challengesAndRemarks = ChallengesAndRemarks::create(array_only($data, ChallengesAndRemarks::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $challengesAndRemarks, $fieldName, $fileContent);
                }
            }

            // commented out for now
                // $contract = Contract::find($data['contract_id']);

                // $npaIdentificationNo = $contract['npa_identification_number'];
                // $sectorId = $contract['sector_id'];
                // $cpmManagers = Helper::getCpmManager($sectorId);
                // forEach ($cpmManagers as $manager) {
                //     $email = $manager->email;
                //     EmailService::send($email, $npaIdentificationNo);
                // }

                //TODO: send session to usm
                // $specialist = Helper::getContractSpecialist($data['contract_id']);

                // if (isset($specialist) && $specialist->email) {
                //     $email = $specialist->email;
                //     EmailService::send($email, $npaIdentificationNo);
                // }


            $document = $attachments = Attachment::where('foreign_key', (int)$challengesAndRemarks['id'])
                ->where('table_name', 'challenges_and_remarks')->get();
            DB::commit();
//            return response()->json('we are here',$challengesAndRemarks);
            return response()->json($document, 201, [
                'location' => $challengesAndRemarks['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {
        $challengesAndRemarksRaw = ChallengesAndRemarks::where('contract_id', $contractId)
            ->get();

        if (!$challengesAndRemarksRaw) {
            return response()->json([], 404);
        }
        $challengesAndRemarks = collect($challengesAndRemarksRaw)->toArray();

        $ids = [];
        $challengesAndRemark['creator'] = [];
        foreach ($challengesAndRemarks as $challengesAndRemark) {
            array_push($ids, $challengesAndRemark['creator_id']);
        }

        $roleList = Helper::getRolesByIds($ids);
        $temp = [];
        foreach ($challengesAndRemarks as $k => $challengesAndRemark) {
            foreach ($roleList as $role) {
                if ($role['id'] == $challengesAndRemark['creator_id']) {
                    $challengesAndRemark['creator'] = [];
                    $challengesAndRemark['creator']['id'] = $role['id'];
                    $challengesAndRemark['creator']['creator_role_name_en'] = $role['name'];
                    $challengesAndRemark['creator']['creator_role_name_da'] = $role['name'];
                }
            }

            $attachments = $challengesAndRemarksRaw[$k]
                ->attachments()
                ->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')
                ->where('field_name', 'document')
                ->where('table_name', 'challenges_and_remarks')
                ->get();
            if (!$attachments) {
                continue;
            }
            $attachments = $attachments->toArray();

            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index) {
                        array_push($att, $v);
                    }
                }
                $challengesAndRemark[$attachments[$key]['field_name']] = $att;
            }
            array_push($temp, $challengesAndRemark);
        }
        return response()->json($temp, 200);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $query = $request->all();
        $files = array_only($query, $this->files);
        $currentDate = Carbon::now()->toDateTimeString();
        try {
            if (isset($query['is_published']) && $query['is_published']) {
                ChallengesAndRemarks::where('id', $query['id'])
                    ->update(
                        [
                            'is_approved' => $query['is_approved'],
                            'is_confirmed' => $query['is_confirmed'],
                            'is_published' => $query['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
            } else if (isset($query['is_confirmed'])) {

                ChallengesAndRemarks::where('id', $query['id'])
                    ->update(
                        [
                            'is_approved' => $query['is_approved'],
                            'is_confirmed' => $query['is_confirmed'],
                            'is_published' => $query['is_published']
                        ]
                    );
            } else {
                $query['category_id'] = $query['category']['id'];
                unset($query['category']);

                $query['status_id'] = $query['status']['id'];
                unset($query['status']);

                $query['resolution_id'] = $query['resolution']['id'];
                unset($query['resolution']);


//                unset($query['document']);

                $rowsCount = ChallengesAndRemarks::where('id', $id)->update(
                    array_except(array_only($query, ChallengesAndRemarks::$fields), 'creator_id')
                );

                $challengesAndRemarks = ChallengesAndRemarks::where('id', $id)->first();

                foreach ($files as $fieldName => $fileContent) {
                    if ($fileContent !== null) {
                        AttachmentController::saveFile($request, $challengesAndRemarks, $fieldName, $fileContent);
                    }
                }

                if ($rowsCount < 1) {
                    throw Error::exceptionNotFound();
                }
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function confirm(Request $request)
    {
        $id = $request->input('id');
        try {
            $npaChallengesAndRemarks = ChallengesAndRemarks::find($id);
            $npaChallengesAndRemarks->is_confirmed = true;
            $npaChallengesAndRemarks->save();
            if ($npaChallengesAndRemarks) {
                return response()->json([]);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function approve(Request $request)
    {
        $id = $request->input('id');
        try {
            $npaChallengesAndRemarks = ChallengesAndRemarks::find($id);
            $npaChallengesAndRemarks->is_approved = true;
            $npaChallengesAndRemarks->save();
            if ($npaChallengesAndRemarks) {
                return response()->json([]);
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
