<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionNotReturned extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'reason',
        'contract_retention_applicable_id'
    ];

    public function contract_retention_applicable()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionApplicable');
    }

    public function contract_retention_not_returned_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRetentionNotReturnedLog');
    }
}
