<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;

class AlterOcdsTriggersOnDataPublish extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_planning_data_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_contract_planning_data_publish`
            after update on `contracts`
            for each row
            exit_label:begin

                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;

                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (new.id, new.npa_identification_number, now());
                
            end
        ");
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_payments_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_contract_payments_publish`
            after update on `contract_progress_payments`
            for each row
            exit_label:begin
            
                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;

                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (new.contract_id, 
                (select npa_identification_number 
                    from contracts 
                    where id = new.contract_id)
                , now());

            end
        ");
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_re_plans_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_re_plans_publish`
            after update on `contract_re_planning_date_f_a_a_p_p_verifications`
            for each row
            exit_label:begin
            declare var_contract_id integer default null;
            
            select contract_id into var_contract_id
                    from contract_re_planning_date_f_a_a_p_ps 
                    where id = new.contract_re_planning_date_f_a_a_p_p_id;
                    
                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;
                
                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (var_contract_id
                , (select npa_identification_number 
                    from contracts 
                    where id = var_contract_id)
                ,now());

            end
        ");
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_amendments_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_amendments_publish`
            after update on `amendments`
            for each row
            exit_label:begin

                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;
                
                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (new.contract_id
                , (select npa_identification_number 
                    from contracts 
                    where id = new.contract_id)
                ,now());

            end
        ");
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_challenges_and_remarks_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_challenges_and_remarks_publish`
            after update on `challenges_and_remarks`
            for each row
            exit_label:begin

                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;
                
                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (new.contract_id
                 , (select npa_identification_number 
                    from contracts 
                    where id = new.contract_id)
                , now());
            end
        ");
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_statuses_publish`');
        DB::connection()->getPdo()->exec("
            create trigger `ocds_trigger_on_contract_statuses_publish`
            after update on `contract_status_verifications`
            for each row
            exit_label:begin
            declare var_contract_id int default null;
            select contract_id into var_contract_id
                    from contract_statuses 
                    where id = new.contract_status_id;

                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;
                
                insert into ocds_sync_contract_data_on_published_logs
                (contract_id, npa_identification_number, created_at)
                values
                (var_contract_id
                 ,(select npa_identification_number 
                    from contracts 
                    where id = var_contract_id)
                , now());

            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_planning_data_publish`');
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_payments_publish`');
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_re_plans_publish`');
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_amendments_publish`');
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_challenges_and_remarks_publish`');
        DB::connection()->getPdo()->exec('drop trigger if exists `ocds_trigger_on_contract_statuses_publish`');

    }
}
