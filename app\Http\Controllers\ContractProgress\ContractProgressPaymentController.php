<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\AmendmentInContractTerms;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractProgressPaymentAfterDueDate;
use NPA\ACPMS\Models\ContractProgressPaymentDelay;
use NPA\ACPMS\Models\ContractProgressVerification;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPP;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ContractProgressPaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');

        $contractLastAmendment = Amendment::where('contract_id', $contractId)
            ->where('is_confirmed', 1)
            ->orderBy('id', 'desc')
            ->first();

        $timeAmendmentId = $this->getAmendmentTypeId('time-amendment');
        $timeAndCostAmendmentId = $this->getAmendmentTypeId('time-and-cost-amendment');
        $costAmendmentId = $this->getAmendmentTypeId('cost-amendment');
        $amendmentInContractTermsId = $this->getAmendmentTypeId('amendment-in-contract-conditions');

        $payments = ContractProgressPayment
            ::where('contract_id', $contractId)
            ->orderBy('year', 'month_id')
            ->get();
        $payments = $payments ? $payments->toArray() : [];
        $contractProgress = ContractProgress::where('contract_id', $contractId)->first();

        $isContractProgressConfirmed = false;
        if ($contractProgress) {
            $contractProgressVerification = ContractProgressVerification::where('contract_progress_id', $contractProgress['id'])->first();
            $isContractProgressConfirmed = $contractProgressVerification && $contractProgressVerification['is_confirmed'] ? true : false;
        }

        $contractPlanning = ContractDetails::select('planned_end_date', 'actual_value')->where('contract_id', $contractId)->first();

        $paymentAfterDueDate = ContractProgressPaymentAfterDueDate::where('contract_id', $contractId)->first();
        if ($paymentAfterDueDate) {
            array_push($payments, $paymentAfterDueDate->toArray());
        }

        $amendmentData = null;
        if ($contractLastAmendment && in_array($contractLastAmendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId, $costAmendmentId, $amendmentInContractTermsId])) {
            if (in_array($contractLastAmendment['type_id'], [$timeAmendmentId, $timeAndCostAmendmentId])) {
                $contractLastAmendment['type_id'] === $timeAmendmentId ?
                    $amendmentData['amended_end_date'] = TimeAmendment::where('amendment_id', $contractLastAmendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'] :
                    $amendmentData['amended_end_date'] = TimeAndCostAmendment::where('amendment_id', $contractLastAmendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'];
            } else if (in_array($contractLastAmendment['type_id'], [$costAmendmentId])) {
                $amendmentData['amended_end_date'] = CostAmendment::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amended_end_date'];
            } else if (in_array($contractLastAmendment['type_id'], [$amendmentInContractTermsId])) {
                $amendmentData['amended_end_date'] = AmendmentInContractTerms::where('amendment_id', $contractLastAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first()['amended_end_date'];
            }

        }

        $amendments = Amendment::where([
            ['contract_id', '=', $contractId],
            ['is_confirmed', '=', 1]
        ])
            ->get();
        $ContractRePlanningDateFAAPP = ContractRePlanningDateFAAPP::where('contract_id', '=', $contractId)->first();
        $amendmentData['amendment_amount'] = 0;
        if ($amendments !== []) {
            foreach ($amendments as $amendment) {
                in_array($amendment['type_id'], [$costAmendmentId, $timeAndCostAmendmentId]) ?
                    $amendment['type_id'] == $timeAndCostAmendmentId ?
                        $amendmentData['amendment_amount'] += TimeAndCostAmendment::where('amendment_id', $amendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amendment_amount'] :
                        $amendmentData['amendment_amount'] += CostAmendment::where('amendment_id', $amendment['id'])
                            ->orderBy('id', 'desc')
                            ->first()['amendment_amount'] :
                    $amendmentData['amendment_amount'];
                    $amendmentData['is_amendment_re_plan_confirmed'] = $ContractRePlanningDateFAAPP && $ContractRePlanningDateFAAPP->contract_re_plan_verification ? !!$ContractRePlanningDateFAAPP->contract_re_plan_verification->is_confirmed : false;
            }
        }

        return response()->json([
            'payments' => $payments,
            'contract_progress' => $contractProgress,
            'contract_planning' => $contractPlanning,
            'amendment' => $amendmentData,
            'is_contract_progress_confirmed' => $isContractProgressConfirmed
        ]);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->all();

            // Doing the following type checks, since verification uses the same function

            if (array_has($data, 'status')) {
                $data['status_id'] = $data['status']['id'];
            }
            if (array_has($data, 'month')) {
                $data['month_id'] = $data['month']['id'];
            }
            $files['contract_M16_payment_form'] = $data['contract_M16_payment_form'];
            $files['physical_progress_report'] = $data['physical_progress_report'];
            unset($data['contract_M16_payment_form']);
            unset($data['physical_progress_report']);

            $contractProgressPayment = ContractProgressPayment::create(array_only($data, ContractProgressPayment::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractProgressPayment, $fieldName, $fileContent);
                }
            }

            DB::commit();
            return response()->json([], 201, [
                'location' => $contractProgressPayment['id']
            ]);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        try {
            $data = $request->all();
            $currentDate = Carbon::now()->toDateTimeString();

            if (isset($data['is_published']) && $data['is_published']) {
                ContractProgressPayment::where('id', $data['id'])
                    ->update(
                        [
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
                return response()->json([], 204);
            }

            if (isset($data['is_confirmed'])) {
                ContractProgressPayment::where('id', $data['id'])
                    ->update(
                        [
                            'is_confirmed' => $data['is_confirmed'],
                            'is_approved' => $data['is_approved'],
                            'is_published' => $data['is_published'],
                        ]
                    );
                return response()->json([], 204);
            }

            // Doing the following type checks, since verification uses the same function
            if (array_has($data, 'status')) {
                $data['status_id'] = $data['status']['id'];
                unset($data['status']);
            }
            if (array_has($data, 'month')) {
                $data['month_id'] = $data['month']['id'];
                unset($data['month']);
            }
            $files['contract_M16_payment_form'] = $data['contract_M16_payment_form'];
            $files['physical_progress_report'] = $data['physical_progress_report'];
            unset($data['contract_M16_payment_form']);
            unset($data['physical_progress_report']);
            DB::beginTransaction();
            $contractProgressPayment = ContractProgressPayment::where('id', $id)->first();
            ContractProgressPayment::where('id', $id)->update(array_only($data, ContractProgressPayment::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractProgressPayment, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 204);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Store a newly created payment after due date resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeAfterDueDate(Request $request)
    {
        try {
            $data = $request->all();

            // Doing the following type checks, since verification uses the same function

            if (array_has($data, 'month')) {
                $data['month_id'] = $data['month']['id'];
            }
            $files['contract_M16_payment_form'] = $data['contract_M16_payment_form'];
            $contractProgressPaymentAfterDueDate = ContractProgressPaymentAfterDueDate::create(array_only($data, ContractProgressPaymentAfterDueDate::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractProgressPaymentAfterDueDate, $fieldName, $fileContent);
                }
            }
            return response()->json([], 201, [
                'location' => $contractProgressPaymentAfterDueDate['$id']
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Update the specified payment after due date resource in storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAfterDueDate(Request $request, $id)
    {
        try {
            $data = $request->all();

            $currentDate = Carbon::now()->toDateTimeString();
            if (isset($data['is_published']) && $data['is_published']) {
                ContractProgressPaymentAfterDueDate::where('id', $data['id'])
                    ->update(
                        [
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
                return response()->json([], 204);
            }

            if (isset($data['is_confirmed'])) {
                ContractProgressPaymentAfterDueDate::where('id', $id)
                    ->update(
                        [
                            'is_confirmed' => $data['is_confirmed'],
                            'is_approved' => $data['is_approved'],
                            'is_published' => $data['is_published'],
                        ]
                    );
                return response()->json([], 204);
            }
            // Doing the following type checks, since verification uses the same function
            if (array_has($data, 'month')) {
                $data['month_id'] = $data['month']['id'];
            }
            $files['contract_M16_payment_form'] = $data['contract_M16_payment_form'];
            $paymentAfterDueDate = ContractProgressPaymentAfterDueDate::where('id', $data['id'])->first();
            ContractProgressPaymentAfterDueDate
                ::where('id', $id)
                ->update(array_only($data, ContractProgressPaymentAfterDueDate::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $paymentAfterDueDate, $fieldName, $fileContent);
                }
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function showPaymentAfterDueDate($id)
    {
        $paymentAfterDueDate = ContractProgressPaymentAfterDueDate
            ::where('id', $id)
            ->first();

        if ($paymentAfterDueDate) {
            $attachments = $paymentAfterDueDate->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index && $v['table_name'] == 'contract_progress_payment_after_due_dates') {
                        array_push($att, $v);
                    }
                }
                $paymentAfterDueDate[$attachments[$key]['field_name']] = $att;
            }
        }
        return response()->json($paymentAfterDueDate);

    }


    public function getAllAmendmentType()
    {
        $path = 'api/dropDown/amendmentType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function getAmendmentTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->getAllAmendmentType(), $slug)['id'];
    }

    public function show($id)
    {
        $payments = ContractProgressPayment
            ::where('id', $id)
            ->orderBy('year', 'month_id')
            ->get();

        foreach ($payments as $payment) {
            $attachments = $payment->contract_progress_payment_attachment()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index) {
                        array_push($att, $v);
                    }
                }
                $payment[$attachments[$key]['field_name']] = $att;
            }
        }
        return response()->json($payments);

    }

    public function destroy($id)
    {
        try {
            ContractProgressPaymentAfterDueDate::where('id', $id)->delete();
            return response()->json([], 204);
        } catch (\Throwable $st) {
            return Error::composeResponse($st);
        }
    }

    public function isAdvancePaymentApplicable(Request $request)
    {
        $contractId = $request->id;
        $contractDetails = ContractDetails::where('contract_id', $contractId)->first();
        if (is_null($contractDetails['is_advance_payment_applicable'])) {
            return response()->json([], 204);
        }
        return response()->json($contractDetails['is_advance_payment_applicable']);
    }
}
