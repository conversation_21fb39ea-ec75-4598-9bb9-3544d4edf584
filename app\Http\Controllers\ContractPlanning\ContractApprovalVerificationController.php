<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractApprovalVerification;

class ContractApprovalVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_approval_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contractApprovalVerification = ContractApprovalVerification::Create(array_only($data, ContractApprovalVerification::$fields));
            return response()->json($contractApprovalVerification, 201, [
                'location' => $contractApprovalVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $contractApprovalId
     * @return \Illuminate\Http\Response
     */
    public function show($contractApprovalId)
    {
        $contractApprovalVerification = ContractApprovalVerification::where('contract_approval_id', $contractApprovalId)->first();
        return response()->json($contractApprovalVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\ContractApprovalVerification $contractApprovalVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractApprovalVerification $contractApprovalVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['contract_approval_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contractApprovalVerification = ContractApprovalVerification::where('contract_approval_id', $data['contract_approval_id'])->first();
            $contractApprovalVerification->update(array_only($data, ContractApprovalVerification::$fields));
            return response()->json([], 204, [
                'location' => $contractApprovalVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ContractApprovalVerification $contractApprovalVerification
     * @return void
     */
    public function destroy(ContractApprovalVerification $contractApprovalVerification)
    {
        //
    }
}
