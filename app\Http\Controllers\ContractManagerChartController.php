<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Filter;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Role;

class ContractManagerChartController extends Controller
{
    public function index(Request $request)
    {
        $contractManagerId = auth('api')->user()->id;
        $roleName = Role::find(auth('api')->user()->role_id)['name'];
        $contractManagerIdCondition = ' ';
        if ($roleName === 'contract-manager') {
            $contractManagerIdCondition = ' and c.contract_manager_id = ' . $contractManagerId . ' ';
        } elseif ($roleName !== 'cpmd-manager' && $roleName !== 'cpmd-system-development') {
            return response()->json([], 404);
        }
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $queryString = Filter::prepareQueryStrings($request->query());
        $data['widgets'] = $this->widgets($contractManagerIdCondition, $queryString, $contractStatuses);
        $data['transferred_contracts_status_to_contract_manager_bar_chart'] = $this->transferredContractsStatusToContractManagerBarChart($contractManagerIdCondition, $queryString);
        $data['contract_manager_workload_percentage'] = $this->contractManagerWorkloadPercentage($contractManagerIdCondition, $queryString, $contractManagerId);
        $data['contract_documents_upload'] = $this->contractsDocumentsUpload($contractManagerIdCondition, $queryString, $procurementTypes);
        $data['documents_upload_consultancy_services'] = $this->documentsUploadConsultancyServices($contractManagerIdCondition, $queryString, $procurementTypes);
        $data['contracts_count_based_on_status_chart'] = $this->contractsCountBasedOnStatusChart($contractManagerIdCondition, $queryString);
        $data['contracts_value_based_on_status_chart'] = $this->contractsValueBasedOnStatusChart($contractManagerIdCondition, $queryString);
        $data['payments_percentage_chart'] = $this->paymentPercentageChart($contractManagerIdCondition, $queryString);
        $data['contract_manager_number_of_contracts_based_on_challenges'] = $this->contractManagerNumberOfContractsBasedOnChallenges($contractManagerIdCondition, $queryString);
        $data['work_in_progress_contract_payments_volume_chart'] = $this->workInProgressContractPaymentsVolumeChart($contractManagerIdCondition, $queryString, $contractStatuses);
        $data['work_in_progress_contract_physical_progress_volume_chart'] = $this->workInProgressContractPhysicalProgressVolumeChart($contractManagerIdCondition, $queryString, $contractStatuses);
        $data['volume_of_completed_contracts'] = $this->volumeOfCompletedContracts($contractManagerIdCondition, $queryString, $contractStatuses);
        $data['contracts_count_based_on_contract_manager'] = $this->contractsCountBasedOnContractManager($contractManagerIdCondition, $queryString);
        $data['contract_count_based_on_procurement_method'] = $this->contractsCountBasedOnProcurementMethod($contractManagerIdCondition, $queryString);
        $data['number_of_amendments_in_contracts'] = $this->numberOfAmendmentsInContracts($contractManagerIdCondition, $queryString);
        $data['amendment_percentage_in_contracts'] = $this->amendmentPercentageInContracts($contractManagerIdCondition, $queryString, $amendmentTypes);
        $data['contracts_value_based_donors'] = $this->contractsValueBasedDonors($contractManagerIdCondition, $queryString);
        $data['contracts_number_based_donors'] = $this->contractsNumberBasedDonors($contractManagerIdCondition, $queryString);
        $data['number_and_value_of_contract_based_on_procurement_type'] = $this->numberAndValueOfContractBasedOnProcurementType($contractManagerIdCondition, $queryString);
        $data['contracts_count_based_on_selection_method_in_consultancy'] = $this->contractsCountBasedOnSelectionMethodInConsultancy($contractManagerIdCondition, $queryString, $procurementTypes);
        $data['province_contracts_based_on_value'] = $this->provinceContractsBasedOnValue($contractManagerIdCondition, $queryString);
        $data['contractChallengesContractAmount'] = $this->contractChallengesContractAmount($contractManagerIdCondition, $queryString);

//        $data['challenges_in_contracts_implementation'] = $this->challengesInContractsImplementation($contractManagerIdCondition, $queryString);

        return response()->json($data);
    }

    private function widgets($contractManagerIdCondition, $queryString, $contractStatuses)
    {

        $contractStatusCancelled = DropDowns::getElementTypeBySlug($contractStatuses, 'cancelled');
        $contractStatusCompleted = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');

        $data = DB::select('select
                    (
                        select
                            count(distinct c.id) as inserted_contracts
                            from contracts as c 
                            join contract_general_information_verifications as cgiv
                              on c.id = cgiv.contract_id
                            ' . $queryString['joins'] . '
                            where cgiv.is_approved = true
                                ' . $contractManagerIdCondition . '
                                ' . $queryString['query_string'] . '
                    ) as inserted_contracts
                    ,(
                            select
                                count(distinct c.id) - count(case when cgiv.is_approved = true then 1 end) as not_inserted_contracts 
                            from contracts as c
                            left join contract_general_information_verifications as cgiv
                              on c.id = cgiv.contract_id
                            ' . $queryString['joins'] . '
                            where 1 = 1
                            ' . $contractManagerIdCondition . '
                            ' . $queryString['query_string'] . '
                    ) as not_inserted_contracts
                    ,(
                        select
                            count(distinct c.id) as above_threshold_contracts 
                            from contracts as c 
                            ' . $queryString['joins'] . '
                            where c.is_above_threshold = true
                            ' . $queryString['query_string'] . '
                            ' . $contractManagerIdCondition . '
                    ) as above_threshold_contracts
                    ,(
                         select
                            count(distinct c.id) as below_threshold_contracts 
                            from contracts as c 
                            ' . $queryString['joins'] . '
                            where c.is_above_threshold = false
                            ' . $queryString['query_string'] . '
                            ' . $contractManagerIdCondition . '
                    ) as below_threshold_contracts
                    ,(
                        select
                            count(distinct c.id) as specialists_remarks 
                            from contracts as c
                            join remarks 
                                on remarks.contract_id = c.id
                             ' . $queryString['joins'] . '
                            where 1 = 1 ' . $contractManagerIdCondition . '
                            ' . $queryString['query_string'] . '
                    ) as specialists_remarks
                    ,(
                        select 
                            count(distinct c.id) as completed_contracts 
                        from contracts as c
                        join (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                        ) as csl_max
                            on csl_max.contract_id = c.id
                        ' . $queryString['joins'] . '
                        where  csl_max.status_id=' . $contractStatusCompleted['id'] . '
                        ' . $contractManagerIdCondition . '
                        ' . $queryString['query_string'] . ' 
                    ) as completed_contracts
                    ,(
                        select count(distinct c.id) as cancelled_contracts 
                            from contracts as c
                           join (
                                	select 
                                        csl.id, csl.contract_id, csl.status_id 
                                    from contract_status_logs as csl
                                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                    on csl.id = max_value.max_id
                            ) as csl_max
                                on csl_max.contract_id = c.id
                             ' . $queryString['joins'] . '
                            where  csl_max.status_id=' . $contractStatusCancelled['id'] . '
                                 ' . $contractManagerIdCondition . '
                                ' . $queryString['query_string'] . '
                    ) as cancelled_contracts
                    ,(
                        select count(distinct c.id) as amended_contracts 
                            from contracts as c
                            join amendments 
                                on amendments.contract_id = c.id
                             join contract_details as cd
                                on cd.contract_id = c.id
                            join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id 
                                and cdv.is_approved = true
                             ' . $queryString['joins'] . '
                            where amendments.is_approved = true
                            ' . $contractManagerIdCondition . '
                            ' . $queryString['query_string'] . '
                    ) as amended_contracts');
        return $data[0];
    }

    private function transferredContractsStatusToContractManagerBarChart($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
                select 
                    c.procurement_type_id as id,
                    count(case when cgiv.is_approved = false then 1 end) as unsaved_contracts,
                    count(case when c.is_published = true then 1 end) as published_contracts_count,
                    count(case when c.is_confirmed = true then 1 end) as confirme_contracts,
                    count(case when cgiv.is_approved = true then 1 end) as saved_contracts
                    
                from contracts as c
                left join contract_general_information_verifications as cgiv
                on cgiv.contract_id = c.id
                 ' . $queryString['joins'] . '
                where  1 = 1  ' . $contractManagerIdCondition . '
                 ' . $queryString['query_string'] . '
                group by c.procurement_type_id
                        
        ');
        return $data;
    }

    private function contractManagerWorkloadPercentage($contractManagerIdCondition, $queryString, $contractManagerId)
    {
        $procurementEntityId = DB::select('
            select 
                ucm.procurement_entity_id
            from users as u
            join user_contract_managers as ucm
                on ucm.user_id = u.id
            where u.id = ' . $contractManagerId . '
        ');
        if (!isset($procurementEntityId[0])) {
            return [];
        }
        $procurementEntityId = $procurementEntityId[0]->procurement_entity_id;
        $data = DB::select('
                select 
                    t1.total_assigned_contracts_to_contract_manager, 
                    t1.total_inserted_contracts, 
                    t1.total_assigned_contracts_to_contract_manager -  t1.total_inserted_contracts as total_not_inserted_contracts,
                    t2.total_contracts_belongs_to_an_award_authority, 
                    t3.total_confirmed_contracts,
                    t4.total_published_contracts
                from
                (
                    select 
                        count(c.id) as total_assigned_contracts_to_contract_manager,
                        count(cgiv_for_true.id) as total_inserted_contracts
                    from contracts as c 
                    left join contract_general_information_verifications as cgiv_for_true
                        on cgiv_for_true.contract_id = c.id and cgiv_for_true.is_approved = true
                    ' . $queryString['joins'] . '
                    where  1 = 1  ' . $contractManagerIdCondition . '
                    ' . $queryString['query_string'] . '
                ) as t1,
                (
                    select 
                        count(c.id) as total_contracts_belongs_to_an_award_authority
                    from contracts as c
                     ' . $queryString['joins'] . '
                    where c.procurement_entity_id = ' . $procurementEntityId . '
                    ' . $queryString['query_string'] . '
                ) as t2,
                (
                    select 
                        count(c.id) as total_confirmed_contracts
                    from contracts as c
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $contractManagerIdCondition . ' and c.is_confirmed = true 
                    ' . $queryString['query_string'] . '
                ) as t3,
                (
                    select
                        count(c.id) as total_published_contracts
                    from contracts as c 
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $contractManagerIdCondition . ' and c.is_published = true 
                    ' . $queryString['query_string'] . '
                ) as t4
        ');
        return $data;
    }

    private function contractsDocumentsUpload($contractManagerIdCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                     ' . $queryString['joins'] . '
                    where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1  ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                             ' . $queryString['joins'] . '
                            where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        
        
        ');

        return $data;
    }

    private function documentsUploadConsultancyServices($contractManagerIdCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');

        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyServices['id'] . '   ' . $contractManagerIdCondition . '
                 ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                        ' . $queryString['joins'] . '
                    where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                     ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'reference_terms\'
                 ,\'financial_tables\', \'action_plan\', \'negotiation_meeting_minutes\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'key_staffs\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name  = \'contract_closeout_report\'
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $queryString['joins'] . '
                            where  1 = 1   ' . $contractManagerIdCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                             ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');
        return $data;
    }

    private function contractsCountBasedOnStatusChart($contractManagerIdCondition, $queryString)
    {
        $data['contracts_count'] = DB::select('
            select 
                count(distinct c.id) as contract_count
            from contracts as c 
            join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as csl_max
                    on csl_max.contract_id = c.id
            ' . $queryString['joins'] . '
            where  1 = 1  ' . $contractManagerIdCondition . '
            ' . $queryString['query_string'] . ';
	    ')[0];
        $data['status_based_contracts_count'] = DB::select('
            select 
                cs.status_id as status_id,
                count( distinct cs.id) as status_count
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c 
                on c.id = cs.contract_id
            ' . $queryString['joins'] . '
            where 1 = 1  ' . $contractManagerIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
 
            
        ');

        return $data;
    }

    private function contractsValueBasedOnStatusChart($contractManagerIdCondition, $queryString)
    {
        $data['contracts_total_value'] = DB::select('
            select sum(temp.contract_value) as contracts_total_value
                from	
                (select 
                    round(sum(
                                        (ifnull(cd.actual_value, 0) + 
                                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                                        ifnull(amendment.amount, 0))
                                        * if(c.exchange_rate, c.exchange_rate, 1)
                                    ) / 1000000, 4) as contract_value
                from contracts as c
                left join (
                                     select 
                                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                            a.contract_id as contract_id
                                     from amendments as a
                                     left join cost_amendments as ca 
                                         on ca.amendment_id = a.id
                                     left join time_and_cost_amendments as tca
                                         on tca.amendment_id = a.id
                                     where a.is_approved = true
                                     group by  a.contract_id 
                                  ) as amendment
                                        on c.id = amendment.contract_id 
                join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
                  on cs.contract_id = c.id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $contractManagerIdCondition . '
                ' . $queryString['query_string'] . '
                group by c.id, c.exchange_rate) as temp;
	    ')[0];

        $data['status_based_contracts_value'] = DB::select('
            select 
                cs.status_id as status_id,
                round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000, 4)  as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            left join contracts as c
                on c.id = cs.contract_id
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
            ) as amendment
                on c.id = amendment.contract_id 
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $contractManagerIdCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
        ');

        return $data;
    }

    private function paymentPercentageChart($contractManagerIdCondition, $queryString)
    {
        $data['plan_and_actual'] = DB::select('
                    select distinct
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                          sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        ) as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1  ' . $contractManagerIdCondition . '
                    ' . $queryString['query_string'] . '
            ')[0];
        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                          if(
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1  ' . $contractManagerIdCondition . '
                    ' . $queryString['query_string'] . '
                    group by cr.iteration_count;   
            ');

        return $data;
    }

    private function contractManagerNumberOfContractsBasedOnChallenges($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
            select 
                total_contracts_count, 
                challenges_contracts_count,
                (total_contracts_count-challenges_contracts_count) as not_challenge_contracts_count
            from 
            (
                select 
                    count(distinct(c.id)) as total_contracts_count, 
                    count(distinct (cr.contract_id)) as challenges_contracts_count
                from contracts as c
                    left Join challenges_and_remarks as cr 
                    on c.id = cr.contract_id and cr.is_approved = true
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $contractManagerIdCondition . ' 
                    ' . $queryString['query_string'] . '
            ) as t
                ');
        return $data[0];
    }

    private function workInProgressContractPaymentsVolumeChart($contractManagerIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];

        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select 
                        round(sum(ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4)  as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                        
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and  cpv.is_approved = true 
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1
                        ' . $contractManagerIdCondition . ' 
                         ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and  
                        cp.year in  ( ' . $value['year'] . ' ) and 
                        cp.month_id in(' . $value['months'] . ')
        ')[0];


            $data['re_plan_and_actual'][$key] = DB::select('
                    select 
                        cr.iteration_count as replan_number,
                        round(sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $contractManagerIdCondition . ' 
                         ' . $queryString['query_string'] . ' and 
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in (' . $value['year'] . ') and 
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }

        return $data;

    }

    private function provinceContractsBasedOnValue($contractManagerIdCondition, $queryString)
    {
        $data['no_shared_contracts'] = DB::select('
                select 
                    p.id as province_id, 
                    cs.status_id as contract_status_id, 
                    count(distinct(c.id)) as contracts_count,
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1) 
                    ) as total_contract_value
                from contracts as c 
                join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = c.id
                left join contract_details as cd 
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $queryString['joins'] . '
                where 1=1 ' . $queryString['query_string'] . '
                ' . $contractManagerIdCondition . '
                and c.id not in
                    (
                    select 
                        c.id as contract_id
                    from contracts as c 
                    join domestic_contract_execution_locations as dc 
                        on dc.contract_id = c.id 
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                    )
                group by p.id, cs.status_id;
        ');

        $data['shared_contracts'] = DB::select('
            select province_id, 
            sum(shared_contracts_count) as shared_contracts_count, 
            sum(total_contract_shared_value) as total_contract_shared_value 
            from (
                select 
                    provinces_contracts.province_id, 
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(join_c.exchange_rate, join_c.exchange_rate, 1) 
                    ) as total_contract_shared_value,
                    count(provinces_contracts.province_id) as shared_contracts_count
                from 
                    (
                        select distinct
                            p.id as province_id,
                            c.id as contract_id
                        from contracts as c
                        join domestic_contract_execution_locations as dcel
                            on dcel.contract_id = c.id
                        join temp_districts as d 
                            on d.id = dcel.district_id
                        join temp_provinces as p
                            on p.id = d.temp_province_id
                        
                    ) as provinces_contracts
                left join contract_details as cd
                    on cd.contract_id = provinces_contracts.contract_id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on provinces_contracts.contract_id = amendment.contract_id
                join contracts as join_c
                    on join_c.id = provinces_contracts.contract_id 
                where provinces_contracts.contract_id not in (
                    select 
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dcel
                        on dcel.contract_id = c.id
                    join temp_districts as d 
                        on d.id = dcel.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $queryString['query_string'] . '
                    ' . $contractManagerIdCondition . '
                    group by c.id
                    having count(distinct p.id) = 1
                )
                group by provinces_contracts.province_id, provinces_contracts.contract_id) as temp_table
            group by province_id;
        ');

        return $data;
    }

    private function workInProgressContractPhysicalProgressVolumeChart($contractManagerIdCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $tempPlan = 0;
        $tempActual = 0;
        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select
                        round(
                        sum((cp.physical_progress_percentage  / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000, 4) + ' . $tempPlan . ' as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000 , 4) + ' . $tempActual . ' as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and cpv.is_approved = true


                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id


                    join contracts as c
                        on cp.contract_id = c.id
                    join contract_details as cd
	                    on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $contractManagerIdCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        cp.year in  ( ' . $value['year'] . ' ) and
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $tempPlan = $data['plan_and_actual'][$key]->planning_progress_value ? $data['plan_and_actual'][$key]->planning_progress_value : 0;
            $tempActual = $data['plan_and_actual'][$key]->actual_progress_value ? $data['plan_and_actual'][$key]->actual_progress_value : 0;


            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        round(sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as actual_progress_value
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment
                        on c.id = amendment.contract_id
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                        ' . $queryString['joins'] . '
                    where 1 = 1 ' . $contractManagerIdCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in  ( ' . $value['year'] . ' ) and
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;

        ');

        }

        return $data;

    }

    private function volumeOfCompletedContracts($contractManagerIdCondition, $queryString, $contractStatuses)
    {
        $contractCompletedStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');
        $data = DB::select('
            select 
                round(
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amcount,
                    
                round(
                  sum(
                          ifnull(cpp.amount,0) * 
                          if(c.exchange_rate, c.exchange_rate, 1)
                      ) / 1000000, 4) as contracts_payment_amount,
                c.npa_identification_number
            from contracts as c
            join contract_details as cd
                on c.id = cd.contract_id
            join contract_details_verifications as cdv
                on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cd.contract_id
            left join (
                select 
                    cpp_inner.contract_id ,
                    sum(cpp_inner.amount) as amount
                from  contract_progress_payments as cpp_inner
                where cpp_inner.is_approved = true
                group by cpp_inner.contract_id 
              ) as cpp
                on c.id = cpp.contract_id 
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
               ) as amendment
                   on c.id = amendment.contract_id
            ' . $queryString['joins'] . '
              where cs.status_id = ' . $contractCompletedStatus['id'] . '
            ' . $contractManagerIdCondition . '
               ' . $queryString['query_string'] . '
            group by c.npa_identification_number
             ');
        return $data;

    }

    private function contractsCountBasedOnContractManager($contractManagerIdCondition, $queryString)
    {
        //Has Transferred to AllCharts
        $procurementEntityId = DB::select('
                            select c.procurement_entity_id 
                            from contracts as c 
                            where  1 = 1   ' . $contractManagerIdCondition . '
                            limit 1     
                           ');
        if (!isset($procurementEntityId[0])) {
            return [];
        }
        $procurementEntityId = $procurementEntityId[0]->procurement_entity_id;
        $data = DB::select('
                    select
                        count(distinct c.id) as contracts_count,
                        u.full_name                    
                    from contracts as c
                    left join users as u
                        on u.id = c.contract_manager_id
                    ' . $queryString['joins'] . '
                    where  c.procurement_entity_id = ' . $procurementEntityId . '
                    ' . $queryString['query_string'] . '
                    group by c.contract_manager_id, u.full_name
                            ');
        return $data;
    }

    private function contractsCountBasedOnProcurementMethod($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
        select
          count(c.id) as contrats_count,
          c.procurement_method_id
        from contracts as c
        ' . $queryString['joins'] . '
        where  1 = 1   ' . $contractManagerIdCondition . '
        ' . $queryString['query_string'] . '
        group by c.procurement_method_id
        ');

        return $data;
    }

    private function numberOfAmendmentsInContracts($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
                select
                  count(distinct c.id) as contracts_number,
                  a.type_id
                from contracts as c
                join amendments as a
                  on c.id = a.contract_id
                join contract_details as cd
                        on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                  ' . $queryString['joins'] . '
                where a.is_approved = true
                and  1 = 1   ' . $contractManagerIdCondition . '
                ' . $queryString['query_string'] . '
                group by a.type_id');
        return $data;
    }

    private function amendmentPercentageInContracts($contractManagerIdCondition, $queryString, $amendmentTypes)
    {
        $timeAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-amendment');
        $timeAndCostAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-and-cost-amendment');
        $data = DB::select('
                    select 
                        round(
                          sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(if(amendment_cost.amount, amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        , 2)  as total_value_percentage,
                        round(
                          sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        , 2)  as cost_amendment_percentage,
                        round(
                            sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) * 100 /
                            (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        , 2) as total_time_percentage, 
                        round(
                            sum(ifnull(amendment_time.days, 0)) * 100 /
                            (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        , 2) as time_amendment_percentage 
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id 
                        and cdv.is_approved = true
                    left join contract_progresses as cp
                        on c.id = cp.contract_id
                    left join contract_progress_verifications as cpv
                        on cp.id = cpv.contract_progress_id
                        and cpv.is_approved = true
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                    ) as amendment_cost
                       on amendment_cost.contract_id = c.id
                    left join (
                        select 
                             a.contract_id,
                             GREATEST(
                                      ifnull(
                                          datediff(
                                              (select amended_end_date from time_amendments where amendment_id = a.id)
                                              , 
                                              (select planned_end_date from contract_details where contract_id = a.contract_id)
                                          ),0), 
                                      ifnull(
                                            datediff(
                                                (select amended_end_date from time_and_cost_amendments where amendment_id = a.id)
                                                , 
                                                (select planned_end_date from contract_details where contract_id = a.contract_id)
                                            ),0)
                              ) as days
                        from amendments as a 
                        join ( 
                            SELECT 
                            contract_id, max(id) as max_id 
                            FROM amendments 
                            where type_id in (' . $timeAmendment['id'] . ',' . $timeAndCostAmendment['id'] . ') and is_approved =true
                            group by contract_id
                            ) as max_value
                            on max_value.max_id = a.id
                        group by a.contract_id, a.id
                    ) as amendment_time
                        on amendment_time.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $contractManagerIdCondition . '
                    ' . $queryString['query_string'] . '
        ');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    private function contractsValueBasedDonors($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('select
                          round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            ) / 1000000, 4) as total_contracts_value,cdo.donor_id as donor_id
                          from contracts as c
                          join contract_donors as cdo
                            on c.id = cdo.contract_id
                          join contract_details as cd
                              on c.id = cd.contract_id
                          join contract_details_verifications cdv
                              on cd.id = cdv.contract_detail_id
                              and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                              on psc.contract_detail_id = cd.id
                          left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                group by  a.contract_id 
                        
                            ) as amendment
                                on c.id = amendment.contract_id
                          ' . $queryString['joins'] . '
                          where 1 = 1 ' . $contractManagerIdCondition . '
                          ' . $queryString['query_string'] . '
                          group by cdo.donor_id;
                        ');

        return $data;
    }

    private function contractsNumberBasedDonors($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
                        select
                          count(c.id) as contracts_number,
                          cd.donor_id
                        from contracts as c
                        join contract_donors as cd
                          on c.id = cd.contract_id
                          ' . $queryString['joins'] . '
                        where  1 = 1   ' . $contractManagerIdCondition . '
                        ' . $queryString['query_string'] . '
                        group by cd.donor_id
                                ');
        return $data;
    }

    private function numberAndValueOfContractBasedOnProcurementType($contractManagerIdCondition, $queryString)
    { // Has Transferred to AllCharts
        $data = DB::select('
                    select
                      count(c.id) as contract_number,
                      round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            )/ 1000000,4) as contract_value,
                      c.procurement_type_id
                    from contracts as c
                    join contract_details as cd
                      on c.id = cd.contract_id
                    join contract_details_verifications as cdv
                      on cd.id = cdv.contract_detail_id
                    left join provisional_sum_and_contingencies as psc
                      on psc.contract_detail_id = cd.id
                    left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                group by  a.contract_id 
                        
                      ) as amendment
                        on c.id = amendment.contract_id
                      ' . $queryString['joins'] . '
                    where  1 = 1   ' . $contractManagerIdCondition . '
                    and cdv.is_approved = true 
                    ' . $queryString['query_string'] . '
                    group by c.procurement_type_id');
        return $data;
    }

    private function contractsCountBasedOnSelectionMethodInConsultancy($contractManagerIdCondition, $queryString, $procurementTypes)
    {
        $consultancyStatus = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data = DB::select('
                select 
                    cc.selection_method_id,
                    count(cc.id) as count
                from contracts as c 
                join consultancy_contracts_selection_methods as cc
                     on cc.contract_id = c.id 
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyStatus['id'] . '   
                ' . $contractManagerIdCondition . '
                ' . $queryString['query_string'] . '
                 group by cc.selection_method_id
        ');

        return $data;
    }

    private function contractChallengesContractAmount($contractManagerIdCondition, $queryString)
    {
        $data = DB::select('
                  select 
                    car_distinct.category_id,
                    count(car_distinct.contract_id) contracts_count,
                    round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amount
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                   ) as amendment
                          on c.id = amendment.contract_id
               left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
               join (
                    select distinct 
                      inner_car.category_id,
                      inner_car.contract_id
                    from challenges_and_remarks as inner_car
                    where inner_car.is_approved = true
                    group by inner_car.category_id, inner_car.contract_id, inner_car.id
                 )as car_distinct 
                    on car_distinct.contract_id = c.id
                 ' . $queryString['joins'] . '
               where 1 = 1    ' . $contractManagerIdCondition . '
               ' . $queryString['query_string'] . ' 
               group by car_distinct.category_id
                  
                  
                  ');
        return $data;
    }

    //    private function challengesInContractsImplementation($contractManagerIdCondition, $queryString)
//    {
//        $data = DB::select('
//                    select
//                      count(c.id) as number_of_contracts_based_on_challenges,
//                      cr.category_id
//                    from contracts as c
//                    inner join challenges_and_remarks as cr
//                      on c.id = cr.contract_id
//                      ' . $queryString['joins'] . '
//                    where  1 = 1   ' . $contractManagerIdCondition . '
//                    ' . $queryString['query_string'] . '
//                    group by cr.category_id');
//        return $data;
//    }

    public function getProcurementTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->procurement_type(), $slug)['id'];
    }

    public function procurement_type()
    {
        $path = 'api/dropDown/procurementType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }


}
