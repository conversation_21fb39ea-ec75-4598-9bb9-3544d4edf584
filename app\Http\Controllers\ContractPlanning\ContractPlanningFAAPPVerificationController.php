<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractPlanningFAAPPVerification;

class ContractPlanningFAAPPVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $getData = Contract::find($data['contract_id'])
                ->contract_planning_f_a_a_p_p_verification()
                ->create(array_only($data, ContractPlanningFAAPPVerification::$fields));
            return response()->json($getData, 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractPlanningFAAPPVerification)
    {
        $contractPlanningFAAPPVerification = ContractPlanningFAAPPVerification::where('contract_id', $contractPlanningFAAPPVerification)->first();
        return response()->json($contractPlanningFAAPPVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractPlanningFAAPPVerification)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contractPlanningFAAPPVerification =
                ContractPlanningFAAPPVerification::where('contract_id', $contractPlanningFAAPPVerification)->first();
            $isUpdated = $contractPlanningFAAPPVerification->update(array_only($data, ContractPlanningFAAPPVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractPlanningFAAPPVerification $contractPlanningFAAPPVerification)
    {
        //
    }
}
