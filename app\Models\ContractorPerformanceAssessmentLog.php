<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractorPerformanceAssessmentLog extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'execution_management_grade_id',
        'work_quality_grade_id',
        'execution_based_on_contract_grade_id',
        'work_progress_based_on_plan_grade_id',
        'contractor_performance_assessment_id',
        'contract_status_id',
        'contract_status_log_id'
    ];

    public function contractor_performance_assessment()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractorPerformanceAssessment');
    }

    public function contract_status_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusLogs');
    }
}
