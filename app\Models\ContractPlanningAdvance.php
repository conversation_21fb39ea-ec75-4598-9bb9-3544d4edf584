<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractPlanningAdvance extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'amount',
        'remarks',
        'guarantee_type_id',
        'date'
    ];

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
