<?php
/**
 * Created by PhpStorm.
 * User: faramarz
 * Date: 8/7/2019
 * Time: 11:21 AM
 */

namespace NPA\ACPMS\Helpers;


use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Http\Controllers\SpecialistMonitoringReport\SpecialistMonitoringReportController;
use NPA\ACPMS\Models\PARS\ProgressAnalysisReport;

class SpecialistMonitoringGetReport
{
    public static function getReport($id){
        try {
            $data = ProgressAnalysisReport::where('id', $id)
                ->with(['general_information' => function ($query) {
                    $query->with(['vendors', 'contract_execution_locations', 'donors', 'budget']);
                },
                    'performance_security',
                    'values',
                    'progress',
                    'durations',
                    'advance_payment_guaranty',
                    'time_amendments',
                    'cost_amendments',
                    'time_and_cost_amendments',
                    'condition_amendments',
                    'challenges_and_remarks',
                    'charts',
                    'check_dbs',
                    'defect_liabilities',
                    'delay_penalties',
                    'progress_stops',
                    'remarks'])->first()->toArray();
            $data['month'] = Date::getJalaliMonthName($data['month']);
            $countContracts = ProgressAnalysisReport::where('contract_id', $data['contract_id'])->count();
            $data['contract_value_with_amendments'] = $countContracts;
            $data['general_information']['contract_value_with_amendments'] = number_format((int)$data['general_information']['contract_value_with_amendments'], 2);
            $data['performance_security']['amount'] = number_format((int)$data['performance_security']['amount'], 2);
            $data['values']['estimated_value'] = number_format((int)$data['values']['estimated_value'], 2);
            $data['values']['actual_value'] = number_format((int)$data['values']['actual_value'], 2);
            $data['values']['value_difference'] = number_format((int)$data['values']['value_difference'], 2);
            $data['values']['percentage_difference'] = round((int)$data['values']['percentage_difference'], 2);
            $data['progress']['planned_payments_till_now'] = round((int)$data['progress']['planned_payments_till_now'], 2);
            $data['progress']['actual_payments_till_now'] = round((int)$data['progress']['actual_payments_till_now'], 2);
            $data['general_information']['isJv'] = count($data['general_information']['vendors']) > 1 ? 1 : 0;

            foreach ($data['cost_amendments'] as &$amendment) {
                $amendment['amendment_amount'] = number_format((int)$amendment['amendment_amount']);
                $amendment['contract_value_after_amend'] = number_format((int)$amendment['contract_value_after_amend']);
            }

            foreach ($data['time_amendments'] as &$amendment) {
                if ($amendment['amendment_start_date']) {
                    $amendment['amendment_start_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amendment_start_date']);
                }
                if ($amendment['amended_end_date']) {
                    $amendment['amended_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_end_date']);
                }
                if ($amendment['amended_performance_security_end_date']) {
                    $amendment['amended_performance_security_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_performance_security_end_date']);
                }
            }

            foreach ($data['time_and_cost_amendments'] as &$amendment) {
                if ($amendment['amendment_start_date']) {
                    $amendment['amendment_start_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amendment_start_date']);
                }
                if ($amendment['amended_end_date']) {
                    $amendment['amended_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_end_date']);
                }
                if ($amendment['amended_performance_security_end_date']) {
                    $amendment['amended_performance_security_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_performance_security_end_date']);
                }
            }
            foreach ($data['cost_amendments'] as &$amendment) {
                if ($amendment['amended_performance_security_end_date']) {
                    $amendment['amended_performance_security_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_performance_security_end_date']);
                }
            }
            foreach ($data['progress_stops'] as &$progressStop) {
                if ($progressStop['progress_stop_start_date']) {
                    $progressStop['progress_stop_start_date'] = Date::gregorianToJalaliInSlashFormat($progressStop['progress_stop_start_date']);
                }
                if ($progressStop['progress_stop_end_date']) {
                    $progressStop['progress_stop_end_date'] = Date::gregorianToJalaliInSlashFormat($progressStop['progress_stop_end_date']);
                }
            }

            $data['general_information']['award_till_signature_date_duration'] =
                SpecialistMonitoringReportController::dayDifference($data['general_information']['award_date'], $data['general_information']['agreement_signature_date']);
            $data['general_information']['signature_till_actual_date_duration'] =
                SpecialistMonitoringReportController::dayDifference($data['general_information']['agreement_signature_date'], $data['durations']['actual_start_date']);
            // fixing the charts data start

            $progress = json_decode($data['charts']['planning_physical_progress_chart'], true);
            $payments = json_decode($data['charts']['planning_payment_progress_chart'], true);
            $spi = json_decode($data['charts']['schedule_performing_index_chart'], true);
            $cpi = json_decode($data['charts']['cost_performance_index_chart'], true);
            $chart = [
                'progress' => [
                    'plan' => SpecialistMonitoringReportController::normalizeProgressData($progress['plan']['payments'], 'physical_progress_percentage'),
                    'plan_length' => count($progress['plan']['payments']) + 1
                ],
                'payments' => [
                    'plan' => SpecialistMonitoringReportController::normalizeProgressData($payments['plan']['payments'], 'amount'),
                    'plan_length' => count($payments['plan']['payments']) + 1
                ],
                'spi' => [
                    'plan' => SpecialistMonitoringReportController::normalizeSpiData($spi['plan']),
                    'plan_length' => count($payments['plan']['payments']) + 1
                ],
                'cpi' => []
            ];
            $progressRePlans = [];
            $paymentRePlans = [];
            $spiRePlans = [];
            foreach ($progress['re_plan']['payments'] as $payment) {
                if (!isset($progressRePlans['re_plan_' . $payment['iteration_count']])) {
                    $progressRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($progressRePlans['re_plan_' . $payment['iteration_count']], $payment);

            }
            foreach ($progressRePlans as $key => $value) {
                $chart['progress'][$key] = SpecialistMonitoringReportController::normalizeProgressData($value, 'physical_progress_percentage');
                $chart['progress'][$key . '_length'] = count($value) + 1;
            }


            foreach ($payments['re_plan']['payments'] as $payment) {
                if (!isset($paymentRePlans['re_plan_' . $payment['iteration_count']])) {
                    $paymentRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($paymentRePlans['re_plan_' . $payment['iteration_count']], $payment);

            }
            foreach ($paymentRePlans as $key => $value) {
                $chart['payments'][$key] = SpecialistMonitoringReportController::normalizeProgressData($value, 'amount');
                $chart['payments'][$key . '_length'] = count($value) + 1;
            }

            foreach ($spi['re_plan'] as &$payment) {
                if ($payment['iteration_count'] === 1) {
                    if ($payment['status_id'] === 2) {
                        foreach ($spi['plan'] as $planPayment) {
                            if ($payment['month_id'] === $planPayment['month_id'] && $payment['year'] === $planPayment['year']) {
                                $payment['pv'] = $planPayment['pv'];
                                $payment['ev'] = $planPayment['ev'];
                                $payment['update'] = true;
                            }
                        }
                    }
                }
                if ($payment['iteration_count'] > 1) {
                    if ($payment['status_id'] === 2) {
                        foreach ($spi['re_plan'] as $innerPayment) {
                            if ($payment['iteration_count'] === $innerPayment['iteration_count'] + 1
                                && $payment['month_id'] === $innerPayment['month_id']
                                && $payment['year'] === $innerPayment['year']) {
                                $payment['pv'] = $innerPayment['pv'];
                                $payment['ev'] = $innerPayment['ev'];
                                $payment['update_' . $payment['iteration_count']] = true;
                            }
                        }

                    }

                }
            }
            $lastRePlanKey = null;
            foreach ($spi['re_plan'] as $payment) {
                if (!isset($spiRePlans['re_plan_' . $payment['iteration_count']])) {
                    $spiRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($spiRePlans['re_plan_' . $payment['iteration_count']], $payment);
                $lastRePlanKey = 're_plan_' . $payment['iteration_count'];

            }
            foreach ($spiRePlans as $key => $value) {
                $chart['spi'][$key] = SpecialistMonitoringReportController::normalizeSpiData($value);
                $chart['spi'][$key . '_length'] = count($value) + 1;


            }
            if ($lastRePlanKey) {
                $chart['spi']['plan'] = $chart['spi'][$lastRePlanKey];
                $chart['spi']['plan_length'] = count($chart['spi'][$lastRePlanKey]) + 1;

            }


            $cpiData = [];
            $i = -1;
            foreach ($cpi as $payment) {
                $ev = ($payment['physical_progress_percentage'] / 100) * $payment['total_amount'];
                array_push($cpiData, [
                    "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                    "total_ac" => $i < 0 ? $payment['amount'] : $cpiData[$i]['total_ac'] + $payment['amount'],
                    "total_ev" => $i < 0 ? $ev : $cpiData[$i]['total_ev'] + $ev,
                    "index" => $i < 0 ? ($ev ? $payment['amount'] / $ev : 0) :
                        ($cpiData[$i]['total_ev'] ? $cpiData[$i]['total_ac'] / $cpiData[$i]['total_ev'] : 0)
                ]);
                $i++;
            }
            $chart['cpi'] = $cpiData;
            $chart['cpi_length'] = count($cpiData) + 1;
            $chart['length'] = count($cpiData);
            $data['chart'] = $chart;
            unset($data['charts']);
            $data['is_performance_security_applicable_text'] = $data['remarks']['is_performance_security_applicable'] ? 'تضمین اجرای قرارداد قابل اجرا می باشد.' : 'تضمین اجرای قرارداد قابل اجرا نمی باشد.';
            $data['is_performance_security_legally_text'] = $data['remarks']['is_performance_security_legally'] && $data['remarks']['is_performance_security_applicable'] ?
                'تضمین اجرای قراراد مطابق شرایط قرارداد و حکم هفتاد و هشتم طرزالعمل تدارکات ارائه گردیده است.' : 'تضمین اجرای قراراد مطابق شرایط قرارداد و حکم هفتاد و هشتم طرزالعمل تدارکات ارائه نگردیده است.';
            $data['is_advance_payment_applicable_text'] = $data['remarks']['is_advance_payment_applicable'] ? 'تضمین پیش پرداخت قابل اجرا می باشد.' : 'تضمین پیش پرداخت قابل اجرا نمی باشد.';
            $data['is_advance_payment_legally_text'] = $data['remarks']['is_advance_payment_legally'] && $data['remarks']['is_advance_payment_applicable'] ?
                'پیش پرداخت در مطابقت با شرایط قرارداد و حکم یکصد و یکم طرز العمل تدارکات اجرا گردیده است' : 'پیش پرداخت در مطابقت با شرایط قرارداد و حکم یکصد و یکم طرز العمل تدارکات اجرا نگردیده است';
            // fixing the charts data ends
            count($data['time_amendments']) > 0 ? $data['time_amendments_length_text'] = '  تعداد تعدیلات زمانی: ' . count($data['time_amendments']) :
                $data['time_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل زمانی قرارداد در سیستم گزارش داده نشده است.';

            count($data['cost_amendments']) > 0 ? $data['cost_amendments_length_text'] = '  تعداد تعدیلات پولی: ' . count($data['cost_amendments']) :
                $data['cost_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل پولی قرارداد در سیستم گزارش داده نشده است.';

            count($data['condition_amendments']) > 0 ? $data['condition_amendments_length_text'] = '  تعداد تعدیلات در شرایط قرارداد: ' . count($data['condition_amendments']) :
                $data['condition_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل در شرایط قرارداد در سیستم گزارش داده نشده است.';

            count($data['time_and_cost_amendments']) > 0 ? $data['time_and_cost_amendments_length_text'] = '  تعداد تعدیلات پولی و زمانی: ' . count($data['time_and_cost_amendments']) :
                $data['time_and_cost_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل  پولی و زمانی قرارداد در سیستم گزارش داده نشده است.';

            $statues = ['cancelled', 'contract-completion-defect-liability-period', 'contract-close-out'];
            $data['subcontracting_text'] = 'الی اکنون اسناد مبنی بر موجودیت قرارداد فرعی در سیستم گزارش داده نشده است.';
            $data['completion_text_1'] = '';
            $data['close_out_text_1'] = '';
            $data['cancelled_text_1'] = '';
            if (in_array($data['progress']['contract_status_slug'], $statues)) {
                if ($data['progress']['contract_status_slug'] == 'cancelled') {
                    $data['cancelled_text_1'] = ' آیا فسخ قرارداد به اثر تخطی قراردادی صورت گرفته است؟ ' . ($data['remarks']['is_termination_cause_of_contractor_violation'] == 1 ? 'بلی' : ($data['remarks']['is_termination_cause_of_contractor_violation'] == 1 ? 'نخیر' : 'ثبت نشده'));
                    $data['cancelled_text_2'] = ' استرداد تضمین اجرای قرارداد مطابق فقره 7 حکم هفتاد وهشتم طرزالعمل تدارکات صورت  ' . ($data['remarks']['is_termination_cause_of_contractor_violation'] ? '' : 'ن') . 'گرفته است.';

                }
                if ($data['progress']['contract_status_slug'] == 'contract-completion-defect-liability-period') {
                    $data['completion_text_1'] = ' سند تکمیل فزیکی قرارداد به قراردادی ارائه ' . ($data['check_dbs']['is_completion_document_uploaded'] ? '' : 'ن') . 'گردیده است.';
                    $data['completion_text_2'] = ' استرداد تضمین اجرای قرارداد مطابق فقره 4 حکم هفتاد هشتم طرزالعمل تدارکات صورت ' . ($data['check_dbs']['is_performance_guaranty_renewed'] ? '' : 'ن') . 'گرفته است.';
                }
                if ($data['progress']['contract_status_slug'] == 'contract-close-out') {
                    $data['close_out_text_1'] = 'سند تصدیقنامه ختم قرارداد به قراردادی صادر  ' . ($data['check_dbs']['is_contract_termination_doc_issued'] ? '' : 'ن') . 'گردیده است.';
                    $data['close_out_text_2'] = '   استرداد تامینات مطابق فقره 4 حکم هشتاد ام طرزالعمل تدارکات به قراردادی صورت  ' . ($data['check_dbs']['is_supplies_legally_returned'] ? '' : 'ن') . 'گرفته است.';
                }
            }
            $data['time_cost_merged'] = array_merge($data['time_amendments'], $data['cost_amendments']);

            foreach ($data['time_cost_merged'] as &$amendment) {
                if (isset($amendment['amended_performance_security_end_date']) && $amendment['amended_performance_security_end_date'] == null) {
                    $amendment['amended_performance_security_end_date'] = 0;
                }
                $amendment['amended_performance_security_end_date'] = 0;
                if (isset($amendment['amended_performance_security_amount']) && $amendment['amended_performance_security_amount'] == null) {
                }
            }
            $data['delay_penalties_text'] = 'جریمه تاخیر در مقابل (' . $data['delay_penalties']['delay_penalties_total_duration'] . ') روز تاخیر در مطابقت با شرایط قرارداد ' .
                '(' . $data['delay_penalties']['delay_penalties_total_percentage'] . ') فیصد قیمت مجموعی قرارداد وضع گردیده است.';
            if (!$data['delay_penalties']['is_delay_penalty_applicable']) {
                $data['delay_penalties_text'] = 'قابل تطبیق نمی باشد.';
            }
            if ($data['check_dbs']['is_main_contract_have_subcontracts']) {
                $data['subcontracting_text'] = 'قرارداد  فرعی در مطابقت با شرایط قرارداد و حکم یکصد و پنجم طرزالعمل تدارکات عقد ' . ($data['check_dbs']['is_subcontracting_legal'] ? '' : 'ن') . 'گردیده است.';
            }

            // changes the date to jalali
            if ($data['general_information']['award_date']) {
                $data['general_information']['award_date'] = Date::gregorianToJalaliInSlashFormat($data['general_information']['award_date']);
            }
            if ($data['general_information']['agreement_signature_date']) {
                $data['general_information']['agreement_signature_date'] = Date::gregorianToJalaliInSlashFormat($data['general_information']['agreement_signature_date']);
            }
            if ($data['performance_security']['start_date']) {
                $data['performance_security']['start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['performance_security']['start_date']);
            }
            if ($data['performance_security']['end_date']) {
                $data['performance_security']['end_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['performance_security']['end_date']);
            }

            if ($data['durations']['planned_start_date']) {
                $data['durations']['planned_start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['planned_start_date']);
            }
            if ($data['durations']['planned_end_date']) {
                $data['durations']['planned_end_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['planned_end_date']);
            }
            if ($data['durations']['actual_close_out_date']) {
                $data['durations']['actual_close_out_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_close_out_date']);
            }
            if ($data['durations']['actual_start_date']) {
                $data['durations']['actual_start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_start_date']);
            }
            if ($data['durations']['actual_completion_date']) {
                $data['durations']['actual_completion_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_completion_date']);
            }
            if ($data['advance_payment_guaranty']['issue_date']) {
                $data['advance_payment_guaranty']['issue_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['advance_payment_guaranty']['issue_date']);
            }
            if ($data['advance_payment_guaranty']['expiration_date']) {
                $data['advance_payment_guaranty']['expiration_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['advance_payment_guaranty']['expiration_date']);
            }
            $data['advance_payment_guaranties'] = [];
            if (isset($data['advance_payment_guaranty']['amount']) && $data['advance_payment_guaranty']['amount'] !== null) {
                $data['advance_payment_guaranty']['expiration_date'] = Date::gregorianToJalaliInSlashFormat($data['remarks']['advance_payment_guaranty_expiration_date']);
                $data['advance_payment_guaranties'] = [$data['advance_payment_guaranty']];

            }
            $data['challenges_and_remark_pe_text'] = 'از جانب اداره محترم کدام چالش در رابطه به پیشرفت قرارداد در سیستم نظارت از پیشرفت قراردادها  (ACPMS) گزارش داده نشده است. ';
            $data['challenges_and_remark_vendor_text'] = 'از جانب شرکت محترم قراردادی کدام چالش در رابطه به پیشرفت قرارداد در سیستم نظارت از پیشرفت قراردادها  (ACPMS) گزارش داده نشده است.';
            foreach ($data['challenges_and_remarks'] as &$challenges_and_remark) {
                if ($challenges_and_remark['creator'] == 'contract-manager') {
                    $challenges_and_remark['is_pe'] = 1;
                    $data['challenges_and_remark_pe_text'] = '';
                } else {
                    $challenges_and_remark['is_pe'] = 0;
                    $data['challenges_and_remark_vendor_text'] = '';
                }
                if ($challenges_and_remark['start_date']) {
                    $challenges_and_remark['start_date'] =
                        Date::gregorianToJalaliInSlashFormat($challenges_and_remark['start_date']);
                }
                if ($challenges_and_remark['created_at']) {
                    $challenges_and_remark['created_at'] =
                        Date::gregorianToJalaliInSlashFormat($challenges_and_remark['created_at']);
                }
            }
            $progressPercentage = $data['progress']['planned_physical_progress_till_now'] - $data['progress']['actual_physical_progress_till_now'];
            $data['progress']['physical_difference'] = $progressPercentage == 0 ? $progressPercentage : ($progressPercentage > 0 ? ('' . $progressPercentage . ' عقبمانی ') : ('' . $progressPercentage . ' پیشرفت '));
            $data['progress']['payment_difference'] = number_format($data['progress']['planned_payments_till_now'] - $data['progress']['actual_payments_till_now'], 2);
            $data['progress']['planned_physical'] = $data['progress']['planned_physical_progress_till_now'];
            $data['progress']['actual_physical'] = $data['progress']['actual_physical_progress_till_now'];
            $data['durations']['actual_start_date_not_reversed'] = $data['durations']['actual_start_date'];
            $data['durations']['actual_start_date'] = SpecialistMonitoringReportController::reversDate($data['durations']['actual_start_date']);
            $data['general_information']['agreement_signature_date_reverse'] = SpecialistMonitoringReportController::reversDate($data['general_information']['agreement_signature_date']);
            $data['values']['provisional_sum_and_contingency_value'] = $data['values']['provisional_sum_and_contingency_value'] . ' ' . $data['general_information']['currency_name'];
            $data['all_remarks'] = DB::select('
                select
                   par.id,
                   pcar.implemented_suggestions,
                   pcar.not_implemented_suggestions,
                   pcar.old_suggestion
                from pars_remarks as pcar
                join progress_analysis_reports as par
                  on par.id = pcar.p_a_r_id
                where par.contract_id = ' . $data['contract_id'] . ' and par.id <= ' . $data['id'] . ' and par.id not in (
                    select min(id)
                    from progress_analysis_reports
                    where contract_id = ' . $data['contract_id'] . '
                ) order by id asc
        ');
            $data['progress']['planned_payment'] = number_format($data['progress']['planned_payments_till_now'], 2);
            $data['progress']['actual_payment'] = number_format($data['progress']['actual_payments_till_now'], 2);

//            return response()->json($data, 200);
            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_ARG_BASE_URL') . 'api/acpms/specialist-report'
                , []
                , $data);

            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $request->body);
            }
            return $request->body;
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }
    }

    public static function normalizeProgressData($payments, $index)
    {
        $chartData = [];
        $i = -1;
        $planIndex = 'planned_' . $index;
        $actualIndex = 'actual_' . $index;
        foreach ($payments as $payment) {

            array_push($chartData, [
                "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                "plan" => $payment[$planIndex],
                "actual" => $payment[$actualIndex],
                "total_plan" => $i < 0 ? $payment[$planIndex] :
                    $chartData[$i]['total_plan'] + $payment[$planIndex],
                "total_actual" => $i < 0 ? $payment[$actualIndex] :
                    $chartData[$i]['total_actual'] + $payment[$actualIndex],
            ]);
            $i++;
        }
        return $chartData;
    }

    public static function normalizeSpiData($payments)
    {
        $data = [];
        $i = -1;
        foreach ($payments as $payment) {
            array_push($data, [
                "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                "total_ev" => $i < 0 ? $payment['ev'] : $data[$i]['total_ev'] + $payment['ev'],
                "total_pv" => $i < 0 ? $payment['pv'] : $data[$i]['total_pv'] + $payment['pv'],
                "index" => $i < 0 ? ($payment['pv'] ? $payment['ev'] / $payment['pv'] : 0) :
                    ($data[$i]['total_pv'] ? $data[$i]['total_ev'] / $data[$i]['total_pv'] : 0)
            ]);
            $i++;
        }
        return $data;
    }

    public static function dayDifference($startDate, $endDate)
    {
        if (!$endDate || !$startDate) {
            return null;
        }
        return Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate));
    }

}
