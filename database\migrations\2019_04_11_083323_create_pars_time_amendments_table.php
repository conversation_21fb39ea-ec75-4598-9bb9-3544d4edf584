<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsTimeAmendmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_time_amendments', function (Blueprint $table) {
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('peshnehad_decision_number')->nullable();
            $table->date('amendment_approval_date')->nullable();
            $table->text('amendment_reasons')->nullable();
            $table->date('amendment_start_date')->nullable();
            $table->date('amended_end_date')->nullable();
            $table->integer('amendment_duration')->nullable();
            $table->integer('contract_duration_after_amend')->nullable();
            $table->date('amended_performance_security_end_date')->nullable();
            $table->boolean('is_performance_guaranty_renewal_is_legal')->nullable();
            $table->integer('amended_performance_security_amount')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_time_amendments');
    }
}
