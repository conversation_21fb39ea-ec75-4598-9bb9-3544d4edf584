<?php

namespace NPA\ACPMS\Http\Controllers\Alerts;

use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use Zend\Diactoros\Request;

class ContractController extends Controller
{
    public function assignContractManager(Request $request)
    {
        $contracts = Contract::whereNull('contract_manager_id')
            ->where('created_at', '<=', now()->subDays(2))
            ->get()
            ->toArray();
        return response()->json($contracts);
    }

    public function assignContractSpecialist(Request $request)
    {
        $contracts = Contract::whereNull('specialist_id')
            ->where('created_at', '<=', now()->subDays(2))
            ->get()
            ->toArray();
        return response()->json($contracts);
    }

    public function contractAndPlanInformation()
    {
        //getting dat form cpms
        $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [], [
            'last_synced_id' => 500
        ]);
        $request = json_decode($request->body);
        $contractSignDate = [];
        foreach ($request as $date) {
            if (property_exists($date, 'contract_signed_date')) {
                array_push($contractSignDate, $date->contract_signed_date);
            }
        }
        return response()->json($contractSignDate);
    }

}
