<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class NpaOtherComments extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'report_title',
        'report_date',
        'contract_situation_and_time',
        'contract_initial_planning',
        'other_contract_conditions',
        'contract_progress',
        'amendments_and_contract_edited_plans',
        'other_factors',
        'completion_closeout_and_cancellation',
        'other_points',
        'strength_and_weaknesses',
        'overall_assessment',
        'is_confirmed',
        'is_approved',
        'is_published',
        'contract_id'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
