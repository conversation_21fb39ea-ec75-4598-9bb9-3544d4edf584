<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractRePlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\DomesticContractExecutionLocation;

class FixingNullable1 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE domestic_contract_execution_locations MODIFY village varchar(191) null');

        ContractPlanningFinanceAffairsAndPhysicalProgress::whereNull('major_activities')->update(['major_activities' => 'N/A']);
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY major_activities text not null');

        ContractRePlanningFinanceAffairsAndPhysicalProgress::whereNull('amount')->update(['amount' => 0]);
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY amount integer not null');

        ContractRePlanningFinanceAffairsAndPhysicalProgress::whereNull('physical_progress_percentage')->update(['physical_progress_percentage' => 0]);
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY physical_progress_percentage integer not null');

        ContractRePlanningFinanceAffairsAndPhysicalProgress::whereNull('major_activities')->update(['major_activities' => 'N/A']);
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY major_activities text not null');

        DB::statement('ALTER TABLE contract_progress_payments MODIFY remarks text null');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DomesticContractExecutionLocation::whereNull('village')->update(['village' => 'N/A']);
        DB::statement('ALTER TABLE domestic_contract_execution_locations MODIFY village varchar(191) not null');
        DB::statement('ALTER TABLE contract_planning_finance_affairs_and_physical_progresses MODIFY major_activities text null');

        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY amount integer null');
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY physical_progress_percentage integer null');
        DB::statement('ALTER TABLE contract_re_planning_finance_affairs_and_physical_progresses MODIFY major_activities text null');

        ContractProgressPayment::whereNull('remarks')->update(['remarks' => 'N/A']);
        DB::statement('ALTER TABLE contract_progress_payments MODIFY remarks text not null');

    }
}
