import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SpecialistMonitoringReportComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report.component';
import { SpecialistMonitoringReportListComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-list.component';

const routes: Routes = [
    {
        path: '',
        component: SpecialistMonitoringReportComponent,
        children: [
            {
                path: '',
                redirectTo: 'list',
                pathMatch: 'full'
            },
            {
                path: 'list',
                component: SpecialistMonitoringReportListComponent,
            }
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class SpecialistMonitoringReportRoutes {}


