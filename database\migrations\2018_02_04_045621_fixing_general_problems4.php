<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingGeneralProblems4 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('foreign_contract_execution_verifications', function (Blueprint $table) {
            $table->dropForeign('foreign_contract_execution_verifications_foreign_exec_id_foreign');
            $table->foreign('foreign_exec_id')
                ->references('id')
                ->on('foreign_contract_execution_locations')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
