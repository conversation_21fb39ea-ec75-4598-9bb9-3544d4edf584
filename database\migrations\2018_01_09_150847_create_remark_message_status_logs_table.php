<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRemarkMessageStatusLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('remark_message_status_logs', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('remark_message_id');
            $table->foreign('remark_message_id')
                ->references('id')
                ->on('remark_messages')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->unsignedInteger('remark_message_status_id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('remark_message_status_logs');
    }
}
