<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DeleteUserIdForeignKeyConstraintFromAlertInstancesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('alert_instances', function (Blueprint $table) {
            $table->dropForeign('alert_instances_recipient_user_id_foreign');
        });
        Schema::table('remark_messages', function (Blueprint $table) {
            $table->dropForeign('remark_messages_creator_user_id_foreign');
        });
        Schema::table('user_remarks_status_logs', function (Blueprint $table) {
            $table->dropForeign('user_remarks_status_logs_user_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::table('alert_instances', function (Blueprint $table) {
            $table->foreign('recipient_user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
        });
        Schema::table('remark_messages', function (Blueprint $table) {
            $table->foreign('creator_user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
        });
        Schema::table('user_remarks_status_logs', function (Blueprint $table) {
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');
        });
        Schema::enableForeignKeyConstraints();
    }
}
