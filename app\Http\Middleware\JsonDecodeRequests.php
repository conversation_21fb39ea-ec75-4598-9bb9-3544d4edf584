<?php

namespace NPA\ACPMS\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class JsonDecodeRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $method = $request->getMethod();
        if (($method === 'PUT' || $method === 'POST') && substr($request->header('Content-Type'), 0, 20) === "multipart/form-data;") {
            foreach ($request->all() as $key => $value) {
                $decodedItem = json_decode($value, true);
                if ($decodedItem) {
                    $request[$key] = $decodedItem;
                }else if(strtolower($value) === 'null'){
                    $request[$key] = null;
                }else if(strtolower($value) === 'false'){
                    $request[$key] = false;
                }else if(strtolower($value) === 'true'){
                    $request[$key] = true;
                }else if(strtolower($value) === 'undefined'){
                    $request[$key] = null;
                }
            }
            return $next($request);
        }
        return $next($request);
    }
}
