<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAlertInstanceStatusLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alert_instance_status_logs', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('alert_instance_id');
            $table->foreign('alert_instance_id')
                ->references('id')
                ->on('alert_instances')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->unsignedInteger('alert_status_id');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('alert_instance_status_logs');
    }
}
