<?php

namespace NPA\ACPMS\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Helper;

class ChartController extends Controller
{

    public function getAllContractManagerOfProcurementEntity(Request $request)
    {
        $user_id = $request->query('user_id');
        $resourcesAssigned = Helper::getResourceAssigned($user_id);
        $contractManagerIds = [];
        foreach ($resourcesAssigned as $key => $value) {
            if ($key === 'user_role_sectors') {
                $sectorIds = [];
                foreach ($value as $item) {
                    array_push($sectorIds, $item->sector_id);
                }
                $contracts = DB::select('
                        select DISTINCT 
                            contract_manager_id 
                        from contracts 
                        where sector_id in (' . implode(',', $sectorIds) . ') and contract_manager_id is not null
                        ');
                foreach ($contracts as $contract) {
                    array_push($contractManagerIds, $contract->contract_manager_id);
                }
            } else if ($key === 'user_role_pes') {
                $peIds = [];
                foreach ($value as $item) {
                    array_push($peIds, $item->procurement_entity_id);
                }
                $contracts = DB::select('
                        select DISTINCT 
                            contract_manager_id 
                        from contracts 
                        where procurement_entity_id in (' . implode(',', $peIds) . ') and contract_manager_id is not null
                        ');
                foreach ($contracts as $contract) {
                    array_push($contractManagerIds, $contract->contract_manager_id);
                }
            }
        }
        $data = Helper::getUsersByIds($contractManagerIds);
        return response()->json($data, 200);
    }

    public function getAllSectorSpecialist(Request $request)
    {
        $user_id = $request->query('user_id');
        $resourcesAssigned = Helper::getResourceAssigned($user_id);
        $specialistIds = [];
        foreach ($resourcesAssigned as $key => $value) {
            if ($key === 'user_role_sectors') {
                $sectorIds = [];
                foreach ($value as $item) {
                    array_push($sectorIds, $item->sector_id);
                }
                $contracts = DB::select('
                        select DISTINCT 
                            cs.specialist_id 
                        from contract_specialists as cs
                        join contracts as c
                          on c.id = cs.contract_id
                        where c.sector_id in (' . implode(',', $sectorIds) . ') and cs.specialist_id is not null
                        ');
                foreach ($contracts as $contract) {
                    array_push($specialistIds, $contract->specialist_id);
                }
            }
        }
        $data = Helper::getUsersByIds($specialistIds);
        return response()->json($data, 200);
    }

    public function getAllAwardAuthorities()
    {
        $data = Helper::getAwardAuthority();
        return response()->json($data, 200);

    }

    public function getAllCpmManagers()
    {
        $data = Helper::getCpmManager();
        return response()->json($data, 200);
    }


}
