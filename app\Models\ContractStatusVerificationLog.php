<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusVerificationLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'is_confirmed',
        'is_approved',
        'is_published',
        'published_date',
        'contract_status_id',
        'contract_status_verification_id'
    ];

    function contract_status_verification()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusVerification');
    }
}
