<?php
/**
 * Created by PhpStorm.
 * User: Faramarz
 * Date: 8/30/2018
 * Time: 9:46 AM
 */

namespace NPA\ACPMS\Types;
use Google_Client;
use Google_Service_Drive;
use Google_Service_Sheets_ClearValuesRequest;
use Google_Service_Sheets_ValueRange;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;

class SyncDataWithGoogleSheet
{
    public static function operate()
    {
        $client = new \Google_Client();
        $file_name = config('custom.GOOGLE_APPLICATION_CREDENTIALS');
        $file_contents = file_get_contents($file_name);

        $client->setAuthConfig(json_decode($file_contents, true));
        $client->addScope(Google_Service_Drive::DRIVE);
        $sheet = new \Google_Service_Sheets($client);

        $procurementEntiies = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity');
        $contractStatuses = DropDowns::getAllValues(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus'
        );

        $requests = DB::select('
                        SELECT c.id as contract_id,c.contract_number, c.procurement_entity_id,
                        cs.status_id, if(jdate(cs.date)=1, \'not-available\',jdate(cs.date)) as contract_status_date, if(jdate(date(c.created_at))=1, \'not-available\', jdate(date(c.created_at))) as contract_created_date,
                        ifnull(c.is_published, 0) as is_published,if(jdate(date(c.published_date))=1, \'not-available\', jdate(date(c.published_date))) as published_date,
                          ifnull((
                               select 
                               (ifnull(cd.actual_value, 0) +  ifnull(amendment.amount, 0) + ifnull(psc.provisional_sum_and_contingency, 0)) as total_contract_value 
                               from contract_details as cd
                               left join (
                                   select 
                                   (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                   a.contract_id as contract_id
                                   from amendments as a
                                   left join cost_amendments as ca 
                                   on ca.amendment_id = a.id
                                   left join time_and_cost_amendments as tca
                                   on tca.amendment_id = a.id
                                   where a.is_approved = true
                                   group by  a.contract_id 
                        
                               ) as amendment
                               on cd.contract_id = amendment.contract_id
                               left join provisional_sum_and_contingencies as psc
                               on psc.contract_detail_id = cd.id	  
                               where cd.contract_id = c.id
                           ), "") as contract_price
                        
                        FROM `contracts` c
                        left join contract_statuses cs on c.id=cs.contract_id 
            order by c.id asc 
            ');
        foreach ($requests as &$contract) {
            $entity = DropDowns::getElementTypeById($procurementEntiies, $contract->procurement_entity_id);
            if (sizeof($entity) > 0) {
                $entity = $entity[0];
                $contract->procurment_entity_name = $entity['name_da'];
                unset($contract->procurement_entity_id);
            } else {
                $contract->procurment_entity_name = 'نا معلوم';
                unset($contract->procurement_entity_id);
            }
            $contractStatus = DropDowns::getElementTypeById($contractStatuses, $contract->status_id);
            if (sizeof($contractStatus) > 0) {
                $contractStatus = $contractStatus[0];
                $contract->contract_status = $contractStatus['name_da'];
                unset($contract->status_id);
            } else {
                $contract->contract_status = 'نا معلوم';
                unset($contract->status_id);
            }
        }
        $spreadsheetId = config('custom.GOOGLE_SHEET_ID');
        $inputOption = ['valueInputOption' => 'USER_ENTERED'];
        $updatedValues = [];
        $columnHeadings = [];
        $headingData = isset($requests) && isset($requests[0]) ? $requests[0] : [];
        foreach ($headingData as $key => $value) {
            $columnHeadings[] = $key;
        }
        $updatedValues[] = $columnHeadings;
        foreach ($requests as $values) {
            $row = [];
            foreach ($values as $key => $value) {
                $row[] = $value;
            }
            $updatedValues[] = $row;
        }
        $range = "Contract_Progress!A1:ZZ1000000";
        $updateBody = new Google_Service_Sheets_ValueRange([
            'range' => $range,
            'majorDimension' => 'ROWS',
            'values' => $updatedValues
        ]);
        $clearGoogleSheetValuesService = new Google_Service_Sheets_ClearValuesRequest();
        $sheet->spreadsheets_values->clear($spreadsheetId, $range, $clearGoogleSheetValuesService);
        $sheet->spreadsheets_values->update($spreadsheetId, $range, $updateBody, $inputOption);
    }
}
