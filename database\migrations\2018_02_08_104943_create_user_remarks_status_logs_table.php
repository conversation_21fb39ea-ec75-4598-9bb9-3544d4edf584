<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUserRemarksStatusLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('remark_message_status_logs');

        Schema::create('user_remarks_status_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->unsignedInteger('remark_id');
            $table->foreign('remark_id')->references('id')->on('remarks')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->unsignedInteger('remark_status_id');
            $table->timestamps();
            $table->unique([
                'user_id',
                'remark_id',
                'remark_status_id'
            ], 'user_remarks_status_logs_uniques');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_remarks_status_logs');

        Schema::create('remark_message_status_logs', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('remark_message_id');
            $table->foreign('remark_message_id')
                ->references('id')
                ->on('remark_messages')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->unsignedInteger('remark_message_status_id');

            $table->unsignedInteger('user_id');
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->timestamps();
        });
    }
}
