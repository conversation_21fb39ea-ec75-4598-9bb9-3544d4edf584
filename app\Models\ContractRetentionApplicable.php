<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionApplicable extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_retention_id',
        'is_returned'
    ];

    public function contract_retention_applicable_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRetentionApplicableLog');
    }

    public function contract_retention_returned()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionReturned');
    }

    public function contract_retention_not_returned()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionNotReturned');
    }

    public function contract_retention()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetention');
    }
}
