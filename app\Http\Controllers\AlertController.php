<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Query;
use NPA\ACPMS\Models\Alert;
use NPA\ACPMS\Models\AlertInstanceStatusLog;
use NPA\ACPMS\Models\RemarkMessage;
use Throwable;

class AlertController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @internal param $statusSlug
     */
    public function index(Request $request)
    {
        try {
            $listType = $request->query('list_type');
            if (!$listType) {
                return Error::composeResponse(new Exception('List type is required'));
            }
            $rawAlerts = $this->getAlertsByListType($request, $listType);
            $alerts = [];
            foreach ($rawAlerts as $r) {
                $alerts[] = [
                    'id' => $r->id,
                    'instance_id' => $r->alert_instances[0]->id,
                    'created_at' => $r->created_at,
                    'contents' => $r->contents,
                    'status_id' => $r->alert_instances[0]->alert_instance_status_logs[0]->alert_status_id,
                ];
            }
            return response()->json($alerts);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function check(Request $request)
    {
        try {
            $listType = $request->query('list_type');
            if (!$listType) {
                return Error::composeResponse(new Exception('list_type is required'));
            }

            $rawAlerts = $this->getAlertsByListType($request, $listType);
            try {
                $lastAlertTimestamp = $rawAlerts->first()['alert_instances'][0]['alert_instance_status_logs'][0]['created_at'];
                return response()->json([
                    'newAlerts' => $lastAlertTimestamp->getTimestamp()
                ]);
            } catch (Throwable $t) {
                return response()->json(['state' => 'up-to-date']);
            }
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param $alertInstanceId
     * @return \Illuminate\Http\Response
     * @internal param Alert $alert
     */
    public function show($alertInstanceId)
    {
        try {

            $statusIdNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'new')['id'];

            $alert = Alert
                ::whereHas('alert_instances', function ($query) use ($alertInstanceId) {
                    $query->where('id', $alertInstanceId);
                })->with(['alert_instances' => function ($query) use ($alertInstanceId) {
                    $query->where('id', $alertInstanceId)
                        ->with(['alert_instance_status_logs' => function ($query) {
                            $query->orderBy('id', 'desc');
                        }]);
                }])->first();

            $tempData = DB::table('contracts')
                ->leftJoin('remarks', 'contracts.id', '=', 'remarks.contract_id')
                ->leftJoin('remark_messages', 'remarks.id', '=', 'remark_messages.remark_id')
                ->select('contracts.npa_identification_number as npa_identification_number',
                    'contracts.contract_number',
                    'remarks.*', 'remark_messages.*')
                ->where('remark_messages.alert_id', '=', $alert->id)->first();

//            return response()->json($tempData);
            $statusId = $alert->alert_instances[0]->alert_instance_status_logs[0]->alert_status_id;

            if ($statusId == $statusIdNew) {
                $statusIdRead = DropDowns::getBySlug(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                    'read')['id'];

                AlertInstanceStatusLog::create([
                    'alert_instance_id' => $alertInstanceId,
                    'alert_status_id' => $statusIdRead,
                ]);
                $statusId = $statusIdRead;
            }

            return response()->json([
                'id' => $alert->id,
                'instance_id' => $alert->alert_instances[0]->id,
                'created_at' => $alert->created_at,
                'contents' => $alert->contents,
                'status_id' => $statusId,
                'npa_identification_number' => $tempData->npa_identification_number,
                'contract_number' => $tempData->contract_number,
                'remark_title' => $tempData->title,
                'url' => $tempData->url,
            ]);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\Alert $alert
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Alert $alert)
    {
        //
    }

    /**
     * @param Request $request
     * @param $listType
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     * @throws Exception
     * @internal param $statusSlug
     */
    private function getAlertsByListType(Request $request, $listType)
    {
        if ($listType != 'working' && $listType != 'new_only') {
            throw Error::composeResponse(new Exception('List type is not supported.'));
        }

        $statuses = [];

        if ($listType == 'working') {
            $statusIdNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'new')['id'];
            $statusIdRead = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'read')['id'];

            $statuses[] = $statusIdRead;
            $statuses[] = $statusIdNew;

        } else if ($listType == 'new_only') {

            $statusIdNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'new')['id'];
            $statuses[] = $statusIdNew;
        }

        $rawAlerts = Alert::whereRaw('
                alerts.id in
                (
                    select distinct ai.alert_id
                    from 
                        alert_instances as ai
                    join 
                        alert_instance_status_logs as aisl on ai.id = aisl.alert_instance_id
                    where 
                        ai.recipient_user_id = ' . $request['user_id'] . ' and 
                        aisl.id = (
                            select max(inner_aisl.id)
                            from alert_instance_status_logs as inner_aisl
                            where inner_aisl.alert_instance_id = ai.id
                        ) and
                        aisl.alert_status_id in (' . implode(',', $statuses) . ')
                )
                    
            ')->with(['alert_instances' => function ($query) use ($request) {
            $query
//                ->where('recipient_user_id', auth('api')->user()->id) // todo remove this and the upper changes in user_id
                ->where('recipient_user_id', $request['user_id'])
                ->with(['alert_instance_status_logs' => function ($query) {
                    $query->orderBy('id', 'desc');
                }]);
        }])
            ->orderBy('id', 'desc')
            ->get();
        return $rawAlerts;
    }
}
