<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAmendmentDateColumnToAmendmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('amendments', function (Blueprint $table) {
            $table->date('amendment_date')->after('type_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('amendments', function (Blueprint $table) {
            $table->dropColumn('amendment_date');
        });

        if (Schema::hasColumn('amendments', 'amendment_date')) {
            Schema::table('amendments', function (Blueprint $table) {
                $table->dropColumn('amendment_date');
            });
        }
    }
}
