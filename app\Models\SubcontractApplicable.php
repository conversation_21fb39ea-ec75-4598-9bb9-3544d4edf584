<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class SubcontractApplicable extends Model
{

    protected $guarded = ['id'];

    public static $fields = [
        'subcontract_id',
        'is_subcontract_signed'
    ];

    public function subcontract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Subcontract');
    }

    public function subcontract_signed()
    {
        return $this->hasOne('NPA\ACPMS\Models\SubcontractSigned');
    }

}
