<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CheckStatusTriggerBeforePublish extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec("
            create trigger `check_status_trigger_before_publish`
            before update on `contracts`
            for each row
            exit_label:begin
                declare is_published integer default null;
 
                if old.is_published = new.is_published or new.is_published = false then
                    leave exit_label;
                end if;
                
            select count(*) into is_published from contract_statuses as cs
            WHERE cs.contract_id = new.id and (cs.status_id = 7);
                
            if is_published > 0 THEN
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0012|||';
            end if;
            set is_published = null;
            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getpdo()->exec('DROP TRIGGER `check_status_trigger_before_publish`');
    }
}
