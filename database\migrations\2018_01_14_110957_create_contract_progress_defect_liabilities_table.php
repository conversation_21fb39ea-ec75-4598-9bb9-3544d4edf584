<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateContractProgressDefectLiabilitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_progress_defect_liabilities', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('contract_id');
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->float('amount');
            $table->dateTime('date');
            $table->text('remarks')->nullable();
            $table->boolean('is_confirmed')
                ->default(false);
            $table->boolean('is_approved')
                ->default(false);
            $table->boolean('is_published')
                ->default(false);
            $table->dateTime('published_date')
                ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_progress_defect_liabilities');
    }
}
