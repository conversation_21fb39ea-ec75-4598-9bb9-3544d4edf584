<?php

namespace NPA\ACPMS\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Http;

class AttachToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     * @throws Exception
     */
    public function handle(Request $request, Closure $next)
    {
        if (config('app.env') !== 'production') {
            return $next($request);
        }
        $session = $request->header('authorization');
        if (!$session) {
            return response()->json('No session provided', 401);
        }
        $headers = ['Authorization' => $session];
        $tokenRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/token-verify?key=' . urlencode(config('custom.APP_KEY')), $headers);
        if ($tokenRequest->status_code >= 300) {
            return response()->json(json_decode($tokenRequest->body), $tokenRequest->status_code);
        }
        $response = json_decode($tokenRequest->body);
        $request['user_id'] = $response->user_id;
        $request['username'] = $response->username;
        $request['email'] = $response->email;
        $request['role'] = $response->role;
        $response = $next($request);

        $token = $tokenRequest->headers['authorization'];
        if ($token) {
            $decodedSession = json_decode($session);
            $decodedSession->token = $token;
            $response->header('Authorization', json_encode($decodedSession));

        }
        return $response;
    }
}
