<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractorPerformanceAssessment extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'execution_management_grade_id',
        'work_quality_grade_id',
        'execution_based_on_contract_grade_id',
        'work_progress_based_on_plan_grade_id',
        'contract_status_id'
    ];

    public function contract_status()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function contractor_performance_assessment_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractorPerformanceAssessmentLog');
    }
}
