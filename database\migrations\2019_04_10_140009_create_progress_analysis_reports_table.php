<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProgressAnalysisReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('progress_analysis_reports', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('serial_number');
            $table->integer('year');
            $table->integer('month');
            $table->unsignedInteger('contract_id');
            $table->timestamp('confirmation_timestamp')->nullable();
            $table->timestamp('generation_timestamp')->useCurrent();
            $table->boolean('is_auto_saved');
            $table->integer('creator_user_id')->nullable();
            $table->text('user_full_name')->nullable();
            $table->boolean('is_confirmed')->nullable();
            $table->boolean('is_approved')->nullable();
            $table->unique(['serial_number', 'contract_id', 'year', 'month'], 'sn_month_year_contract_unique');
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('progress_analysis_reports');
    }
}
