import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SpecialistMonitoringReportRoutes } from './specialist-monitoring-report.routes';
import { SpecialistMonitoringReportComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report.component';
import { SpecialistMonitoringReportListComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-list.component';
import { SpecialistMonitoringReportProvider } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report.provider';
import { SharedModule } from '../shared/shared.module';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule } from '@angular/material/dialog';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { NgxEchartsModule } from 'ngx-echarts';
import { SpecialistMonitoringReportViewDialogComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-view-dialog/specialist-monitoring-report-view-dialog.component';
import { SpecialistMonitoringReportEditDialogComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/specialist-monitoring-report-edit-dialog/specialist-monitoring-report-edit-dialog.component';
import { MonitoringReportAddEditDialogComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/monitoring-report-add-edit-dialog/monitoring-report-add-edit-dialog.component';
import { MonitoringReportViewDialogComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/monitoring-report-view-dialog/monitoring-report-view-dialog.component';
import { ContractIntroductionComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/contract-introduction/contract-introduction.component';
import { ConfirmationDialogComponent } from '../challenges-and-remarks/specialist-monitoring-report/specialist-monitoring-report-list/confirmation-dialog/confirmation-dialog.component';
import { JalaliPipe } from '../shared/pipes/jalali.pipe';

@NgModule({
    imports: [
        CommonModule,
        SpecialistMonitoringReportRoutes,
        MatCardModule,
        MatTableModule,
        MatToolbarModule,
        MatIconModule,
        MatButtonModule,
        MatFormFieldModule,
        MatInputModule,
        MatTooltipModule,
        MatSelectModule,
        MatCheckboxModule,
        MatDialogModule,
        FlexLayoutModule,
        MatDatepickerModule,
        MatNativeDateModule,
        ReactiveFormsModule,
        MatRadioModule,
        FormsModule,
        MatTabsModule,
        SharedModule,
        NgxEchartsModule,
    ],
    declarations: [
        SpecialistMonitoringReportComponent,
        SpecialistMonitoringReportListComponent,
        SpecialistMonitoringReportViewDialogComponent,
        SpecialistMonitoringReportEditDialogComponent,
        MonitoringReportAddEditDialogComponent,
        MonitoringReportViewDialogComponent,
        ContractIntroductionComponent,
        ConfirmationDialogComponent
    ],
    entryComponents: [
        SpecialistMonitoringReportViewDialogComponent,
        SpecialistMonitoringReportEditDialogComponent,
        MonitoringReportAddEditDialogComponent,
        MonitoringReportViewDialogComponent,
        ConfirmationDialogComponent
    ],
    providers: [
        SpecialistMonitoringReportProvider,
        JalaliPipe
    ]
})
export class SpecialistMonitoringReportModule { }
