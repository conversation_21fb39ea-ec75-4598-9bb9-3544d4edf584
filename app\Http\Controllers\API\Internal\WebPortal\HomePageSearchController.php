<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;

class HomePageSearchController extends Controller
{
    public function index(Request $request)
    {
        $queryParams = $request->all();
        if (array_has($queryParams, 'projectIds') && $queryParams['projectIds'] > 0) {
            $queryParams['projectIds'] = implode(', ', $queryParams['projectIds']);
        }
        if (array_has($queryParams, 'company_id')) {
            $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') .
                'api/getCompaniesByName?lang=' . $request['lang'] . '&value=' . $request['company_id'] . '&type=id');
            $remoteRequest = json_decode($remoteRequest->body, true);
            $queryParams['licence_number'] = $remoteRequest['licence_number'];
        }

        $queryString = $this->prepareQueryStrings($queryParams);
        $data = DB::select('
                select 
                    c.npa_identification_number,
                    c.procurement_entity_id,
                    c.project_id
                    from contracts c
                     ' . $queryString['joins'] . '
                where c.is_published = true 
                ' . $queryString['query_string'] . '
        ');
        return response()->json($data, 200);
    }

    public function prepareQueryStrings($queryArray)
    {
        $data['joins'] = '';
        $data['query_string'] = '';

        foreach ($queryArray as $key => $value) {
            switch ($key) {
                case 'npa_identification_number':
                    $data['query_string'] .= ' and c.npa_identification_number = \'' . $value . '\'';
                    break;
                case 'procurement_entity_id':
                    $data['query_string'] .= ' and c.procurement_entity_id = ' . $value;
                    break;
                case 'projectIds':
                    $data['query_string'] .= ' and c.project_id in (' . $value . ') ';
                    break;
                case 'procurement_type_id':
                    $data['query_string'] .= ' and c.procurement_type_id = ' . $value;
                    break;
                case 'procurement_method_id':
                    $data['query_string'] .= ' and c.procurement_method_id = ' . $value;
                    break;
                case 'donor_id':
                    $data['joins'] .= ' join contract_donors as qcdj on qcdj.contract_id = c.id ';
                    $data['query_string'] .= ' and qcdj.donor_id = ' . $value;
                    break;
                case 'licence_number':
                    $data['query_string'] .= ' and c.id in
                                            (
                                            select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            where q_inner_c.id in (
                                                select
                                                cg.contract_id 
                                                from company_general_informations as cg
                                                join companies as com
                                                    on com.company_general_information_id = cg.id
                                                where com.license_number = \'' . $value . '\')
                                            ) ';
                    break;
            }
        }
        return $data;

    }
}
