<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class SubcontractSigned extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'subcontract_applicable_id',
        'has_p_e_approved_in_written',
        'approval_date',
        'end_date',
        'value',
        'percentage',
        'currency_type',
        'currency_type_id',
        'company_licence_end_date',
        'company_contact_number',
        'company_id',
        'remarks'
    ];

    public function subcontract_applicable()
    {
        return $this->belongsTo('NPA\ACPMS\Models\SubcontractApplicable');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

}
