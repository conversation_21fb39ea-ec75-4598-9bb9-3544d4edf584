<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractDetailsVerification;
use Illuminate\Http\Request;

class ContractDetailsVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $data['contract_detail_id'] = $data['parent_id'];
            unset($data['parent_id']);
            $verificationData = ContractDetailsVerification::create(array_only($data, ContractDetailsVerification::$fields));
            return response()->json($verificationData, 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\models\ContractDetailsVerification $contractDetailsVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractDetailsVerification)
    {


        $data = ContractDetailsVerification::where('contract_detail_id', $contractDetailsVerification)->first();
        return response()->json($data, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\models\ContractDetailsVerification $contractDetailsVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractDetailsVerification $contractDetailsVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\models\ContractDetailsVerification $contractDetailsVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractDetailsId)
    {
        $data = $request->all();
        $data['contract_detail_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contactDetailsVerification =
                ContractDetailsVerification::where('contract_detail_id', $contractDetailsId)->first();

            $isUpdated = $contactDetailsVerification->update(array_only($data, ContractDetailsVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\models\ContractDetailsVerification $contractDetailsVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractDetailsVerification $contractDetailsVerification)
    {
        //
    }
}
