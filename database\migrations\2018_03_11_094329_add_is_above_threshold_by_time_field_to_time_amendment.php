<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIsAboveThresholdByTimeFieldToTimeAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->boolean('is_above_threshold_by_time')
                ->nullable()
                ->after('is_above_threshold');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('time_amendments', 'is_above_threshold_by_time')) {
            Schema::table('time_amendments', function (Blueprint $table) {
                $table->dropColumn('is_above_threshold_by_time');
            });
        }
    }
}
