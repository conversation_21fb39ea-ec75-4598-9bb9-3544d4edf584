<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\BI;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Http\Controllers\Controller;
use Carbon\Carbon;
use Morilog\Jalali\jDateTime;



class <PERSON>iController extends Controller
{
  public function status(Request $request)
  {
    $request = $request->all();
    $join = '';
    $condition = '';
    if (isset($request['status_id']) && $request['status_id']) {
      $condition .= '
      and csl.status_id = ' . $request['status_id'];
    }
    if (isset($request['status_ids']) && $request['status_ids']) {
      $condition .= '
      and csl.status_id in  (' . $request['status_ids'] . ') ';
    }
    if (isset($request['is_challenges']) && $request['is_challenges']) {
      $condition .= '
      and c.id in  (select contract_id from challenges_and_remarks) ';
    }
    if (isset($request['ids']) && $request['ids']) {
      $condition .= '
      and c.id in (' . $request['ids'] . ')';
    }

    $data['status'] = \DB::select('
            select
                cs.status_id as status_id,
                    sum(
                        (ifnull(cd.actual_value, 0) +
                        ifnull(psc.provisional_sum_and_contingency, 0) +
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    )  as contract_value,
                     count(distinct c.id) as count,
                     GROUP_CONCAT(c.id) as ids
            from (
                  select
                      csl.id, csl.contract_id, csl.status_id
                  from contract_status_logs as csl
                  where csl.id in
                    ( SELECT max(id) as max_id FROM contract_status_logs group by contract_id)
              ) as cs
            left join (
                 select
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                 from amendments as a
                 left join cost_amendments as ca
                     on ca.amendment_id = a.id
                 left join time_and_cost_amendments as tca
                     on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by  a.contract_id
            ) as amendment
                on cs.contract_id = amendment.contract_id
            join contracts as c
                on c.id = cs.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id

            where c.is_published = true
            group by cs.status_id
			');


    $data['entity'] = \DB::select('
            select
              c.procurement_entity_id,
              sum(
                  (ifnull(cd.actual_value, 0) +
                  ifnull(psc.provisional_sum_and_contingency, 0) +
                  ifnull(amendment.amount, 0)) *
                  if(c.exchange_rate, c.exchange_rate, 1)
              ) as value,
              count(distinct c.id) as contract_count
            from contracts as c
            join contract_details as cd
              on c.id = cd.contract_id
            join contract_details_verifications cdv
              on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join (
                select
                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                    a.contract_id as contract_id
                from amendments as a
                left join cost_amendments as ca
                    on ca.amendment_id = a.id
                left join time_and_cost_amendments as tca
                    on tca.amendment_id = a.id
                where a.is_approved = true
                group by  a.contract_id

            ) as amendment
                  on c.id = amendment.contract_id
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            join (
                      select *
                      FROM contract_status_logs
                      where id in (
                        select  max(id) as max_id
                        FROM contract_status_logs
                        group by contract_id)
                      ) as csl
                on csl.contract_id = c.id
            where c.is_published = true  ' . $condition . '
            group by c.procurement_entity_id
            order by contract_count
        ');


    $data['sector'] = \DB::select('
    select
        c.sector_id,
        sum(
            (ifnull(cd.actual_value, 0) +
            ifnull(psc.provisional_sum_and_contingency, 0) +
            ifnull(amendment.amount, 0)) *
            if(c.exchange_rate, c.exchange_rate, 1)
        ) as value,
        count(distinct c.id) as contract_count
      from contracts as c
      join contract_details as cd
        on c.id = cd.contract_id
      join contract_details_verifications cdv
        on cd.id = cdv.contract_detail_id and cdv.is_approved = true
      left join (
          select
              (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
              a.contract_id as contract_id
          from amendments as a
          left join cost_amendments as ca
              on ca.amendment_id = a.id
          left join time_and_cost_amendments as tca
              on tca.amendment_id = a.id
          where a.is_approved = true
          group by  a.contract_id

      ) as amendment
            on c.id = amendment.contract_id
      left join provisional_sum_and_contingencies as psc
              on psc.contract_detail_id = cd.id
      join (
            select *
            FROM contract_status_logs
            where id in (
              select  max(id) as max_id
              FROM contract_status_logs
              group by contract_id)
            ) as csl
      on csl.contract_id = c.id
      ' . $join . '
      where c.is_published = true   ' . $condition . '
      group by c.sector_id
      order by contract_count
    ');

    $data['donor'] = \DB::select('
    select

        sum(
            (ifnull(cd.actual_value, 0) +
            ifnull(psc.provisional_sum_and_contingency, 0) +
            ifnull(amendment.amount, 0)) *
            if(c.exchange_rate, c.exchange_rate, 1)
        ) as value,
        count(distinct c.id) as contract_count,
        cdo.donor_id
    from contracts as c
    join contract_donors as cdo
      on c.id = cdo.contract_id
    join contract_details as cd
      on c.id = cd.contract_id
    join contract_details_verifications cdv
      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
    left join (
        select
            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
            a.contract_id as contract_id
        from amendments as a
        left join cost_amendments as ca
            on ca.amendment_id = a.id
        left join time_and_cost_amendments as tca
            on tca.amendment_id = a.id
        where a.is_approved = true
        group by  a.contract_id

    ) as amendment
          on c.id = amendment.contract_id
    left join provisional_sum_and_contingencies as psc
            on psc.contract_detail_id = cd.id
    join (
            select *
            FROM contract_status_logs
            where id in (
              select  max(id) as max_id
              FROM contract_status_logs
              group by contract_id)
            ) as csl
      on csl.contract_id = c.id
    ' . $join . '
    where c.is_published = true  ' . $condition . '
    group by cdo.donor_id
    order by contract_count
    ');


    $data['provinces'] = DB::select('

            select
              count(distinct c.id) as contract_count,
              sum(
                    (ifnull(cd.actual_value, 0) +
                    ifnull(psc.provisional_sum_and_contingency, 0) +
                    ifnull(amendment.amount, 0)) *
                    if(c.exchange_rate, c.exchange_rate, 1)
                ) as value,
              p.name_en,
              p.name_da as name_prs,
              p.name_pa as name_ps


            from contracts as c
            join domestic_contract_execution_locations as dc
                on dc.contract_id = c.id
            join temp_districts as d
                on d.id = dc.district_id
            join temp_provinces as p
                on p.id = d.temp_province_id
                join contract_details as cd
                on c.id = cd.contract_id
            join contract_details_verifications cdv
              on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join (
                select
                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                    a.contract_id as contract_id
                from amendments as a
                left join cost_amendments as ca
                    on ca.amendment_id = a.id
                left join time_and_cost_amendments as tca
                    on tca.amendment_id = a.id
                where a.is_approved = true
                group by  a.contract_id

            ) as amendment
                  on c.id = amendment.contract_id
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            join (
                    select *
                    FROM contract_status_logs
                    where id in (
                      select  max(id) as max_id
                      FROM contract_status_logs
                      group by contract_id)
                    ) as csl
              on csl.contract_id = c.id
            ' . $join . '
            where c.is_published = true   ' . $condition . '
            group by p.id, p.name_en, p.name_da, p.name_pa


    ');
    $data['vendor'] = DB::select('
      select
        com.license_number,
        sum(
            (ifnull(cd.actual_value, 0) +
            ifnull(psc.provisional_sum_and_contingency, 0) +
            ifnull(amendment.amount, 0)) *
            if(c.exchange_rate, c.exchange_rate, 1)
        ) as value,
        count( distinct c.id) as contract_count
      from contracts as c
      inner join company_general_informations as cgi
        on c.id = cgi.contract_id
      inner join companies as com
        on cgi.id = com.company_general_information_id
      join contract_details as cd
        on c.id = cd.contract_id
      join contract_details_verifications cdv
        on cd.id = cdv.contract_detail_id and cdv.is_approved = true
      left join (
          select
              (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
              a.contract_id as contract_id
          from amendments as a
          left join cost_amendments as ca
              on ca.amendment_id = a.id
          left join time_and_cost_amendments as tca
              on tca.amendment_id = a.id
          where a.is_approved = true
          group by  a.contract_id

      ) as amendment
            on c.id = amendment.contract_id
      left join provisional_sum_and_contingencies as psc
              on psc.contract_detail_id = cd.id
      join (
              select *
              FROM contract_status_logs
              where id in (
                select  max(id) as max_id
                FROM contract_status_logs
                group by contract_id)
              ) as csl
      on csl.contract_id = c.id
        ' . $join . '
      where c.is_published = true
      ' . $condition . '
      group by com.license_number
      order by contract_count Desc
      limit 100
    ');



    return $data;
  }



  public function physical(Request $request)
  {
    $statusId = $request->query('status_id');

    $now = jDateTime::toJalali(Carbon::now()->year, Carbon::now()->month, Carbon::now()->day);
    $monthId = $now[1];
    $year = $now[0];
    if ($monthId > 9) {
      $year = $year + 1;
    }

    $join = '';
    $condition = '';
    if ($statusId) {
      $join .= '
          join contract_status_logs
          as csl
            on c.id = csl.contract_id
      ';
      $condition .= '
      and csl.status_id = ' . $statusId;
    }
    $data['planned'] = \DB::selectOne('
    select
      sum(
          ((ifnull(cd.actual_value, 0) +
          ifnull(psc.provisional_sum_and_contingency, 0) +
          ifnull(amendment.amount, 0)) *
          if(c.exchange_rate, c.exchange_rate, 1))
          * pr.physical_progress
      )  as work_value

    from contracts as c
    join contract_details as cd
      on c.id = cd.contract_id
    join contract_details_verifications cdv
      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
    left join (
        select
            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
            a.contract_id as contract_id
        from amendments as a
        left join cost_amendments as ca
            on ca.amendment_id = a.id
        left join time_and_cost_amendments as tca
            on tca.amendment_id = a.id
        where a.is_approved = true
        group by  a.contract_id

    ) as amendment
          on c.id = amendment.contract_id
    left join provisional_sum_and_contingencies as psc
            on psc.contract_detail_id = cd.id
    left join
      (
        select
          c.id,
            sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) / 100 )  as physical_progress
          from contracts as c
          left join (
            select
              contract_id, sum(physical_progress_percentage) as physical_progress_percentage
            from contract_planning_finance_affairs_and_physical_progresses
            where (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  (year < ' . $year . ')
            group by contract_id
            ) as cp
            on	c.id = cp.contract_id
          left join  (
            select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage
            from (
                select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                    on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                  where crv.is_approved = true
                  group by crd.contract_id
                ) as cr

            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
              on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
            where (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
            group by cr.contract_id
          ) as re_cp
            on c.id = re_cp.contract_id
          where c.is_published = true
          group by c.id
      ) as pr
      on pr.id = c.id
    where c.is_published = true
        ');

    $data['actual'] = \DB::selectOne(
      '
        select
          sum(
              ((ifnull(cd.actual_value, 0) +
              ifnull(psc.provisional_sum_and_contingency, 0) +
              ifnull(amendment.amount, 0)) *
              if(c.exchange_rate, c.exchange_rate, 1))
              * pr.physical_progress
          )  as work_value

        from contracts as c
        join contract_details as cd
          on c.id = cd.contract_id
        join contract_details_verifications cdv
          on cd.id = cdv.contract_detail_id and cdv.is_approved = true
        left join (
            select
                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                a.contract_id as contract_id
            from amendments as a
            left join cost_amendments as ca
                on ca.amendment_id = a.id
            left join time_and_cost_amendments as tca
                on tca.amendment_id = a.id
            where a.is_approved = true
            group by  a.contract_id

        ) as amendment
              on c.id = amendment.contract_id
        left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id
        left join
          (
            select
              c.id,
              sum(cpp.physical_progress_percentage / 100)  physical_progress
            from contract_progress_payments as cpp
            join contracts as c
              on c.id = cpp.contract_id
            where cpp.is_approved = true and c.is_published = true and
            (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  (cpp.year < ' . $year . ')
            group by c.id
          ) as pr
          on pr.id = c.id

        where c.is_published = true
      '
    );
    $data['spi_count'] = \DB::selectOne(
      '
          select
            count(if( plan.physical_progress != 0 , if((actual.physical_progress / plan.physical_progress) >= 1 , c.id, null ) , null) ) as on_time,
            count(if( plan.physical_progress != 0 , if((actual.physical_progress / plan.physical_progress) < 1 , c.id, null ) , c.id) ) as late

          from contracts as c
          left join (
          select
            c.id,
            sum(cpp.physical_progress_percentage / 100)  physical_progress
          from contract_progress_payments as cpp
          join contracts as c
            on c.id = cpp.contract_id
          where cpp.is_approved = true and c.is_published = true and
          (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  (cpp.year < ' . $year . ')
          group by c.id
          ) as actual on actual.id = c.id


          left join (
          select
          c.id,
            sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) / 100 )  as physical_progress
          from contracts as c
          left join (
            select
              contract_id, sum(physical_progress_percentage) as physical_progress_percentage
            from contract_planning_finance_affairs_and_physical_progresses
            where (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  (year < ' . $year . ')
            group by contract_id
            ) as cp
            on	c.id = cp.contract_id
          left join  (
            select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage
            from (
                select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                    on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                  where crv.is_approved = true
                  group by crd.contract_id
                ) as cr

            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
              on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
            where (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
            group by cr.contract_id
          ) as re_cp
            on c.id = re_cp.contract_id
          where c.is_published = true
          group by c.id
          ) as plan on plan.id = c.id
            where c.is_published = true
      '
    );
    $data['diff'] = \DB::select(
      '

        select
          count(f.id) contract_count,
          round(f.diff / 10) as category,
          GROUP_CONCAT(if(f.id is null, 0, f.id)) as ids
        from (

        select
          c.id,
          if(actual.physical_progress is null , 0, actual.physical_progress) - plan.physical_progress as diff
        from contracts as c
        left join (
          select
            c.id,
            sum(cpp.physical_progress_percentage)  physical_progress
          from contract_progress_payments as cpp
          join contracts as c
            on c.id = cpp.contract_id
          where cpp.is_approved = true and c.is_published = true and
          (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  ( cpp.year < ' . $year . ')
          group by c.id
          ) as actual on actual.id = c.id


        left join (
        select
          c.id,
            sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) )  as physical_progress
          from contracts as c
          left join (
            select
              contract_id, sum(physical_progress_percentage) as physical_progress_percentage
            from contract_planning_finance_affairs_and_physical_progresses
            where  (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  ( year < ' . $year . ')
            group by contract_id
            ) as cp
            on	c.id = cp.contract_id
          left join  (
            select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage
            from (
                select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                    on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                  where crv.is_approved = true
                  group by crd.contract_id
                ) as cr

            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
              on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
            where  (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
            group by cr.contract_id
          ) as re_cp
            on c.id = re_cp.contract_id
          where c.is_published = true
          group by c.id
          ) as plan on plan.id = c.id
          where c.is_published = true   and plan.physical_progress is not null
        group  by c.id

      ) as f
      where round(f.diff / 10) > -11 and round(f.diff / 10 ) < 11
      group by round(f.diff / 10)
      order by round(f.diff / 10) Desc
      '
    );






    return $data;
  }




  public function completion(Request $request)
  {

    // count(c.id) as completed_contract_count,
    // GROUP_CONCAT(if(c.id is null, 0, c.id)) as ids,

    $data = DB::select('
            select
                GROUP_CONCAT(if(DATEDIFF(cs.date, cd.planned_end_date) > 0, c.id, null)) as late_ids,
                GROUP_CONCAT(if(DATEDIFF(cs.date, cd.planned_end_date) <= 0, c.id, null)) as on_time_ids,
                count(if(DATEDIFF(cs.date, cd.planned_end_date) > 0, 1, null)) as late,
                count(if(DATEDIFF(cs.date, cd.planned_end_date) <= 0, 1, null)) as on_time,
                year(cd.planned_end_date) as fiscal_year
            from  contracts as c
            left join contract_details as cd
                on c.id = cd.contract_id
            left join
                contract_progresses as cp
                on c.id = cp.contract_id

            join (
              select * from contract_status_logs where id in
                  (select max(id) as max_id
                  FROM contract_status_logs
                  where status_id = 3
                  group by contract_id)
                ) as cs
                on c.id = cs.contract_id
            where c.is_published = true and cs.status_id = 3
            group by year(cd.planned_end_date)
            order by year(cd.planned_end_date) Desc
                 ');

    foreach ($data as &$item) {
      $item->on_time = [
        'ids' => $item->on_time_ids,
        'contract_count' => $item->on_time
      ];
      $item->late = [
        'ids' => $item->late_ids,
        'contract_count' => $item->late
      ];
      unset($item->on_time_ids);
      unset($item->late_ids);
      $date = jDateTime::toJalali($item->fiscal_year, 1, 1);
      $item->fiscal_year =  $date[0] + 1;
    }
    return $data;
  }

  public function amendment(Request $request)
  {
    $join = '';
    $condition = '';

    //widgets
    $data['amendment_count'] = DB::selectOne('
           select
               count(c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
            where
               c.is_published = true
        ');
    $data['contract'] = DB::selectOne('
           select
               count(distinct c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
            where
               c.is_published = true
        ');
    $data['cost_amendment'] = DB::selectOne('
             select
               count(c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
               inner join cost_amendments AS cost_a
		               on a.id=cost_a.amendment_id
            where
               c.is_published = true
        ');
    $data['time_amendment'] = DB::selectOne('
            select
               count(c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
               inner join time_amendments AS time_a
		               on a.id=time_a.amendment_id
            where
               c.is_published = true
        ');
    $data['contract_terms_amendment'] = DB::selectOne('
            select
               count(c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
               inner join amendment_in_contract_terms AS a_c_t
		               on a.id=a_c_t.amendment_id
            where
               c.is_published = true
        ');

    $data['time_and_cost_amendment'] = DB::selectOne('
            select
               count(c.id) as count
            from contracts as c
               inner join amendments as a
                  on a.contract_id = c.id
               inner join time_and_cost_amendments AS time_a
		               on a.id=time_a.amendment_id
            where
               c.is_published = true
        ');

    //this part belongs to first chart
    $data['contract_count_without_amendment'] = DB::selectOne('
                    select
                       count(distinct c.id) as count,
                       GROUP_CONCAT(distinct c.id) as ids
                    from
                       contracts as c
                    where
                       c.is_published = true and c.id not in
                       (
                          select distinct a.contract_id
                          from amendments as a
                       )
                 ');
    $data['contract_count_with_one_amendment'] = DB::selectOne('
            select
                   count(distinct c.id) as count,
                   GROUP_CONCAT(distinct c.id) as ids
                from contracts as c
                where
                   c.is_published = true and c.id in
                   (
                      select a.contract_id
                      from amendments as a
                      group by a.contract_id
                      having count(a.contract_id) = 1
                   )
                 ');
    $data['contract_count_between_two_and_three_amendment'] = DB::selectOne('
           select
                   count(distinct c.id) as count,
                   GROUP_CONCAT(distinct c.id) as ids
                from contracts as c
                where
                   c.is_published = true and c.id in
                   (
                      select a.contract_id
                      from amendments as a
                      group by a.contract_id
                      having count(a.contract_id) between 2 and 3
                   )
                 ');
    $data['contract_count_between_four_and_five_amendment'] = DB::selectOne('
             select
                   count(distinct c.id) as count,
                   GROUP_CONCAT(distinct c.id) as ids
                from contracts as c
                where
                   c.is_published = true and c.id in
                   (
                      select a.contract_id
                      from amendments as a
                      group by a.contract_id
                      having count(a.contract_id) between 4 and 5
                   )
                 ');
    $data['contract_count_more_then_five_amendment'] = DB::selectOne('
              select
                   count(distinct c.id) as count,
                   GROUP_CONCAT(distinct c.id) as ids
                from contracts as c
                where
                   c.is_published = true and c.id in
                   (
                      select a.contract_id
                      from amendments as a
                      group by a.contract_id
                      having count(a.contract_id) > 5
                   )
                 ');

    return $data;
  }

  public function challenges()
  {
    $join = '';
    $condition = '';

    //widgets
    $data['challenges'] = DB::selectOne('
          select
               count(cr.id) as count
            from challenges_and_remarks as cr
               inner join contracts as c
                  on cr.contract_id = c.id
            where
               c.is_published = true
        ');

    $data['challenges_contrac'] = DB::selectOne('
             select
                    count(distinct c.id) as count
            from contracts as c
               inner join challenges_and_remarks as cr
                  on cr.contract_id = c.id
            where
               c.is_published = true
        ');

    $data['top_three_challenges'] = DB::select('
            select
               count(cr.id) as count,
               cr.category_id
            from challenges_and_remarks as cr
               inner join contracts as c
                  on cr.contract_id = c.id
            where c.is_published = true
            group by cr.category_id
            order by count(cr.id) desc
            limit 3
        ');

    //charts
    $data['chart'] = DB::select('
       select
           count(cr.id) as count,
           GROUP_CONCAT(distinct c.id) as ids,
           sum( (IFNULL(cd.actual_value, 0) + IFNULL(psc.provisional_sum_and_contingency, 0) + IFNULL(amendment.amount, 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) as value,
           cr.category_id
       from challenges_and_remarks as cr
           inner join contracts as c
              on cr.contract_id = c.id
           join contract_details as cd
              on c.id = cd.contract_id
           join contract_details_verifications cdv
              on cd.id = cdv.contract_detail_id
              and cdv.is_approved = true
           left join
              (
               select
                (sum(IFNULL(ca.amendment_amount, 0)) + sum(IFNULL(tca.amendment_amount, 0))) as amount,
                    a.contract_id as contract_id
                 from amendments as a
                    left join cost_amendments as ca
                       on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                       on tca.amendment_id = a.id
                 where a.is_approved = true
                 group by a.contract_id
              ) as amendment
              on c.id = amendment.contract_id
           left join provisional_sum_and_contingencies as psc
              on psc.contract_detail_id = cd.id
        where c.is_published = true
        group by cr.category_id
                 ');
    $data['chart_mid_point_by_value_and_count'] = DB::select('
            select
                FORMAT(floor(count(cr.id) / 2), 0) as mid_point_of_challenges_count,
                sum( (IFNULL(cd.actual_value, 0) + IFNULL(psc.provisional_sum_and_contingency, 0) + IFNULL(amendment.amount, 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 2 as mid_point_of_value
             from challenges_and_remarks as cr
                inner join contracts as c
                   on cr.contract_id = c.id
                join contract_details as cd
                   on c.id = cd.contract_id
                join contract_details_verifications cdv
                   on cd.id = cdv.contract_detail_id
                and cdv.is_approved = true
                left join
                   (
                      select (sum(IFNULL(ca.amendment_amount, 0)) + sum(IFNULL(tca.amendment_amount, 0))) as amount,
                         a.contract_id as contract_id
                      from amendments as a
                         left join cost_amendments as ca
                            on ca.amendment_id = a.id
                         left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                      where a.is_approved = true
                      group by a.contract_id
                   ) as amendment
                    on c.id = amendment.contract_id
                left join provisional_sum_and_contingencies as psc
                   on psc.contract_detail_id = cd.id
             where c.is_published = true
                 ');

    // $data['impacts'] = DB::select('
    // select
    //        category_impact.category_id,
    //        ROUND(avg( ( if(category_impact.high_total is not null, category_impact.high_total / (category_impact.high_total / 3), 0)
    //                         + if(category_impact.meduim_total is not null, category_impact.meduim_total / (category_impact.meduim_total / 2), 0)
    //                         + if(category_impact.low_total is not null, category_impact.low_total / (category_impact.low_total / 1), 0) ) / 3 ), 1
    //            ) as total_impact
    //     from
    //        (
    //           select
    //              impact.category_id,
    //              sum(impact.cost_nil + quality_nil + time_nil) / 3 as nil_total,
    //              sum(impact.cost_low + quality_low + time_low) / 3 as low_total,
    //              sum(impact.cost_meduim + quality_meduim + time_meduim) / 3 as meduim_total,
    //              sum(impact.cost_high + quality_high + time_high) / 3 as high_total
    //           from
    //              (
    //                 select
    //                    cr.category_id,
    //                    if(c.id is null, 0, c.id) as ids,
    //                    if (cr.impact_level_cost_id = 4, 0, null) as cost_nil,
    //                    if (cr.impact_level_quality_id = 4, 0, null) as quality_nil,
    //                    if (cr.impact_level_time_id = 4, 0, null) as time_nil,
    //                    if (cr.impact_level_cost_id = 3, 1, null) as cost_low,
    //                    if (cr.impact_level_quality_id = 3, 1, null) as quality_low,
    //                    if (cr.impact_level_time_id = 3, 1, null) as time_low,
    //                    if (cr.impact_level_cost_id = 2, 2, null) as cost_meduim,
    //                    if (cr.impact_level_quality_id = 2, 2, null) as quality_meduim,
    //                    if (cr.impact_level_time_id = 2, 2, null) as time_meduim,
    //                    if (cr.impact_level_cost_id = 1, 3, null) as cost_high,
    //                    if (cr.impact_level_quality_id = 1, 3, null) as quality_high,
    //                    if (cr.impact_level_time_id = 1, 3, null) as time_high
    //                 from
    //                    challenges_and_remarks as cr
    //                    inner join
    //                       contracts as c
    //                       on cr.contract_id = c.id
    //                 where
    //                    c.is_published = true
    //                    and cr.impact_level_cost_id is not null
    //                    or cr.impact_level_quality_id is not null
    //                    or cr.impact_level_time_id
    //                 group by
    //                    c.id, cr.category_id
    //              )
    //              as impact
    //           group by impact.category_id
    //        )
    //        as category_impact
    //     group by  category_impact.category_id
    // ');
    return $data;
  }


  public function payments(Request $request)
  {
    $statusId = $request->query('status_id');

    $now = jDateTime::toJalali(Carbon::now()->year, Carbon::now()->month, Carbon::now()->day);
    $monthId = $now[1];
    $year = $now[0];
    if ($monthId > 9) {
      $year = $year + 1;
    }

    $join = '';
    $condition = '';
    if ($statusId) {
      $join .= '
          join contract_status_logs
          as csl
            on c.id = csl.contract_id
      ';
      $condition .= '
      and csl.status_id = ' . $statusId;
    }
    $data['planned'] = \DB::selectOne('
    select
      sum(pr.amount * if(c.exchange_rate, c.exchange_rate, 1) )  as work_value
    from contracts as c
    left join
      (
        select
          c.id,
            sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) / 100 )  as physical_progress,
            sum(if(re_cp.amount is not null, re_cp.amount, cp.amount ))  as amount
          from contracts as c
          left join (
            select
              contract_id, sum(physical_progress_percentage) as physical_progress_percentage, sum(amount) as amount
            from contract_planning_finance_affairs_and_physical_progresses
            where (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  (year < ' . $year . ')
            group by contract_id
            ) as cp
            on	c.id = cp.contract_id
          left join  (
            select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage, sum(crp.amount) as amount
            from (
                select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                    on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                  where crv.is_approved = true
                  group by crd.contract_id
                ) as cr

            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
              on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
            where (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
            group by cr.contract_id
          ) as re_cp
            on c.id = re_cp.contract_id
          where c.is_published = true
          group by c.id
      ) as pr
      on pr.id = c.id
    where c.is_published = true
        ');

    $data['actual'] = \DB::selectOne(
      '
        select
          sum( pr.amount * if(c.exchange_rate, c.exchange_rate, 1))  as work_value
        from contracts as c
        left join
          (
            select
              c.id,
              sum(cpp.physical_progress_percentage / 100)  physical_progress,
              sum(cpp.amount) as  amount
            from contract_progress_payments as cpp
            join contracts as c
              on c.id = cpp.contract_id
            where cpp.is_approved = true and c.is_published = true and
            (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  (cpp.year < ' . $year . ')
            group by c.id
          ) as pr
          on pr.id = c.id

        where c.is_published = true
      '
    );
    $data['spi_count'] = \DB::selectOne(
      '
          select
            count(if( plan.amount != 0 , if((actual.amount >= plan.amount)  , c.id, null ) , null) ) as on_time,
            count(if( plan.amount != 0 , if((actual.amount < plan.amount)  , c.id, null ) , c.id) ) as late

          from contracts as c
          left join (
          select
            c.id,
            sum(cpp.physical_progress_percentage / 100)  physical_progress,
            sum(cpp.amount)  amount
          from contract_progress_payments as cpp
          join contracts as c
            on c.id = cpp.contract_id
          where cpp.is_approved = true and c.is_published = true and
          (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  (cpp.year < ' . $year . ')
          group by c.id
          ) as actual on actual.id = c.id


          left join (
          select
          c.id,
            sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) / 100 )  as physical_progress,
            sum(if(re_cp.amount is not null, re_cp.amount, cp.amount ))  as amount
          from contracts as c
          left join (
            select
              contract_id, sum(physical_progress_percentage) as physical_progress_percentage, sum(amount) as amount
            from contract_planning_finance_affairs_and_physical_progresses
            where (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  (year < ' . $year . ')
            group by contract_id
            ) as cp
            on	c.id = cp.contract_id
          left join  (
            select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage, sum(crp.amount) as amount
            from (
                select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                    on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                  where crv.is_approved = true
                  group by crd.contract_id
                ) as cr

            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
              on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
            where (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
            group by cr.contract_id
          ) as re_cp
            on c.id = re_cp.contract_id
          where c.is_published = true
          group by c.id
          ) as plan on plan.id = c.id
            where c.is_published = true
      '
    );

    $data['diff'] = \DB::select(
      '
      select
      count(f.id) contract_count,
      round(f.diff / 10) as category,
      GROUP_CONCAT(if(f.id is null, 0, f.id)) as ids
      from (
          select
            c.id,
            (if(actual.amount is null , 0, actual.amount) - plan.amount) /
            ((ifnull(cd.actual_value, 0) +
                ifnull(psc.provisional_sum_and_contingency, 0) +
                ifnull(amendment.amount, 0)) *
                if(c.exchange_rate, c.exchange_rate, 1)) * 100 as diff
          from contracts as c
          left join (
            select
              c.id,
              sum(cpp.physical_progress_percentage)  physical_progress,
              sum(cpp.amount) * if(c.exchange_rate, c.exchange_rate, 1) as amount
            from contract_progress_payments as cpp
            join contracts as c
              on c.id = cpp.contract_id
            where cpp.is_approved = true and c.is_published = true and
            (cpp.year = ' . $year . ' and cpp.month_id < ' . $monthId . ' ) or  ( cpp.year < ' . $year . ')
            group by c.id
            ) as actual on actual.id = c.id


          left join (
          select
            c.id,
              sum(if(re_cp.physical_progress_percentage is not null, re_cp.physical_progress_percentage, cp.physical_progress_percentage ) )  as physical_progress,
              sum(if(re_cp.amount is not null, re_cp.amount, cp.amount ) ) * if(c.exchange_rate, c.exchange_rate, 1) as amount
            from contracts as c
            left join (
              select
                contract_id, sum(physical_progress_percentage) as physical_progress_percentage, sum(amount) as amount
              from contract_planning_finance_affairs_and_physical_progresses
              where (year = ' . $year . ' and month_id < ' . $monthId . ' ) or  ( year < ' . $year . ')
              group by contract_id
              ) as cp
              on	c.id = cp.contract_id
            left join  (
              select cr.contract_id, sum(crp.physical_progress_percentage) as physical_progress_percentage, sum(crp.amount) as amount
              from (
                  select max(crd.id) as id, crd.contract_id from contract_re_planning_date_f_a_a_p_ps  as crd
                  left join contract_re_planning_date_f_a_a_p_p_verifications as crv
                      on crd.id = crv.contract_re_planning_date_f_a_a_p_p_id
                    where crv.is_approved = true
                    group by crd.contract_id
                  ) as cr

              left join contract_re_planning_finance_affairs_and_physical_progresses as crp
                on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
              where  (crp.year = ' . $year . ' and crp.month_id < ' . $monthId . ' ) or  ( crp.year < ' . $year . ')
              group by cr.contract_id
            ) as re_cp
              on c.id = re_cp.contract_id
            where c.is_published = true
            group by c.id
            ) as plan on plan.id = c.id


          join contract_details as cd
            on c.id = cd.contract_id
          join contract_details_verifications cdv
            on cd.id = cdv.contract_detail_id and cdv.is_approved = true
          left join (
              select
                  (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                  a.contract_id as contract_id
              from amendments as a
              left join cost_amendments as ca
                  on ca.amendment_id = a.id
              left join time_and_cost_amendments as tca
                  on tca.amendment_id = a.id
              where a.is_approved = true
              group by  a.contract_id

          ) as amendment
                on c.id = amendment.contract_id
          left join provisional_sum_and_contingencies as psc
                  on psc.contract_detail_id = cd.id
            where c.is_published = true   and plan.physical_progress is not null
          group  by c.id
      ) as f
      where round(f.diff / 10) > -11 and round(f.diff / 10 ) < 11
      group by round(f.diff / 10)
      order by round(f.diff / 10) Desc


      '
    );






    return $data;
  }
}
