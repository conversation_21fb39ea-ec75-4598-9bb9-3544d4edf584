<?php

namespace NPA\ACPMS\Helpers;

const TIMEOUT = 120;
//(int) config('custom.REQUEST_TIMEOUT')


use Requests;

class Http
{

    static function getHeaders()
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Origin' => config('custom.CUSTOM_FRONT_END_BASE_URL')
        ];
    }

    public static function get($url, $headers = array())
    {
        return Requests::get($url, array_merge($headers, self::getHeaders()), array('timeout' => (int)config('custom.REQUEST_TIMEOUT')));
    }

    public static function delete($url, $headers = array())
    {
        return Requests::delete($url, array_merge($headers, self::getHeaders()), array('timeout' => (int)config('custom.REQUEST_TIMEOUT')));
    }

    public static function post($url, $headers = array(), $data = array())
    {
        return Requests::post($url, array_merge($headers, self::getHeaders()), json_encode($data), array('timeout' => (int)config('custom.REQUEST_TIMEOUT')));
    }

    public static function put($url, $headers = array(), $data = array())
    {
        return Requests::put($url, array_merge($headers, self::getHeaders()), json_encode($data), array('timeout' => (int)config('custom.REQUEST_TIMEOUT')));
    }
}

