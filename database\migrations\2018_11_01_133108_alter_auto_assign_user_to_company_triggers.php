<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AlterAutoAssignUserToCompanyTriggers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

      DB::connection()->getPdo()->exec('drop trigger operation_companies_user_after_delete_auto_assignment');
      DB::connection()->getPdo()->exec("
            create trigger operation_companies_user_after_delete_auto_assignment
            after delete
            on companies
            for each row
            exit_label:begin
                declare user_id_var int default null;
                declare contract_id_var int default null;
                declare id_for_previously_assgined_company_record int default null;
                declare auto_generated_user_assigned_contracts_count int default null;
                
                -- Get contract_id
                select c.id
                into contract_id_var
                from contracts as c
                join company_general_informations as cgi 
                    on (
                        cgi.contract_id = c.id and
                        cgi.id = old.company_general_information_id
                    );
                
                -- Get user_id
                select 
                    u.id 
                into 
                    user_id_var
                from users as u
                join 
                    user_companies as uc on uc.user_id = u.id
                where uc.company_id = old.company_id
                limit 1;
                -- Delete the records
                
                delete from user_companies
                where 
                  user_id = user_id_var and 
                    company_id = old.company_id and 
                    contract_id = contract_id_var;
               
               -- Delete dangling users
                select count(uc.id)
                into auto_generated_user_assigned_contracts_count
                from user_companies as uc
                where  uc.user_id = user_id_var;
                if auto_generated_user_assigned_contracts_count = 0 then 
                     delete from users where id = user_id_var;
                end if;
          
            end        
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      DB::connection()->getPdo()->exec('drop trigger operation_companies_user_after_delete_auto_assignment');
    }
}
