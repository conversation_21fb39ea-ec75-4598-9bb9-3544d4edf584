<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\SubcontractVerification;
use Illuminate\Http\Request;

class SubcontractVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            $data['contract_id'] = $data['parent_id'];
            unset($data['parent_id']);
            $verificationData = SubcontractVerification::create(array_only($data, SubcontractVerification::$fields));
            return response()->json($verificationData, 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\SubcontractVerification $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {


        $data = SubcontractVerification::where('contract_id', $contractId)->first();
        return response()->json($data, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\SubcontractVerification $subcontractVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(SubcontractVerification $subcontractVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\SubcontractVerification $subcontractVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractId)
    {
        try {
            $data = $request->all();
            $data['contract_id'] = $data['parent_id'];
            unset($data['parent_id']);
            $subcontractVerification = SubcontractVerification::where('contract_id', $contractId)->first();
            $isUpdated = $subcontractVerification->update(array_only($data, SubcontractVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\SubcontractVerification $subcontractVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(SubcontractVerification $subcontractVerification)
    {
        //
    }
}
