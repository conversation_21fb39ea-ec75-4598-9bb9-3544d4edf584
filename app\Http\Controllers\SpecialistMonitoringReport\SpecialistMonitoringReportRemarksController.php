<?php

namespace NPA\ACPMS\Http\Controllers\SpecialistMonitoringReport;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\PARS\ParsAdvancePaymentGuarantie;
use NPA\ACPMS\Models\PARS\ParsRemark;
use NPA\ACPMS\Models\PARS\ProgressAnalysisReport;

class SpecialistMonitoringReportRemarksController extends Controller
{
    public function store(Request $request)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            $lastNewSuggesstion = DB::table('progress_analysis_reports')
                ->join('pars_remarks', 'progress_analysis_reports.id', '=', 'pars_remarks.p_a_r_id')
                ->where('progress_analysis_reports.contract_id', $data['contract_id'])
                ->select('pars_remarks.new_suggestion', 'pars_remarks.new_suggestions_vendor')
                ->orderBy('progress_analysis_reports.id', 'DESC')
                ->first();
            if (isset($lastNewSuggesstion->new_suggestion)) {
                $data['old_suggestion'] = $lastNewSuggesstion->new_suggestion;
            }
            if (isset($lastNewSuggesstion->new_suggestions_vendor)) {
                $data['old_suggestions_vendor'] = $lastNewSuggesstion->new_suggestions_vendor;
            }
            ParsAdvancePaymentGuarantie::where('p_a_r_id', $data['p_a_r_id'])->update([
                'expiration_date' => $data['advance_payment_guaranty_expiration_date']
            ]);
            $createdId = ParsRemark::create(array_only($data, ParsRemark::$fields))->id;
            DB::commit();
            return response()->json([], 201, [
                'location' => $createdId
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function update(Request $request, $id)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            $lastNewSuggesstion = DB::table('progress_analysis_reports')
                ->join('pars_remarks', 'progress_analysis_reports.id', '=', 'pars_remarks.p_a_r_id')
                ->where('progress_analysis_reports.contract_id', $data['contract_id'])
                ->where('pars_remarks.id', '<', $data['id'])
                ->select('pars_remarks.new_suggestion', 'pars_remarks.new_suggestions_vendor')
                ->orderBy('progress_analysis_reports.id', 'DESC')
                ->first();
            if (isset($lastNewSuggesstion->new_suggestion)) {
                $data['old_suggestion'] = $lastNewSuggesstion->new_suggestion;
            }
            if (isset($lastNewSuggesstion->new_suggestions_vendor)) {
                $data['old_suggestions_vendor'] = $lastNewSuggesstion->new_suggestions_vendor;
            }
            ParsAdvancePaymentGuarantie::where('p_a_r_id', $data['p_a_r_id'])->update([
                'expiration_date' => $data['advance_payment_guaranty_expiration_date']
            ]);

            $progressAnalysisReport = ProgressAnalysisReport::where('id', $data['p_a_r_id'])
                ->where('serial_number', $data['serial_number'])
                ->where('contract_id', $data['contract_id'])
                ->where('year', $data['year'])
                ->where('month', $data['month'])->first();
            $progressAnalysisReport->update(['is_auto_saved' => $data['is_auto_saved']]);
            $parsRemark = ParsRemark::where('id', $id)->first();
            $parsRemark->update(array_only($data, ParsRemark::$fields));
            DB::commit();
            return response()->json([], 204, [
                'location' => $parsRemark['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    public function uploadReport(Request $request)
    {
        $data = $request->all();
        \Log::info($data['specialist_report_pdf_version']);
        $progressAnalysisReportId = $request->query('progress_analysis_report_id');
        \Log::info($progressAnalysisReportId);
        $enteredParentRecord = ProgressAnalysisReport::find($progressAnalysisReportId);
        \Log::info($enteredParentRecord);
        AttachmentController::saveFile($request, $enteredParentRecord, 'specialist_report_pdf_version', $data['specialist_report_pdf_version']);
        return response()->json([], 201);
    }
}
