<?php

namespace NPA\ACPMS\Helpers;


class ArrayAndObject
{
    /**
     * @param $resultArray
     * @param array $exceptionsArray
     * @return array
     */
    public static function flattenResultArray($resultArray, $exceptionsArray = [])
    {
        $returnArray = [];
        foreach ($resultArray as $kLevel1 => $vLevel1) {
            array_forget($vLevel1, $exceptionsArray);
            $resultArray[$kLevel1] = $vLevel1;
            $returnArray[] = self::flatten($resultArray, $kLevel1);
        }
        return $returnArray;
    }

    /**
     * @param $inputArray
     * @param $index
     * @return array
     */
    private static function flatten($inputArray, $index)
    {
        $returnArray = [];
        if (is_array($inputArray[$index])) {
            foreach ($inputArray[$index] as $k => $v) {
                $returnArray = array_merge($returnArray, self::flatten($inputArray[$index], $k));
            }
        } else {
            $returnArray[$index] = $inputArray[$index];
        }
        return $returnArray;
    }

    /**
     * @param $resultArray
     * @param $candidateSlugs
     * @return mixed
     */
    public static function stubSlugs($resultArray, $candidateSlugs)
    {
        $returnArray = [];
        foreach ($resultArray as $kLevel1 => $vLevel1) {
            foreach ($vLevel1 as $kLevel2 => $vLevel2) {
                $returnArray[$kLevel1][$kLevel2] = $vLevel2;
                foreach ($candidateSlugs as $vLevel3) {
                    if ($kLevel2 === $vLevel3 . '_name_en') {
                        $returnArray[$kLevel1][$vLevel3 . '_slug'] = Query::buildSlug($returnArray[$kLevel1][$kLevel2]);
                    }
                }
            }
        }
        return $returnArray;
    }
}
