<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixingTriggersValidationContractsContractPlanningVerificationIsConfirmed extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec('drop trigger if exists `validation_contracts_contract_planning_verification_is_confirmed`');
        DB::connection()->getPdo()->exec("
            create trigger `validation_contracts_contract_planning_verification_is_confirmed` 
            before update on `contracts`
            for each row 
            exit_label:begin
            
                declare is_confirmed boolean default null;
                declare total boolean default null;
            
                if old.is_confirmed = new.is_confirmed or new.is_confirmed = false then 
                    leave exit_label;
                end if;
            
                select count(*)
                into is_confirmed 
                from contract_approval_verifications as cav
                join contract_approvals as ca
                    on ca.id = cav.contract_approval_id
                join contracts as c
                on c.id = ca.contract_id
                where c.id = new.id and cav.is_confirmed=1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0001|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed 
                from p_e_contact_person_verifications as pecpv
                where pecpv.contract_id = new.id and pecpv.is_confirmed=1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text ='|||NPA-ACPMS-PLANNING-0002|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed 
                from contracts as c
                left join domestic_contract_execution_verifications as dcev 
                    on c.id = dcev.contract_id
                left join foreign_contract_execution_locations as fcel
                    on fcel.contract_id = c.id
                left join foreign_contract_execution_verifications as fcelv
                    on fcel.id = fcelv.foreign_exec_id
                where c.id = new.id and (dcev.is_confirmed = true or fcelv.is_confirmed = true);
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0003|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed 
                from contract_details_verifications as cdv
                join contract_details as cd
                    on cd.id = cdv.contract_detail_id
                join contracts as c
                on c.id = cd.contract_id
                where c.id = new.id and cdv.is_confirmed=1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0004|||';
                end if;
                
                set total = null;    
                set is_confirmed = null;
                
                select 
                count(case when com.is_confirmed = true then 1 end) into is_confirmed
                from companies as com 
                join company_general_informations as cgi
                    on cgi.id = com.company_general_information_id
                where cgi.contract_id = new.id;
                
                select 
                count(com.id) into total
                from companies as com 
                join company_general_informations as cgi
                    on cgi.id = com.company_general_information_id
                where cgi.contract_id = new.id;
            
                if is_confirmed != total or total = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0009|||';
                end if;
                
                set total = null;    
                set is_confirmed = null;
                
                select 
                count(case when cbi.is_confirmed = true then 1 end) into is_confirmed
                from company_bank_informations as cbi
                join companies as com 
                    on cbi.company_id = com.id
                join company_general_informations as cgi
                    on cgi.id = com.company_general_information_id
                where cgi.contract_id = new.id;
                
                select 
                count(cbi.id) into total
                from company_bank_informations as cbi
                join companies as com 
                    on cbi.company_id = com.id
                join company_general_informations as cgi
                    on cgi.id = com.company_general_information_id
                where cgi.contract_id = new.id;
                
                
                if is_confirmed != total or total = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0010|||';
                end if;
                
                set total = null;    
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed
                from company_general_informations as cgi
                where cgi.contract_id = new.id and cgi.is_confirmed = 1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0005|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed
                from subcontract_verifications as sv
                where sv.contract_id = new.id and sv.is_confirmed = 1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0006|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed
                from contract_planning_f_a_a_p_p_verifications as cpfaappv
                where cpfaappv.contract_id = new.id and cpfaappv.is_confirmed = 1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0007|||';
                end if;
            
                set is_confirmed = null;
            
                select count(*)
                into is_confirmed
                from liquidated_damages_delivery_verifications as lddv
                where lddv.contract_id = new.id and lddv.is_confirmed = 1;
            
                if is_confirmed = 0 then
                    signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-PLANNING-0008|||';
                end if;
            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
