<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Company;
use NPA\ACPMS\Models\CompanyBankInformation;
use NPA\ACPMS\Models\CompanyGeneralInformation;

class CompanyBankInformationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        try {
            $companyGeneralInformation = CompanyGeneralInformation::where('id', $request->query('id'))->first();
            $companies = $companyGeneralInformation->companies;
            $companyBankInfo = [];
            $count = 0;
            foreach ($companies as $company) {
                $status = false;
                foreach ($company->bank_information as $info) {
                    $status = true;
                    $dropDowns = CompanyController::dropDown($company['company_general_information_id'])->original->toArray();
                    foreach ($dropDowns as $dropDown) {
                        if ($dropDown['id'] === $company['id']) {

                            $info['company'] = $dropDown;
                        }
                    }
                    array_push($companyBankInfo, $info);
                }
                if ($status) {
                    $count += 1;
                }
            }
            $InvolvedCompaniesCount = sizeof($companies);
            $savedBankInformationCompaniesCount = $count;

            return response()->json([
                'savedBankInformationCompaniesCount' => $savedBankInformationCompaniesCount,
                'involvedCompaniesCount' => $InvolvedCompaniesCount,
                'companyBankInfo' => $companyBankInfo
            ], 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return void
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['email'] = $data['bank_email'];
        $data['bank_id'] = $data['bank']['id'];
        unset($data['bank']);
        $data['company_id'] = $data['company']['id'];
        unset($data['company']);
        try {

            $companyBankInformation = CompanyBankInformation::create(array_only($data, CompanyBankInformation::$fields));
            return response()->json([], 201, [
                'location' => $companyBankInformation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param $id
     * @return void
     */
    public function show($id)
    {
        try {
            $data = CompanyBankInformation::where('id', $id)->first();
            $data['license_number'] = Company::select('license_number')->where('id', $data['company_id'])->first();
            $queryString = '?search[license_number][match]=in&' . 'search[license_number][value][]=' . $data['license_number']['license_number'];
            $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
            $remoteRequestData = json_decode($remoteRequest->body, true);
            $data['company'] = count($remoteRequestData) > 0 ? $remoteRequestData[0] : $remoteRequestData;
            return response()->json($data);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param CompanyBankInformation $companyBankInformation
     * @return void
     */
    public function edit(CompanyBankInformation $companyBankInformation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param CompanyBankInformation $companyBankInformation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CompanyBankInformation $companyBankInformation)
    {
        $data = $request->all();
        try {
            if (!array_key_exists('parent_id', $data)) {
                $data['email'] = $data['bank_email'];
                $data['bank_id'] = $data['bank']['id'];
                unset($data['bank']);
                $data['company_id'] = $data['company']['id'];
                unset($data['company']);
                $companyBankInformation->update(array_only($data, CompanyBankInformation::$fields));
            } else {
                unset($data['parent_id']);
                unset($data['email']);
                $companyBankInformation->update(array_only($data, CompanyBankInformation::$fields));
            }
            return response()->json([], 204, [
                'location' => $companyBankInformation['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            CompanyBankInformation::where('id', $id)->delete();
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
