<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class Amendment extends Model
{
    protected $guarded = [
        'id'
    ];

    public static $fields = [
        'contract_id',
        'is_pe_capacity_certified',
        'type_id',
        'amendment_start_date',
        'peshnehad_decision_number',
        'is_approved',
        'is_confirmed'
    ];


    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function time_amendment()
    {
        return $this->hasOne('NPA\ACPMS\Models\TimeAmendment');
    }

    public function time_and_cost_amendment()
    {
        return $this->hasOne('NPA\ACPMS\Models\TimeAndCostAmendment');
    }

    public function cost_amendment()
    {
        return $this->hasOne('NPA\ACPMS\Models\CostAmendment');
    }

    public function amendment_in_contract_terms()
    {
        return $this->hasOne('NPA\ACPMS\Models\AmendmentInContractTerms');
    }

}
