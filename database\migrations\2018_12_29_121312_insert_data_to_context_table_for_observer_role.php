<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;

class InsertDataToContextTableForObserverRole extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            $role_id = $this->get_role();
            $data = array(
                array('name' => 'exception', 'role_id' => $role_id),
                array('name' => 'info-edit', 'role_id' => $role_id),
                array('name' => 'info-add', 'role_id' => $role_id),
                array('name' => 'edit', 'role_id' => $role_id),
                array('name' => 'add', 'role_id' => $role_id),
                array('name' => 'reports', 'role_id' => $role_id),
                array('name' => 'procurement-entity-profile', 'role_id' => $role_id),
            );
//            DB::table('contexts')->insert($data);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        try {
            $role_id = $this->get_role();
            DB::table('contexts')->where('role_id', '=', $role_id)->delete();
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    public function get_role()
    {
        $role = DB::table('roles')->select('id')->where('name', '=', 'observer')->first();
        return $role->id;
    }
}
