<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    protected $guarded = ['id'];


    public static $fields = [
        'address',
        'remarks',
        'license_number',
        'licence_end_date',
        'joint_venture_company_role_id',
        'company_general_information_id',
        'percentage_contract_share',
        'is_confirmed',
        'is_approved'
    ];

    public function bank_information()
    {
        return $this->hasMany('NPA\ACPMS\Models\CompanyBankInformation');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function company_general_information()
    {
        return $this->belongsTo('NPA\ACPMS\Models\CompanyGeneralInformation');
    }
}
