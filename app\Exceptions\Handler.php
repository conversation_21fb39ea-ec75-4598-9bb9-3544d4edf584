<?php

namespace NPA\ACPMS\Exceptions;

use Exception;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Exception $exception
     * @return void
     * @throws Exception
     */
    public function report(Exception $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Exception $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Exception $exception)
    {
        $appEnv = config('app.env');
        Log::error('>>>>>>>>> API Specific: ' . get_class($exception) . '\n' . $exception->getMessage());
        if ($exception instanceof NotFoundHttpException || $exception instanceof MethodNotAllowedHttpException) {
            return response()->json([
                'message' => 'Not found',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $exception->getMessage(),
                    'stackTrace' => $exception->getTrace()
                ]
            ], 404);
        } else {
            return response()->json([
                'message' => 'Internal server error',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $exception->getMessage(),
                    'stackTrace' => $exception->getTrace()
                ]
            ], 500);
        }
//        return parent::render($request, $exception);
    }
}
