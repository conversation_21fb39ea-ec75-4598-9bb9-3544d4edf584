<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameCulomnCurrentThresholdValueInCostAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->renameColumn('current_threshold_value', 'current_cost_threshold_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->renameColumn('current_cost_threshold_value', 'current_threshold_value');
        });
    }
}
