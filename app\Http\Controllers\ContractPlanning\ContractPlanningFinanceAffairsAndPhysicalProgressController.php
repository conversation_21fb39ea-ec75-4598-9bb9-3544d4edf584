<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress;
use Illuminate\Http\Request;

class ContractPlanningFinanceAffairsAndPhysicalProgressController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $financeAffairsPhysicalProgress = ContractPlanningFinanceAffairsAndPhysicalProgress
            ::where('contract_id', $contractId)
            ->orderBy('year', 'month_id')
            ->get();
        $plannedTotalAmount = ContractPlanningFinanceAffairsAndPhysicalProgress
            ::where('contract_id', $contractId)->sum('amount');

        $contractDetails = ContractDetails
            ::select('planned_start_date', 'planned_end_date', 'actual_value')
            ->where('contract_id', $contractId)
            ->first();

        $plannedPaymentsSchedule = Contract::select('id')->where('id', $contractId)->first();
        $attachments = $plannedPaymentsSchedule->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
        foreach ($attachments as $key => $value) {
            $index = $attachments[$key]['field_name'];
            $att = [];
            foreach ($attachments as $k => $v) {
                if ($v['field_name'] == $index && $v['table_name'] == 'contracts') {
                    array_push($att, $v);
                }
            }
            $plannedPaymentsSchedule[$attachments[$key]['field_name']] = $att;
        }
        return response()->json([
            'finance_affairs_physical_progress' => $financeAffairsPhysicalProgress,
            'contract_details' => $contractDetails,
            'planned_total_amount' => $plannedTotalAmount,
            'planned_payments_schedule' => $plannedPaymentsSchedule]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $data = $request->all();
        $data['status_id'] = $data['status']['id'];
        unset($data['status']);
        unset($data['month']);
        try {
            $createdId = ContractPlanningFinanceAffairsAndPhysicalProgress
                ::create(array_only($data, ContractPlanningFinanceAffairsAndPhysicalProgress::$fields))->id;

            return response()->json([], 201, [
                'location' => $createdId,
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress
     * @return \Illuminate\Http\Response
     */
    public function show(ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractPlanningFinanceAffairsAndPhysicalProgressId)
    {
        try {
            $data = $request->all();
            unset($data['month']);
            $data['status_id'] = $data['status']['id'];
            unset($data['status']);
            $contractPlanningFinanceAffairsAndPhysicalProgress = ContractPlanningFinanceAffairsAndPhysicalProgress
                ::where('id', $contractPlanningFinanceAffairsAndPhysicalProgressId)
                ->update(array_only($data, ContractPlanningFinanceAffairsAndPhysicalProgress::$fields));
            if ($contractPlanningFinanceAffairsAndPhysicalProgress < 0) {
                return Error::exceptionNotFound();
            }
            return response()->json([], 204);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractPlanningFinanceAffairsAndPhysicalProgress $contractPlanningFinanceAffairsAndPhysicalProgress)
    {
        //
    }
}
