<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\Calculate;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\API\Internal\Daemons\CommonValue;
use NPA\ACPMS\Models\Company;
use NPA\ACPMS\Models\CompanyBankInformation;
use NPA\ACPMS\Models\CompanyGeneralInformation;
use NPA\ACPMS\Models\ConsultancyContractsSelectionMethod;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractApproval;
use NPA\ACPMS\Models\ContractApprovalVerification;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractDetailsVerification;
use NPA\ACPMS\Models\ContractGeneralInformationVerification;
use NPA\ACPMS\Models\ContractPlanningAdvance;
use NPA\ACPMS\Models\ContractPlanningFAAPPVerification;
use NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractProgressAdvance;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractProgressPaymentAfterDueDate;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPP;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\ContractStatusLog;
use NPA\ACPMS\Models\ContractStatusVerification;
use NPA\ACPMS\Models\ContractTags;
use NPA\ACPMS\Models\DomesticContractExecutionVerification;
use NPA\ACPMS\Models\Exception as ContractException;
use NPA\ACPMS\Models\ForeignContractExecutionLocation;
use NPA\ACPMS\Models\ForeignContractExecutionVerification;
use NPA\ACPMS\Models\LiquidatedDamagesAfterDeliveryService;
use NPA\ACPMS\Models\LiquidatedDamagesDeliveryVerification;
use NPA\ACPMS\Models\PEContactPersonVerification;
use NPA\ACPMS\Models\Subcontract;
use NPA\ACPMS\Models\SubcontractVerification;
use Throwable;

class ContractController extends Controller
{

    public function index(Request $request)
    {
        try {
            $result = $this->loadList($request, false);
            return response()->json($result['data'], 200, [
                'x-pagination-size' => $result['count']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function downloadXlsx(Request $request)
    {
        $data = $request->all();
        try {
            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_ARG_BASE_URL') . 'api/acpms/contract-list-report'
                , []
                , array_values($data));

            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $request->body);
            }
            return response($request->body, 200);


//
//            $data = $this->loadList($request, true);
//            $data['totalColumnsCount'] = 20;
//            $data['searchParameters'] = $request->all();
//            $request = Requests::post(
//                config('custom.CUSTOM_API_INTERNAL_RM_BASE_URL') . 'api/report',
//                ['Content-Type' => 'application/json'],
//                json_encode([
//                    'template' => [
//                        'shortid' => config('custom.CUSTOM_RM_SHORT_ID_XLSX')
//                    ],
//                    'data' => [
//                        'htmlContents' => View::make('reports/contract-list',
//                            $data
//                        )->render()
//                    ]
//                ])
//            );
//
//            if ($request->status_code > 300) {
//
//                //TODO: Handle
//                throw new \Exception($request->body);
//            }
//            return response($request->body, 200, [
//                'Content-Type' => $request->headers['content-type']
//            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    private function loadList(Request $request, $isDownloading = false)
    {

        $sectors = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/sector');
        $procurementEntities = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntityAll/procurementEntityAll');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $procurementMethods = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementMethod');
        $currencies = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency');
        $selectionMethods = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/selectionMethod');
        $contractTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractType');
        $contractTags = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/projectTag');
        $queryParameters = $request->query();
        $count = Contract::whereRaw('1=1');

        $this->applyUserManagement($request, $count);
        $this->applySearch($count, $queryParameters);
        $count = $count->count();

        $contracts = Contract::whereRaw('1=1');
        $this->applyUserManagement($request, $contracts);

        $contracts = $contracts->orderBy('contracts.id', 'desc');

        if (!$isDownloading) {
            $contracts
                ->offset($queryParameters['page_size'] * $queryParameters['page_index'])
                ->take($queryParameters['page_size']);
        }
        $this->applySearch($contracts, $queryParameters);

        $contracts = $contracts->with(['donors' => function ($query) {
            $query->select(
                'contract_id',
                'donor_id'
            );
        }, 'company_general_information' => function ($query) {
            $query->with('companies');
        }, 'contract_approval' => function ($query) {
            $query->select(
                'award_date as approval_date',
                'contract_id'
            );
        }, 'contract_specialists' => function ($query) {
            $query->select(
                'id',
                'specialist_id',
                'contract_id'
            );
        }, 'contract_tags' => function ($query) {
            $query->select(
                'id',
                'tag_id',
                'contract_id'
            );
        }])->get();

        if (count($contracts) == 0) {
            return [
                'data' => [],
                'count' => 0
            ];
        }


        $projectIds = [];
        $companyLicenses = [];
        foreach ($contracts as $contract) {
            $contract = $contract->toArray();


            $projectIds[] = $contract['project_id'];
            if ($this->contractHasCompany($contract)) {
                $companyLicenses[] = $contract['company_general_information']['companies'][0]['license_number'];
            }

        }
        $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info',
            [],
            [
                'ids' => $projectIds
            ]
        );

        $projects = json_decode($request->body, true);


        if ($request->status_code >= 300) {
            throw new Exception('Problem getting data from APPMS');
        }

        $referenceCompanies = [];
        if (count($companyLicenses) !== 0) {
            $queryString = '?search[license_number][match]=in&';
            if ($companyLicenses) {
                foreach ($companyLicenses as $licenseNumber) {
                  $queryString .= '&search[license_number][value][]=' . urlencode($licenseNumber);
                }
              }
            $request = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);

            // $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company/searchByLicenses',
            //     [],
            //     [
            //         'licenses' => $companyLicenses
            //     ]
            // );

            $referenceCompanies = json_decode($request->body, true);

            if ($request->status_code >= 300) {
                throw new Exception('Problem getting data from CDM');
            }
        }

        $specialists = Helper::getSpecialists();
        $contractManagers = Helper::getContractManagers();
        $returnContracts = [];
        foreach ($contracts as $keyContract => $contract) {

            $contract = $contract->toArray();

            if ($this->contractHasCompany($contract)) {
                foreach ($referenceCompanies as $c) {
                    if ($c['license_number'] == $contract['company_general_information']['companies'][0]['license_number']) {
                        $contract['company_name_da'] = $c['full_name'];
                    }
                }

            }
            $contract['specialists'] = [];
            foreach ($specialists as $specialist) {
                foreach ($contract['contract_specialists'] as $contract_specialist) {
                    if ($specialist->id == $contract_specialist['specialist_id']) {
                        array_push($contract['specialists'], $specialist);
                    }
                }
            }
            $contract['tags'] = [];
            foreach ($contractTags as $contractTag) {
                foreach ($contract['contract_tags'] as $contract_tag) {
                    if ($contractTag['id'] == $contract_tag['tag_id']) {
                        array_push($contract['tags'], $contractTag);
                    }
                }
            }

            foreach ($contractManagers as $contractManager) {
                if ($contractManager->id == $contract['contract_manager_id']) {
                    $contract['contract_manager'] = $contractManager;
                }
            }

            $isPublished = Contract::where('id', $contract['id'])->where('is_approved', 1)
                ->whereHas('contract_status', function ($query) {
                    $query->where('status_id', 4);
                })
                ->whereHas('p_e_contact_person_verification', function ($query) {
                    $query->where('is_approved', 1);
                })
                ->whereHas('contract_approval', function ($query) {
                    $query->whereHas('contract_approval_verification', function ($query) {
                        $query->where('is_approved', 1);
                    });
                })
                ->whereHas('domestic_contract_execution_verification', function ($query) {
                    $query->where('is_approved', 1);
                })
                ->whereHas('foreign_contract_execution_location', function ($query) {
                    $query->whereHas('foreign_contract_execution_verification', function ($query) {
                        $query->where('is_approved', 1);
                    });
                })
                ->whereHas('contract_details', function ($query) {
                    $query->whereHas('contract_detail_verification', function ($query) {
                        $query->where('is_approved', 1);
                    });
                })
                ->whereHas('company_general_information', function ($query) {
                    $query->where('is_approved', 1);
                    $query->whereDoesntHave('companies', function ($query) {
                        $query->where('is_approved', 0)->orWhere('is_approved', null);
                    });
                })
                ->whereHas('contract_planning_f_a_a_p_p_verification', function ($query) {
                    $query->where('is_approved', 1);
                })
                ->whereHas('liquidated_damages_after_delivery_service_verification', function ($query) {
                    $query->where('is_approved', 1);
                })
                ->get();
            $check = false;
            if ($companies = $contracts[$keyContract]->company_general_information) {
                $companies = $contracts[$keyContract]->company_general_information->companies;
                foreach ($companies as $company) {
                    if ($company->whereHas('bank_information', function ($query) {
                            $query->where('is_approved', 0)->orWhere('is_approved', null);
                        })->get()->toArray() === []) {
                        $check = true;
                        break;
                    }
                    $check = false;
                }
            }

            $contract['can_publish'] = $isPublished->toArray() !== [] ? $check : false;
            $contract['contract_grand_total_amount'] = Calculate::totalAmount($contract['id'], false);
            $contract['contract_payment_percentage_up_to_now'] = Calculate::actualPaymentProgressPercentage($contract['id']);

            $contract['contract_physical_progress_percentage_up_to_now'] = Calculate::actualPhysicalProgressPercentage($contract['id']);
            $contract['current_status_da'] = $this->getContractStatus($contract['id']);
            $contract['is_transferred'] = $this->isContractTransferred($contract['id']);

            $contract['grant_number'] = DB::table('contracts as c')
                ->leftJoin('contract_approvals as ca', 'c.id', '=', 'ca.contract_id')
                ->leftJoin('contract_approval_verifications as cav', 'ca.id', '=', 'cav.contract_approval_id')
                ->select('ca.award_number')
                ->where('c.id', $contract['id'])
                ->first()->award_number;

            unset($contract['company_general_information']);
            $returnContracts[$keyContract] = $contract;

            foreach ($projects as $value) {
                if ($contract['project_id'] == $value['project_id']) {
                    $returnContracts[$keyContract]['id'] = $contract['id'];
                    $returnContracts[$keyContract]['name_da'] = $value['project_name_da'];
                    $returnContracts[$keyContract]['name_pa'] = $value['project_name_pa'];
                    $returnContracts[$keyContract]['name_en'] = $value['project_name_en'];

                    $returnContracts[$keyContract]['donors'] = $value['v2_project_donors'];
                    $returnContracts[$keyContract] = array_merge($returnContracts[$keyContract], array_except($value, [
                        'id',
                        'updated_at',
                        'created_at',
                        'project_name_da',
                        'project_name_pa',
                        'project_name_en',
                        'contract_number',
                        'npa_identification_number',
                        'procurement_method_selection_justification',
                    ]));
                }
            }

            if ($returnContracts[$keyContract]['contract_approval']) {
                $returnContracts[$keyContract]['approval_date'] = Date::gregorianToJalali($returnContracts[$keyContract]['contract_approval']['approval_date']);
            }
            unset($returnContracts[$keyContract]['contract_approval']);
            $returnContracts[$keyContract]['is_transferred'] = $this->checkIfTransferred($contract['id'])->is_transferred;

            // Fix: Add null check for sector_name_da
            $sectorData = isset($contract['sector_id']) && $contract['sector_id'] ? DropDowns::getById('', $contract['sector_id'], $sectors) : [];
            $returnContracts[$keyContract]['sector_name_da'] = !empty($sectorData) && isset($sectorData['name_da']) ? $sectorData['name_da'] : null;

            // Fix: Add null check for procurement_entity_name_da
            $procurementEntityData = isset($contract['procurement_entity_id']) && $contract['procurement_entity_id'] ? DropDowns::getById('', $contract['procurement_entity_id'], $procurementEntities) : [];
            $returnContracts[$keyContract]['procurement_entity_name_da'] = !empty($procurementEntityData) && isset($procurementEntityData['name_da']) ? $procurementEntityData['name_da'] : null;
            // Fix: Add null check for procurement_type_name_da
            $procurementTypeData = isset($contract['procurement_type_id']) && $contract['procurement_type_id'] ? DropDowns::getById('', $contract['procurement_type_id'], $procurementTypes) : [];
            $returnContracts[$keyContract]['procurement_type_name_da'] = !empty($procurementTypeData) && isset($procurementTypeData['name_da']) ? $procurementTypeData['name_da'] : null;

            // Fix: Add null check for procurement_method_name_da
            $procurementMethodData = isset($contract['procurement_method_id']) && $contract['procurement_method_id'] ? DropDowns::getById('', $contract['procurement_method_id'], $procurementMethods) : [];
            $returnContracts[$keyContract]['procurement_method_name_da'] = !empty($procurementMethodData) && isset($procurementMethodData['name_da']) ? $procurementMethodData['name_da'] : null;

            // Fix: Add null check for currency_name_da
            $currencyData = isset($contract['currency_id']) && $contract['currency_id'] ? DropDowns::getById('', $contract['currency_id'], $currencies) : [];
            $returnContracts[$keyContract]['currency_name_da'] = !empty($currencyData) && isset($currencyData['name_da']) ? $currencyData['name_da'] : null;

            // Fix: Add null check for contract_type_name_da
            $contractTypeData = isset($contract['contract_type_id']) && $contract['contract_type_id'] ? DropDowns::getById('', $contract['contract_type_id'], $contractTypes) : [];
            $returnContracts[$keyContract]['contract_type_name_da'] = !empty($contractTypeData) && isset($contractTypeData['name_da']) ? $contractTypeData['name_da'] : null;
            // Fix: Add null check for selection_method_name_da
            $selectionMethodData = isset($contract['selection_method_id']) && $contract['selection_method_id'] ? DropDowns::getById('', $contract['selection_method_id'], $selectionMethods) : [];
            $returnContracts[$keyContract]['selection_method_name_da'] = !empty($selectionMethodData) && isset($selectionMethodData['name_da']) ? $selectionMethodData['name_da'] : null;
            $contractDetails = ContractDetails::where('contract_id', $contract['id'])->first();
            $returnContracts[$keyContract]['plan_start_date'] = $contractDetails ? $contractDetails['planned_start_date'] : null;
            $returnContracts[$keyContract]['signed_date'] = $contractDetails ? $contractDetails['agreement_signature_date'] : null;
        }

        return [
            'data' => $returnContracts,
            'count' => $count
        ];
    }

    private function checkIfTransferred($contractId)
    {
        $value = [];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $year = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $year = $current_jalali_date[0] + 1;
        }


        $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
        $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;
        array_push($value, $fiscalStartDate);
        $statuses_id = DropDowns::getBySlugs(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            ['contract-close-out', 'cancelled']);
        $temp = count($statuses_id);
        $i = 0;
        $where = '';

        if ($temp !== 0) {
            foreach ($statuses_id as $item) {
                $i++;
                $where .= 'cs.status_id not in ( ? )';
                $where .= $i < $temp ? ' or ' : ' ';
                array_push($value, $item['id']);
            }
        } else {
            $where .= '1=1';
        }
        array_push($value, $contractId);
        $data = DB::select
        ('
                 select
                        if(cp.actual_start_date < ?  and ( ' . $where . ' or cs.status_id is null ), true, false) as is_transferred
                from contracts as c
                left join contract_progresses as cp
                    on c.id = cp.contract_id
                left join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cp.contract_id
                where c.id = ?
        ', $value)[0];
        return $data;
    }

    private function applySearch(&$query, $queryParameters)
    {
        if (array_has($queryParameters, 'contract_number')) {
            $query->where('contract_number', 'like', '%' . $queryParameters['contract_number'] . '%');
        }
        if (array_has($queryParameters, 'contract_title')) {
            $PPMSRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') .
                'api/matched-title-contracts?match_string=' . $queryParameters['contract_title'], []);

            if ($PPMSRequest->status_code >= 300) {
                throw new Exception('Problem getting data from APPMS');
            }

            $projectsId = json_decode($PPMSRequest->body, true);
            $query->whereIn('project_id', array_flatten($projectsId));
        }
        if (array_has($queryParameters, 'npa_identification_number')) {
            $query->where('npa_identification_number', 'like', '%' . $queryParameters['npa_identification_number'] . '%');
        }
        if (array_has($queryParameters, 'license_number')) {
            $tempCompany = Company::where('license_number', $queryParameters['license_number'])->with(
                ['company_general_information']
            )->get();
            $contractIds = [];
            if ($tempCompany) {
                foreach ($tempCompany as $item) {
                    array_push($contractIds, $item->company_general_information->contract_id);
                }
                $query->whereIn('contracts.id', $contractIds);
            } else {
                $query->where('contracts.id', -1);
            }
        }

        if (array_has($queryParameters, 'sector_id')) {
            $query->where('sector_id', $queryParameters['sector_id']);
        }
        if (array_has($queryParameters, 'procurement_entity_id')) {
            $query->where('procurement_entity_id', $queryParameters['procurement_entity_id']);
        }
        if (array_has($queryParameters, 'procurement_type_id')) {
            $query->where('procurement_type_id', $queryParameters['procurement_type_id']);
        }
        if (array_has($queryParameters, 'is_approved')) {
            $query->where('is_approved', 1);
        }
        if (array_has($queryParameters, 'is_published')) {
            $query->where('is_published', 1);
        }
        if (array_has($queryParameters, 'below_threshold') && !array_has($queryParameters, 'above_threshold')) {
            $query->where('is_above_threshold', false);
        }

        if (array_has($queryParameters, 'above_threshold') && !array_has($queryParameters, 'below_threshold')) {
            $query->where('is_above_threshold', 1);
        }
        if (array_has($queryParameters, 'currency_id')) {
            $query->where('currency_id', $queryParameters['currency_id']);
        }
        if (array_has($queryParameters, 'donor_id')) {
            $query->whereHas('donors', function ($query) use ($queryParameters) {
                $query->where('donor_id', $queryParameters['donor_id']);
            });
        }
        if (array_has($queryParameters, 'contract_tag_id')) {
            $query->whereHas('contract_tags', function ($query) use ($queryParameters) {
                $query->where('tag_id', $queryParameters['contract_tag_id']);
            });
        }
        if (array_has($queryParameters, 'contract_status_id')) {
            $query->whereHas('contract_status', function ($query) use ($queryParameters) {
                $query->where('status_id', $queryParameters['contract_status_id']);
            });
        }
        if (array_has($queryParameters, 'contract_manager')) {
            $query->where('contract_manager_id', $queryParameters['contract_manager']);
        }
        if (array_has($queryParameters, 'total_amount_from') ||
            array_has($queryParameters, 'total_amount_to') ||
            (array_has($queryParameters, 'total_amount_from') && array_has($queryParameters, 'total_amount_to'))) {

            $condition = null;
            if (array_has($queryParameters, 'total_amount_from') && !array_has($queryParameters, 'total_amount_to')) {
                $condition = 'where t.contract_value >=' . $queryParameters['total_amount_from'];
            }
            if (array_has($queryParameters, 'total_amount_to') && !array_has($queryParameters, 'total_amount_from')) {
                $condition = 'where t.contract_value <=' . $queryParameters['total_amount_to'];
            }

            if (array_has($queryParameters, 'total_amount_from') && array_has($queryParameters, 'total_amount_to')) {
                $condition = 'where t.contract_value >=' . $queryParameters['total_amount_from'] . ' AND t.contract_value <=' . $queryParameters['total_amount_to'];
            }
            $query->whereRaw('
                contracts.id in (
                    select t.id from (
                        select c.id,(
                            (
                                cd.actual_value +
                                ifnull(psc.provisional_sum_and_contingency,0)
                            ) + ifnull(
                                (
                                    select
                                        sum(ifnull(ca.amendment_amount, 0)) +
                                        sum(ifnull(tca.amendment_amount,0))
                                    from amendments as a
                                    join cost_amendments as ca
                                        on ca.amendment_id = a.id
                                    join time_and_cost_amendments as tca
                                        on tca.amendment_id = a.id
                                    where a.contract_id =c.id
                                ), 0
                            )
                        ) * ifnull(c.exchange_rate,1)  as contract_value
                        from contracts as c
                        join contract_details as cd
                            on cd.contract_id = c.id
                        join contract_details_verifications as cdv
                            on cdv.contract_detail_id = cd.id
                            and cdv.is_approved = true
                        left join provisional_sum_and_contingencies as psc
                            on psc.contract_detail_id = cd.id
                    ) as t ' . $condition . '
                )
            '
            );
        }
        if (array_has($queryParameters, 'physical_improvement_percent_from') ||
            array_has($queryParameters, 'physical_improvement_percent_to') || (
                array_has($queryParameters, 'physical_improvement_percent_from') && array_has($queryParameters, 'physical_improvement_percent_to'))) {
            $condition = null;
            if (array_has($queryParameters, 'physical_improvement_percent_from') && !array_has($queryParameters, 'physical_improvement_percent_to')) {
                $condition = 't.total_physical_progress >= ' . $queryParameters['physical_improvement_percent_from'];
            }
            if (array_has($queryParameters, 'physical_improvement_percent_to') && !array_has($queryParameters, 'physical_improvement_percent_from')) {
                $condition = 't.total_physical_progress <= ' . $queryParameters['physical_improvement_percent_from'];
            }
            if (array_has($queryParameters, 'physical_improvement_percent_from') && array_has($queryParameters, 'physical_improvement_percent_to')) {
                $condition = 't.total_physical_progress >= ' . $queryParameters['physical_improvement_percent_from'] . ' AND t.total_physical_progress <= ' . $queryParameters['physical_improvement_percent_to'];
            }

            $query->whereRaw('
            contracts.id in (
            select tx.contract_id from (
            select
                t.c_id as contract_id,
                t.total_physical_progress
            from
                (
                    select
                        c.id as c_id,
                        sum(physical_progress_percentage)
                        as total_physical_progress from contracts as c
                    join contract_progress_payments as cpp
                          on c.id = cpp.contract_id
                          group by c.id

                ) as t
                where ' . $condition . ') as tx )');
        }

        if (array_has($queryParameters, 'payment_percent_from') ||
            array_has($queryParameters, 'payment_percent_to') || (
                array_has($queryParameters, 'payment_percent_from') && array_has($queryParameters, 'payment_percent_to'))) {
            $condition = null;
            if (array_has($queryParameters, 'payment_percent_from') && !array_has($queryParameters, 'payment_percent_to')) {
                $condition = 'percentage >= ' . $queryParameters['payment_percent_from'];
            }
            if (array_has($queryParameters, 'payment_percent_to') && !array_has($queryParameters, 'payment_percent_from')) {
                $condition = 'percentage <= ' . $queryParameters['payment_percent_to'];
            }
            if (array_has($queryParameters, 'payment_percent_from') && array_has($queryParameters, 'payment_percent_to')) {
                $condition = 'percentage >= ' . $queryParameters['payment_percent_from'] . ' AND percentage <= ' . $queryParameters['payment_percent_to'];
            }

            $query->whereRaw('
                contracts.id in(
                    select contract_id from (
                        select
                            round((contracts_actual_amount *100) /contract_value,2) as percentage,id as contract_id
                        from
                        (
                            select (
                            (
                                cd.actual_value +
                                ifnull(psc.provisional_sum_and_contingency,0)
                            ) + ifnull(
                                        (
                                            select
                                            sum(ifnull(ca.amendment_amount, 0)) +
                                            sum(ifnull(tca.amendment_amount,0))
                                            from amendments as a
                                            join cost_amendments as ca
                                            on ca.amendment_id = a.id
                                            join time_and_cost_amendments as tca
                                            on tca.amendment_id = a.id
                                            where a.contract_id =c.id
                                        ), 0
                                    )
                            ) * ifnull(c.exchange_rate,1)  as contract_value,ifnull(ifnull(cpp.amount,0) *
                            ifnull(c.exchange_rate,1),0)as
                                contracts_actual_amount,c.id as id
                            from contracts as c
                                join contract_details as cd
                                on cd.contract_id = c.id
                                join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id
                            and cdv.is_approved = true
                                left join provisional_sum_and_contingencies as psc
                                on psc.contract_detail_id = cd.id
                            join
                                contract_progress_payments as cpp
                            on c.id = cpp.contract_id and cpp.is_approved = true
                        ) as t
                    )as tx WHERE ' . $condition . '
                )
            ');
        }

        if (array_has($queryParameters, 'agreement_signature_date_from') ||
            array_has($queryParameters, 'agreement_signature_date_to') || (
                array_has($queryParameters, 'agreement_signature_date_from') && array_has($queryParameters, 'agreement_signature_date_to'))) {
            $condition = null;
            if (array_has($queryParameters, 'agreement_signature_date_from') && !array_has($queryParameters, 'physical_improvement_percent_to')) {
                $condition = 'cd.agreement_signature_date >= "' . $queryParameters['agreement_signature_date_from'] . '"';
            }
            if (array_has($queryParameters, 'agreement_signature_date_to') && !array_has($queryParameters, 'physical_improvement_percent_from')) {
                $condition = 'cd.agreement_signature_date <= "' . $queryParameters['agreement_signature_date_to'] . '"';
            }
            if (array_has($queryParameters, 'agreement_signature_date_from') && array_has($queryParameters, 'agreement_signature_date_to')) {
                $condition = 'cd.agreement_signature_date >= "' . $queryParameters['agreement_signature_date_from'] . '" AND cd.agreement_signature_date <= "' . $queryParameters['agreement_signature_date_to'] . '"';
            }
            $query->whereRaw('
                contracts.id in (
                SELECT c.id
                    FROM contracts as c
                JOIN
                    contract_details as cd on c.id = cd.contract_id
                where ' . $condition . '
                )'
            );
        }

        if (array_has($queryParameters, 'plan_start_date_from') ||
            array_has($queryParameters, 'plan_start_date_to') || (
                array_has($queryParameters, 'plan_start_date_from') && array_has($queryParameters, 'plan_start_date_to'))) {
            $condition = null;
            if (array_has($queryParameters, 'plan_start_date_from') && !array_has($queryParameters, 'plan_start_date_to')) {
                $condition = 'cd.planned_start_date >= "' . $queryParameters['plan_start_date_from'] . '"';
            }
            if (array_has($queryParameters, 'plan_start_date_to') && !array_has($queryParameters, 'plan_start_date_from')) {
                $condition = 'cd.planned_start_date <= "' . $queryParameters['plan_start_date_to'] . '"';
            }
            if (array_has($queryParameters, 'plan_start_date_from') && array_has($queryParameters, 'plan_start_date_to')) {
                $condition = 'cd.planned_start_date >= "' . $queryParameters['plan_start_date_from'] . '" AND cd.planned_start_date <= "' . $queryParameters['plan_start_date_to'] . '"';
            }
            $query->whereRaw('
                contracts.id in (
                SELECT c.id
                    FROM contracts as c
                JOIN
                    contract_details as cd on c.id = cd.contract_id
                where ' . $condition . '
                )'
            );
        }

        if (array_has($queryParameters, 'is_transferred')) {

            $current_date = Carbon::now();
            $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
            $year = $current_jalali_date[0];
            if ($current_jalali_date[1] > 9) {
                $year = $current_jalali_date[0] + 1;
            }


            $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
            $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;

            $statuses_id = DropDowns::getBySlugs(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
                ['contract-close-out', 'cancelled']);
            $idQuery = '';
            foreach ($statuses_id as $item) {
                $idQuery .= $item['id'] . ',';
            }
            $idQuery = substr($idQuery, 0, -1);


            $query->whereRaw('
                contracts.id in(
                    select
                            c.id
                    from contracts as c
                    left join contract_progresses as cp
                        on c.id = cp.contract_id
                    left join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                    ) as cs
                        on cs.contract_id = cp.contract_id
                    where cp.actual_start_date < "' . $fiscalStartDate . '"  and ( cs.status_id not in (' . $idQuery . ') or cs.status_id is null)

                )
            ');
        }
    }

    private function applyUserManagement($request, &$contracts)
    {
        if (config('app.env') !== 'production') {

            return;
        }

        $closeOutStatusId = DropDowns::getBySlug(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            'contract-close-out')['id'];
        $contracts
            ->leftJoin('contract_statuses', 'contracts.id', '=', 'contract_statuses.contract_id')
            ->where(function ($query) use ($closeOutStatusId) {
                $query
                    ->where(function ($query) use ($closeOutStatusId) {
                        $query
                            ->where('contract_statuses.status_id', '=', $closeOutStatusId)
                            ->where('contract_statuses.date', '>=', now()->subDays(config('custom.CUSTOM_CONTRACT_EXPIRY_DAYS')));
                    })
                    ->orWhere('contract_statuses.id', '!=', $closeOutStatusId)
                    ->orWhereNull('contract_statuses.id');
            })->select('contracts.*');
        $resourcesAssigned = Helper::getResourceAssigned($request['user_id']);
        if (!count((array)$resourcesAssigned)) {
            $contracts->whereRaw('1=2');
        }
        $contracts->where(function ($contracts) use ($resourcesAssigned) {
            $functionName = lcfirst('WhereIn');
            foreach ($resourcesAssigned as $key => $value) {
                switch ($key) {
                    case 'user_role_pes':
                        $procurementEntityIds = [];
                        foreach ($value as $item) {
                            array_push($procurementEntityIds, $item->procurement_entity_id);
                        }
                        $contracts->$functionName('procurement_entity_id', $procurementEntityIds);
                        break;
                    case 'user_role_sectors':
                        // $sectorIds = [];
                        // foreach ($value as $item) {
                        //     array_push($sectorIds, $item->sector_id);
                        // }
                        // $contracts->$functionName('sector_id', $sectorIds);
                        break;
                    case 'user_role_vendor':

                        $contractIds = [];
                        $licenseNumber = $value->vendor_license_number;
                        $data = DB::select
                        ('  select distinct
                                cgi.contract_id
                            from company_general_informations as cgi
                            join companies as com
                                on cgi.id = com.company_general_information_id
                            where com.license_number = ?
                            ', [$licenseNumber]);
                        foreach ($data as $item) {
                            array_push($contractIds, $item->contract_id);
                        }
                        $contracts->$functionName('contracts.id', $contractIds);
                        break;
                    case 'user_role_records':
                        $contractIds = [];
                        foreach ($value as $item) {
                            array_push($contractIds, $item->record_id);
                        }
                        $contracts->$functionName('contracts.id', $contractIds);
                        break;
                    default:
                        throw new Exception('Invalid user role');
                }
                $functionName = lcfirst('orWhereIn');
            }
        });
        return;
    }

    public function projects()
    {


        $lastSyncedId = CommonValue::where('common_key', 'last_synced_id')->first()->common_value;
        $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info',
            [], [
                'last_synced_id' => $lastSyncedId
            ]
        );

        if ($request->status_code > 400) {
            throw new Exception($request->body);
        }

        $contractStatusPending = DropDowns::getBySlug(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus', 'pending');
        $currentDate = Carbon::now()->toDateTimeString();
        try {
            DB::beginTransaction();
            foreach (json_decode($request->body, true) as $r) {
                if ($r['npa_identification_number'] !== '') {
                    $r['name_da'] = $r['project_name_da'];
                    $r['name_pa'] = $r['project_name_pa'];
                    $r['name_en'] = $r['project_name_en'];
                    unset($r['project_name_da']);
                    unset($r['project_name_pa']);
                    unset($r['project_name_en']);
                    $created = Contract::create(array_only($r, Contract::$fields));
                    if ($r['selection_method_id'] && isset($created['id'])) {
                        ConsultancyContractsSelectionMethod::create([
                            'contract_id' => $created['id'],
                            'selection_method_id' => $r['selection_method_id']
                        ]);
                    }
                    $contractStatusCreated = ContractStatus::create([
                        'status_id' => $contractStatusPending['id'],
                        'date' => $currentDate,
                        'remarks' => '',
                        'contract_id' => $created['id']
                    ]);
                    ContractStatusVerification::create([
                        'contract_status_id' => $contractStatusCreated['id'],
                        'published_date' => $currentDate
                    ]);
                    ContractStatusLog::create([

                        'status_id' => $contractStatusPending['id'],
                        'date' => $currentDate,
                        'remarks' => '',
                        'contract_id' => $created['id'],
                        'contract_status_id' => $contractStatusCreated['id']

                    ]);
                    CommonValue::where('common_key', 'last_synced_id')->update([
                        'common_value' => $r['project_id']
                    ]);
                }
            }
            DB::commit();
        } catch (\Throwable $t) {
            DB::rollback();
            return Error::composeResponse($t);
        }
        return response()->json(['list' => json_decode($request->body)]);
    }

    public function project($contract_number)
    {
        while (str_contains($contract_number, '%')) {
            $contract_number = urldecode($contract_number);
        }

        $project = Contract::select('id', 'project_id', 'contract_number',
            'procurement_type_id')->where('contract_number', $contract_number)->first();
        return response()->json($project, 200);
    }

    public function store(Request $request)
    {
        try {
            $contract = Contract::find($request->contract_id);
            $contract->contract_number = $request->input('contract_number');
            $contract->currency = $request->input('currency');
            $contract->exchange_rate = $request->input('exchange_rate');
            $contract->is_above_threshold = $request->input('is_above_threshold');
            $contract->save();
            return response()->json(['success' => true], 201, [
                'location' => $contract
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function show($id)
    {
        try {
            $contract = Contract::find($id);
            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [], [
                'ids' => [$contract->project_id]
            ]);
            $project = json_decode($request->body, true);

            if (!$project) {
                return response()->json([], 404);
            }
            $contract = json_decode($contract, true);
            $project[0]['name_da'] = $project[0]['project_name_da'];
            $project[0]['name_pa'] = $project[0]['project_name_pa'];
            $project[0]['name_en'] = $project[0]['project_name_en'];
            unset($project[0]['project_name_da']);
            unset($project[0]['project_name_pa']);
            unset($project[0]['project_name_en']);
//            $contract = array_collapse([$contract, $project[0]]);

          $contract = array_merge($contract, array_except($project[0], [
            'id',
            'updated_at',
            'created_at',
            'contract_number',
            'npa_identification_number'
          ]));

            $data['contract'] = $contract;
            // $data['grand_total_amount'] = Calculate::totalAmount($id);
            $data['contract']['budget_code'] = DropDowns::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/code', $data['contract']['budget_code_id']);
            $data['contract']['budget_type'] = $data['contract']['budget_code']['budget_code_fund_type'];
            unset($data['contract']['budget_code']['budget_code_fund_type']);
            unset($data['contract']['budget_code']['budget_code_status']);
            unset($data['contract']['budget_code']['budget_code_optional_non_optional']);
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function update(Request $request, $id)
    {
        try {
            if (isset($request['is_published']) && $request['is_published'] && !$request['has_requested_to_unpublish']) {
                $currentDate = Carbon::now()->toDateTimeString();
                Contract::where('id', $request['id'])
                    ->update(
                        [
                            'is_published' => $request['is_published'],
                            'published_date' => $currentDate,
                        ]);
                return response()->json([], 204);
            }
            if (isset($request['has_requested_to_unpublish'])) {
                Contract::where('id', $request['id'])
                    ->update(
                        [
                            'has_requested_to_unpublish' => $request['has_requested_to_unpublish'],
                            'is_published' => $request['is_published'],
                        ]);
                return response()->json([], 204);
            }

            if (isset($request['is_confirmed'])) {
                Contract::where('id', $request['id'])
                    ->update(
                        [
                            'is_confirmed' => $request['is_confirmed'],
                            'is_approved' => $request['is_approved'],
                        ]);
                return response()->json([], 204);
            }
            $contract = Contract::find($id);

            $contract->update([
                'currency_id' => $request->currency_id['id'],
                'exchange_rate' => $request->exchange_rate,
                // 'is_above_threshold' => $request->is_above_threshold,
                // 'contract_number' => $request->contract_number
            ]);
            return response()->json($contract, 201);
        } catch (Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    public function isPublished($contractId)
    {
        var_dump('is published', $contractId);
        die();
    }

    public function scanOneToOneForms($contractId)
    {
        try {
            $returnArray = [];
            $contractIdLabel = 'contract_id';

            // TODO: Uncomment the following when their migrations are done

            // Contract planning

            $count = ContractApproval::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning'] = ['contractApproval' => $count > 0];

            $count = ForeignContractExecutionLocation::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['contractExecutionLocation'] = ['foreign' => $count > 0];

            $contractDetails = ContractDetails::select('is_delay_penalty_applicable', 'is_defect_liability_applicable')->where($contractIdLabel, $contractId)->first();
            if ($contractDetails) {
                $returnArray['contractPlanning']['contractDetails'] = $contractDetails->count() > 0;
                $contractDetails['is_delay_penalty_applicable']
                    ? $returnArray['contractPlanning']['isContractDetailsDelayPenaltyApplicable'] = true
                    : $returnArray['contractPlanning']['isContractDetailsDelayPenaltyApplicable'] = false;
                $contractDetails['is_defect_liability_applicable']
                    ? $returnArray['contractPlanning']['isContractDetailsDefectLiabilityApplicable'] = true
                    : $returnArray['contractPlanning']['isContractDetailsDefectLiabilityApplicable'] = false;
            }

            $count = Subcontract::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['subcontract'] = $count > 0;

            $count = LiquidatedDamagesAfterDeliveryService::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['liquidatedDamagesPeriodComponent'] = $count > 0;

            $count = CompanyGeneralInformation::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['companyGeneralInformation']['value'] = $count > 0;

            $returnArray['contractPlanning']['company'] = [];
            $returnArray['contractPlanning']['company']['value'] = false;

            if ($count > 0) {
                $contractGeneralInformation = CompanyGeneralInformation::where($contractIdLabel, $contractId)->first();
                $returnArray['contractPlanning']['companyGeneralInformation']['companyParentId'] = $contractGeneralInformation['id'];
                $returnArray['contractPlanning']['companyGeneralInformation']['isJointVenture'] = $contractGeneralInformation['is_joint_venture'];

                $returnArray['contractPlanning']['company']['value'] = Company::where('company_general_information_id', $contractGeneralInformation['id'])->count() > 0;

            }

            $count = ContractPlanningAdvance::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['financeAffairsPhysicalProgress']['contractPlanningAdvance'] = $count > 0;

            $count = ContractRePlanningDateFAAPP::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['ContractRePlan'] = $count > 0;

            $contractRePlanDate = ContractRePlanningDateFAAPP::select('id')
                ->where($contractIdLabel, $contractId)
                ->orderBy('id', 'desc')
                ->first();
            if ($contractRePlanDate) {
                $contractRePlanDateVerification = ContractRePlanningDateFAAPPVerification::where('contract_re_planning_date_f_a_a_p_p_id', $contractRePlanDate['id'])->first();
                $contractRePlanDateVerification ?
                    $returnArray['contractPlanning']['IsContractRePlanApproved'] = $contractRePlanDateVerification['is_approved'] :
                    $returnArray['contractPlanning']['IsContractRePlanApproved'] = 0;
            } else {
                $returnArray['contractPlanning']['IsContractRePlanApproved'] = 0;
            }


            // Contract progress

            $count = ContractProgress::where($contractIdLabel, $contractId)->count();
            $returnArray['contractProgress']['contract'] = $count > 0;

            $count = ContractProgressPayment::where($contractIdLabel, $contractId)->count();
            $returnArray['contractProgress']['payments'] = $count > 0;

            $count = ContractStatus::where($contractIdLabel, $contractId)->count();
            $returnArray['contractProgress']['financeAffairsPhysicalProgress'] = ['contractStatus' => $count > 0];

            $count = ContractProgressAdvance::where($contractIdLabel, $contractId)->count();
            $returnArray['contractProgress']['financeAffairsPhysicalProgress']['contractProgressAdvance'] = $count > 0;

            $count = ContractRePlanningDateFAAPP::where($contractIdLabel, $contractId)->count();
            $returnArray['contractPlanning']['ContractRePlan'] = $count > 0;

            // Contract General Information
            $count = ContractException::where($contractIdLabel, $contractId)->count();
            $returnArray['contractGeneralInfo']['exception'] = $count > 0;

            $returnArray['contractGeneralInfo']['procurementEntity'] = [
                'id' => Contract::find($contractId)->procurement_entity_id
            ];

            // Contract General Information verification
            $count = ContractGeneralInformationVerification::where($contractIdLabel, $contractId)->where('is_confirmed', 1)->count();
            $returnArray['contractGeneralInfo']['isConfirmed'] = $count > 0;

            return response()->json($returnArray);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function roleUsersMap($contractId)
    {
        $contract = Contract::where('id', $contractId)->first();
        $contractManager = Helper::getContractContractManager($contractId);
        if ($contractManager) {
            $contractManager->user_id = $contractManager->id;
            $contractManager->user_full_name = $contractManager->full_name;
        }
        $specialist = Helper::getContractSpecialist($contractId);
        if ($specialist) {
            $specialist->user_id = $specialist->id;
            $specialist->user_full_name = $specialist->full_name;

        }
        $cpmManager = Helper::getCpmManager($contract->sector_id);
        $awardAuthority = Helper::getAwardAuthority($contract->procurement_entity_id);
        // $cpmDirector = Helper::getCpmDirector();
        // if ($cpmDirector) {
        //     $cpmDirector->user_id = $cpmDirector->id;
        //     $cpmDirector->user_full_name = $cpmDirector->full_name;
        // }
        foreach ($cpmManager as $item) {
            $item->user_id = $item->id;
            $cpmManager = $item;
            break;
        }
        foreach ($awardAuthority as $item) {
            $item->user_id = $item->id;
            $awardAuthority = $item;
            break;
        }

        return response()->json([
            'cpms-contract-manager' => $contractManager,
            'cpms-specialist' => $specialist,
            'cpms-award-authority' => $awardAuthority,
            'cpms-cpm-manager' => $cpmManager,
            // 'cpms-cpm-director' => $cpmDirector
        ]);
    }

    public function savePlannedPaymentSchedule(Request $request)
    {
        try {
            $data = $request->all();
            $contractData = Contract::where('id', $data['contract_id'])->first();
            AttachmentController::saveFile($request, $contractData, 'planned_payments_schedule', $data['planned_payments_schedule']);
            return response()->json([], 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function confirmContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            Contract::where('id', $contract_id)->update(['is_confirmed' => true]);
            return response()->json([], 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    public function unConfirmContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            $is_confirmed = Contract::where('id', $contract_id)->update(['is_confirmed' => false]);
            return response()->json($is_confirmed, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function requestToUnApproveContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            $is_confirmed = Contract::where('id', $contract_id)->update(['has_requested_to_unapprove' => true]);
            return response()->json($is_confirmed, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function getContractStatus($contractId)
    {
        $status_id = DB::table('contracts as c')
            ->leftJoin('contract_statuses as cs', 'c.id', '=', 'cs.contract_id')
            ->leftJoin('contract_status_verifications as csv', 'cs.id', '=', 'csv.contract_status_id')
            ->select('cs.status_id')
            ->where('c.id', $contractId)->first()->status_id;
        if ($status_id) {
            $status = DropDowns::get(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus', $status_id);
            $status = $status['name_da'];
            return $status;
        }
    }

    public function isContractTransferred($contractId)
    {
        $value = [];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $year = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $year = $current_jalali_date[0] + 1;
        }

        $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
        $fiscalEndArray = JDateTime::toGregorian($year, 9, 30);
        $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;
        $fiscalEndDate = $fiscalEndArray ? $fiscalEndArray[0] . '-' . $fiscalEndArray[1] . '-' . $fiscalEndArray[2] : null;
        array_push($value, $fiscalStartDate);
        $statuses_id = DropDowns::getBySlugs(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            ['contract-close-out', 'cancelled']);
        $temp = count($statuses_id);
        $i = 0;
        $where = '';

        if ($temp !== 0) {
            foreach ($statuses_id as $item) {
                $i++;
                $where .= 'cs.status_id not in ( ? )';
                $where .= $i < $temp ? ' or ' : ' ';
                array_push($value, $item['id']);
            }
        } else {
            $where .= '1=1';
        }
        array_push($value, $contractId);
        $data = DB::select
        ('
            select
                case when
                    cp.actual_start_date < ?  and
                    ( ' . $where . ' or cs.status_id is null)
                then true end
                from contracts as c
                inner join contract_progresses as cp
                    on c.id = cp.contract_id
                join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                    ) as cs
                on cs.contract_id = cp.contract_id
                where c.id = ?
            ', $value);
        return $data ? true : false;
    }

    public function approveContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            DB::beginTransaction();
            $contract_approval_id = ContractApproval::select('id')->where('contract_id', $contract_id)->first();
            $return_value = ContractApprovalVerification::where('contract_approval_id', $contract_approval_id->id)
                ->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = PEContactPersonVerification::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = DomesticContractExecutionVerification::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $find_foreign = ForeignContractExecutionLocation::select('id')->where('contract_id', $contract_id)->first();
            if ($find_foreign) {
                $return_value = ForeignContractExecutionVerification::where('foreign_exec_id', $find_foreign->id)->where('is_confirmed', 1)->update(['is_approved' => true]);
                if (!$return_value) {
                    throw new Error('|||NPA-ACPMS-0002|||');
                }
            }
            $contract_details = ContractDetails::select('id')->where('contract_id', $contract_id)->first();
            $return_value = ContractDetailsVerification::where('contract_detail_id', $contract_details->id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = CompanyGeneralInformation::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $company_general_info = CompanyGeneralInformation::select('id')->where('contract_id', $contract_id)->where('is_confirmed', 1)->first();
            $companies = Company::where('company_general_information_id', $company_general_info->id)->get();
            foreach ($companies as $company) {
                $return_value = Company::where('id', $company->id)->where('is_confirmed', 1)->update(['is_approved' => true]);
                if (!$return_value) {
                    throw new Error('|||NPA-ACPMS-0002|||');
                }
                $banks = CompanyBankInformation::where('company_id', $company->id)->get();
                foreach ($banks as $bank) {
                    $return_value = CompanyBankInformation::where('id', $bank->id)->where('is_confirmed', 1)->update(['is_approved' => true]);
                    if (!$return_value) {
                        throw new Error('|||NPA-ACPMS-0002|||');
                    }
                }
            }

            $return_value = SubcontractVerification::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = ContractPlanningFAAPPVerification::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = LiquidatedDamagesDeliveryVerification::where('contract_id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            $return_value = Contract::where('id', $contract_id)->where('is_confirmed', 1)->update(['is_approved' => true]);
            if (!$return_value) {
                throw new Error('|||NPA-ACPMS-0002|||');
            }

            DB::commit();
            return response()->json([], 200);
        } catch (Error $t) {
            DB::rollBack();
            return Error::composeResponse($t, $t->getNpaCode());
        }
    }

    public function publishContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            $is_approved = Contract::where('id', $contract_id)->update(['is_published' => true]);
            return response()->json($is_approved, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    public function requestToUnPublishContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            $is_approved = Contract::where('id', $contract_id)->update(['has_requested_to_unpublish' => true]);
            return response()->json($is_approved, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function respondToRequestToUnApproveContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            DB::beginTransaction();
            $is_approved = Contract::where('id', $contract_id)->update(['is_approved' => false]);
            Contract::where('id', $contract_id)->update(['has_requested_to_unapprove' => false]);
            DB::commit();
            return response()->json($is_approved, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function respondToRequestToUnPublishContractPlanning(Request $request)
    {
        $contract_id = $request->id;
        try {
            $is_unpublished = Contract::where('id', $contract_id)->update(['is_published' => false]);
            $has_requested_to_unpublish = Contract::where('id', $contract_id)->update(['has_requested_to_unpublish' => false]);
            return response()->json($is_unpublished, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function contractGeneralVerification($contractId)
    {
        $isConfirmed = Contract::select('is_confirmed')->where('id', $contractId)->first();
        return response()->json($isConfirmed);

    }

    private function contractHasCompany($contract)
    {
        return $contract['company_general_information'] &&
            $contract['company_general_information']['companies'] &&
            array_key_exists(0, $contract['company_general_information']['companies']) &&
            array_key_exists('license_number', $contract['company_general_information']['companies'][0]);
    }

    public function getAllContractManagers(Request $request)
    {
        try {
            $contractManagerIds = DB::select('
                    select c.contract_manager_id
                    from contracts as c
                    join contract_specialists as cs
                      on cs.contract_id = c.id
                    where cs.specialist_id = ?
            ', [$request['user_id']]);
            $ids = [];
            foreach ($contractManagerIds as $contractManagerId) {
                if ($contractManagerId->contract_manager_id) {
                    array_push($ids, $contractManagerId->contract_manager_id);
                }
            }
            if (!count($ids)) {
                return response()->json([]);
            }
            $contractManagers = Helper::getUsersByIds($ids);
            return response()->json($contractManagers);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function assignContractTags(Request $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->all();
            $contractTags = ContractTags::where('contract_id', $data['contract_id'])->get();
            ContractTags::where('contract_id', $data['contract_id'])->delete();
            $TagData = [];
            if ($data['contract_tags'] === '[]') {
                DB::commit();
                return response()->json([], 201);
            }
            foreach ($data['contract_tags'] as $contractTag) {
                array_push($TagData, [
                    'tag_id' => $contractTag['id'],
                    'contract_id' => $data['contract_id']
                ]);
            }
            ContractTags::insert($TagData);
            DB::commit();
            return response()->json([], 201);
        } catch (Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    public function removeReadOnlySectionsVerification($contractId)
    {

        try {
            DB::beginTransaction();
            Contract::where('id', $contractId)->update(['is_published' => false]);
            Contract::where('id', $contractId)->update(['is_approved' => false]);
            Contract::where('id', $contractId)->update(['is_confirmed' => false]);
            ContractPlanningFAAPPVerification::where('contract_id', $contractId)->delete();
            ContractPlanningFinanceAffairsAndPhysicalProgress::where('contract_id', $contractId)->delete();
            ContractProgress::where('contract_id', $contractId)->delete();
            ContractProgressPayment::where('contract_id', $contractId)->delete();
            Amendment::where('contract_id', $contractId)->delete();
            ContractRePlanningDateFAAPP::where('contract_id', $contractId)->delete();
            ContractProgressPaymentAfterDueDate::where('contract_id', $contractId)->delete();
            DB::commit();
            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }


    }
}
