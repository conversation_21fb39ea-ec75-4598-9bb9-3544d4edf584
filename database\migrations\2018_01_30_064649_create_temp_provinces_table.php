<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;


class CreateTempProvincesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('temp_provinces', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order')
                ->nullable();
            $table->string('name_da')
                ->unique();
            $table->string('name_pa')
                ->unique();
            $table->string('name_en')
                ->unique();
            $table->integer('temp_zone_id')->unsigned();
            $table->foreign('temp_zone_id')
                ->references('id')->on('temp_zones')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('slug')
                ->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public
    function down()
    {
        Schema::dropIfExists('temp_provinces');
    }
}
