<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRmarksColumnToSubcontractSignedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->string('approval_date')->nullable()->change();
            $table->string('end_date')->nullable()->change();
            $table->string('value')->nullable()->change();
            $table->string('percentage')->nullable()->change();
            $table->string('currency_type_id')->nullable()->change();
            $table->string('company_licence_end_date')->nullable()->change();
            $table->string('company_contact_number')->nullable()->change();
            $table->string('company_id')->nullable()->change();
            $table->string('remarks')
                ->after('company_contact_number');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subcontract_signeds', function (Blueprint $table) {
            $table->dropColumn('remarks');
        });
    }
}
