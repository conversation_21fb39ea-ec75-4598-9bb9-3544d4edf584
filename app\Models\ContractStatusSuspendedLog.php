<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusSuspendedLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'suspend_reason',
        'contract_status_id',
        'contract_status_suspended_id',
        'contract_status_log_id'
    ];

    public function contract_status_suspended()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusSuspend');
    }
}
