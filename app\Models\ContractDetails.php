<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractDetails extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'agreement_signature_date',
        'planned_start_date',
        'planned_end_date',
        'estimated_value',
        'actual_value',
        'is_provisional_sum_and_contingency_applicable',
        'is_provisional_sum_and_contingency_included',
        'is_performance_security_applicable',
        'is_delay_penalty_applicable',
        'is_contract_category_transfers_or_rental_of_vehicles',
        'is_defect_liability_applicable',
        'is_advance_payment_applicable',
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract_detail_verification() {
        return $this->hasOne('NPA\ACPMS\Models\ContractDetailsVerification','contract_detail_id');
    }
}
