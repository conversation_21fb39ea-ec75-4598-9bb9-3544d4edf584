<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionApplicableLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'is_returned',
        'contract_retention_id',
        'contract_retention_applicable_id',
        'contract_retention_log_id'
    ];

    public function contract_retention_applicable()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionApplicable');
    }
    public function contract_retention_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionLogs');
    }
}
