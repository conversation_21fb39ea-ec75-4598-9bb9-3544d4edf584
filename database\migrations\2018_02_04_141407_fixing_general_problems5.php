<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingGeneralProblems5 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('challenges_and_remarks', function (Blueprint $table) {
            $table->text('description')->change();
            $table->text('suggested_solution')->change();
            $table->text('applied_solution')->change();
        });

        Schema::table('user_companies', function (Blueprint $table) {
            $table->unique('contract_id');
            $table->unsignedInteger('contract_id')->change();
            $table->foreign('contract_id')
                ->references('id')
                ->on('contracts')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_companies', function (Blueprint $table) {
            $table->dropForeign('user_companies_contract_id_foreign');
            $table->dropUnique('user_companies_contract_id_unique');
        });
    }
}
