<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangesPublishedDateTypeOnAfterDueDates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->dateTime('published_date')
                ->change()
                ->nullable()
                ->default(NULL);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->boolean('published_date')
                ->change()
                ->nullable()
                ->default(NULL);
        });
    }
}
