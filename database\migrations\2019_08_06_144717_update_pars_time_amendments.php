<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateParsTimeAmendments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pars_time_amendments', function (Blueprint $table) {
            $table->dropColumn('amended_performance_security_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pars_time_amendments', function (Blueprint $table) {
            $table->integer('amended_performance_security_amount')->after('amended_performance_security_end_date')->nullable();
        });
    }
}
