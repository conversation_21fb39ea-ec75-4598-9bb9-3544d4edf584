<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusVerification extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'is_confirmed',
        'is_approved',
        'is_published',
        'contract_status_id',
        'published_date'
    ];

    public function contract_status()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function contract_status_verification_logs()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusVerificationLog');
    }
}
