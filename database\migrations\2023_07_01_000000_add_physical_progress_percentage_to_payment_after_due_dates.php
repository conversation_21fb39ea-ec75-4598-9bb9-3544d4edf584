<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPhysicalProgressPercentageToPaymentAfterDueDates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->integer('physical_progress_percentage')
                ->after('amount')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->dropColumn('physical_progress_percentage');
        });
    }
}
