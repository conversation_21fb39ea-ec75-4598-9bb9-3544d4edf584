<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use Illuminate\Http\Request;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\LiquidatedDamagesDeliveryVerification;

class LiquidatedDamagesDeliveryVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $createdId = Contract::find($data['contract_id'])
                ->liquidated_damages_after_delivery_service_verification()
                ->create(array_only($data, LiquidatedDamagesDeliveryVerification::$fields))->id;
            return response()->json([], 201, [
                'location' => $createdId
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {
        $verificationData = LiquidatedDamagesDeliveryVerification::where('contract_id', $contractId)->first();
        if (isset($verificationData)) {
            return response()->json($verificationData, 200);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\LiquidatedDamagesAndAfterDeliveryServiceVerification $liquidatedDamagesAndAfterDeliveryServiceVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(LiquidatedDamagesDeliveryVerification $liquidatedDamagesAndAfterDeliveryServiceVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\LiquidatedDamagesAndAfterDeliveryServiceVerification $contractId
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractId)
    {
        $data = $request->all();
        $data['contract_id'] = $data['parent_id'];
        unset($data['parent_id']);
        try {
            $contractId =
                LiquidatedDamagesDeliveryVerification::where('contract_id', $contractId)->first();
            $isUpdated = $contractId->update(array_only($data, LiquidatedDamagesDeliveryVerification::$fields));
            if (!$isUpdated) {
                return Error::exceptionNotFound();
            }
            return response()->json($contractId, 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\LiquidatedDamagesAndAfterDeliveryServiceVerification $liquidatedDamagesAndAfterDeliveryServiceVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(LiquidatedDamagesDeliveryVerification $liquidatedDamagesAndAfterDeliveryServiceVerification)
    {
        //
    }
}
