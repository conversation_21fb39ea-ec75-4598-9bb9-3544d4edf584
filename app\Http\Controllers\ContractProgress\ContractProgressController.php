<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification;

class ContractProgressController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $builder = ContractProgress::whereRaw('1=1');
        try {
            $updatedBuilder = Query::buildQuery($builder, $request->query());
            return response()->json($updatedBuilder->get());

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $id = ContractProgress::create(array_only($request->all(), ContractProgress::$fields))->id;
            return response()->json([], 201, [
                'location' => $id
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $contractId
     * @return \Illuminate\Http\Response
     */
    public function show($contractId)
    {
        try {
            $contractProgress = ContractProgress::select()->where('contract_id', $contractId)->first();
            if (!isset($contractProgress)) {
                return response()->json([], 404);
            }
            $plannedEndDate = ContractDetails::select('planned_end_date')->where('contract_id', $contractId)->first();
            if (!isset($plannedEndDate)) {
                return response()->json([], 404);
            }
            $contractProgress['planned_end_date'] = $plannedEndDate['planned_end_date'];
            return response()->json($contractProgress);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param $contractProgressId
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $contractId)
    {
        try {
            $updatedRowsCount = ContractProgress
                ::where('contract_id', $contractId)
                ->update(array_only($request->all(), ContractProgress::$fields));

            if ($updatedRowsCount > 0) {
                return response()->json([], 204);
            } else {
                throw Error::exceptionNotFound();
            }
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

    }

    public function limitDates($contractId)
    {
        try {
            $returnResult = [];

            $resultPlanned = ContractDetails
                ::select('planned_end_date')
                ->where('contract_id', $contractId)->first();
            $returnResult['planned_end_date'] = $resultPlanned ? $resultPlanned->planned_end_date : null;

            $resultActual = ContractProgress
                ::select('actual_start_date')
                ->where('contract_id', $contractId)->first();
            $returnResult['actual_start_date'] = $resultActual ? $resultActual->actual_start_date : null;

            return response()->json($returnResult);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function isRePlanNeeded(Request $request)
    {
        $amendmentInContractConditionsStatus = DropDowns::getBySlug(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') .
            'api/dropDown/amendmentType?path=api/dropDown/', 'amendment-in-contract-conditions');
        $contractId = $request->query('contract_id');
        $amendment = Amendment::where('contract_id', $contractId)
            ->where('is_confirmed', true)
            ->where('type_id', '<>', $amendmentInContractConditionsStatus['id'])
            ->orderBy('id', 'desc')
            ->first();
        if ($amendment) {
            if ($amendment['re_plan_id']) {
                $contractRePlanningDateFAAPPVerification = ContractRePlanningDateFAAPPVerification::
                where('contract_re_planning_date_f_a_a_p_p_id', $amendment['re_plan_id'])
                    ->where('is_confirmed', true)->first();
                if ($contractRePlanningDateFAAPPVerification) {
                    return response()->json(['is_re_plan_needed' => false], 200);
                }
            }
            return response()->json(['is_re_plan_needed' => true], 200);
        } else {
            return response()->json(['is_re_plan_needed' => false], 200);

        }
    }
}
