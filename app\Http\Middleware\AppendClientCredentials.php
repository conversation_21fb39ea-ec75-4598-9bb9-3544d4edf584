<?php

namespace NPA\ACPMS\Http\Middleware;

use Closure;

class AppendClientCredentials
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $input = $request->all();
        $input['client_id'] = '2';
        $input['client_secret'] = env('CLIENT_KEY');
        $input['grant_type'] = 'password';
        $input['scope'] = '*';
        $request->replace($input);
        return $next($request);
    }
}
