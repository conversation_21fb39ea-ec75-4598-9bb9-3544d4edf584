<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ContractStatusCancelledsCorrections extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_status_cancelleds', function (Blueprint $table){
            $table->dropColumn('is_loss_implementable');
            $table->dropColumn('loss_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_status_cancelleds', function (Blueprint $table){
            $table->boolean('is_loss_implementable')->default(false)->after('cancelled_reason');
            $table->integer('loss_amount')->after('is_loss_implementable');
        });
    }
}
