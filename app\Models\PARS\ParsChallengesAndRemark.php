<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsChallengesAndRemark extends Model
{
    public static $fields = [
        'p_a_r_id',
        'title',
        'creator',
        'category',
        'start_date',
        'description',
        'suggested_solution_procurement_entity',
        'suggested_solution_company',
        'applied_solution',
        'updated_timestamp',
        'status'
    ];

    protected $guarded = ['id'];


}
