<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionReturnedLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'returned_date',
        'contract_retention_applicable_id',
        'contract_retention_returned_id',
        'contract_retention_log_id'
    ];

    public function contract_retention_returned()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionReturned');
    }
    public function contract_retention_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionLogs');
    }
}
