<?php

namespace NPA\ACPMS\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Alert;
use NPA\ACPMS\Models\AlertInstance;
use NPA\ACPMS\Models\AlertInstanceStatusLog;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\Remark;
use NPA\ACPMS\Models\RemarkMessage;
use NPA\ACPMS\Models\UserRemarksStatusLog;
use Throwable;

class RemarkController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {

// TODO: Make the status ID dynamic
            $userId = $request['user_id'];

            $remarksRawData = Remark
                ::whereRaw(
                    '
                        remarks.id in (
                            select inner_ursl.remark_id
                            from user_remarks_status_logs as inner_ursl
                            where inner_ursl.user_id = ?
                            group by inner_ursl.remark_id
                            having max(inner_ursl.remark_status_id) != ' . '4' . '
                        )
                    ', [$userId])
                ->with([
                    'user_remarks',
                    'remark_messages',
                    'contract'
                ])
                ->orderBy('id', 'desc')
                ->get();
            foreach ($remarksRawData as $k => $remark) {
                $rawRecipients = [];
                foreach ($remark['user_remarks'] as $userRemark) {
                    $rawRecipients[] = $userRemark->user_id;
                }

                $rawRecipients = array_unique($rawRecipients);
                $rawRecipients = Helper::getUsersByIds($rawRecipients);

                $recipients = [];
                foreach ($rawRecipients as $r) {
                    $recipients[] = [
                        'user_id' => $r->id,
                        'user_full_name' => $r->full_name,
                        'role_slug' => $r->role_slug,
                    ];
                }
                $remarksRawData[$k]['recipients'] = $recipients;
            }

            //=== Combining data

            $projectIds = [];
            $remarks = [];
            foreach ($remarksRawData as $r) {

                $projectId = Contract::find($r['contract_id'])->project_id;
                $projectIds[] = $projectId;

                $temp = [
                    'id' => $r->id,
                    'title' => $r->title,
                    'alert_subcategory_id' => $r->alert_subcategory_id,
                    'contract' => [
                        'id' => $r->contract->id,
                        'name_da' => '',
                        'contract_number' => $r->contract->contract_number,
                    ],
                    'messages' => [],
                    'url' => $r->url,
                    'recipients' => $r['recipients']
                ];
                $ids = [];
                foreach ($r->remark_messages as $m) {
                    array_push($ids, $m->creator_user_id);
                }
                $creators = Helper::getUsersByIds($ids);
                foreach ($r->remark_messages as $m) {
                    foreach ($creators as $creator) {
                        if ($m->creator_user_id == $creator->id) {
                            $temp['messages'][] = [
                                'by' => $creator->full_name,
                                'contents' => $m->contents,
                                'date' => $m->created_at
                            ];
                        }
                    }
                }

                $remarks[] = $temp;
            }

            //=== Getting project names

            $request = Http::post(
                config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info',
                [],
                [
                    'ids' => $projectIds
                ]
            );

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            $projectsInfo = json_decode($request->body, true);

            foreach ($projectsInfo as $p) {
                foreach ($remarks as $i => $r) {
                    if ($p['contract_number'] === $r['contract']['contract_number']) {
                        $remarks[$i]['contract']['name_da'] = $p['project_name_da'];
                    }
                }
            }

            return response()->json($remarks);
        } catch (Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    private function mapCpmsToCdmRoles($cpmsRole)
    {
        switch ($cpmsRole) {
            case 'contract-manager':
                return 'cpms-contract-manager';
            case 'award-authority':
                return 'cpms-award-authority';
            case 'specialist':
                return 'cpms-specialist';
            case 'cpmd-manager':
                return 'cpms-cpm-manager';
            // case 'cpmd-director':
            //     return 'cpms-cpm-director';
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {

            $dropDownAlertStatusNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'new'
            );
            $dropDownRemarkStatusNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/remarkStatus',
                'new'
            );

            DB::beginTransaction();
            $data = $request->all();

            // === Dealing with alert

            $createdAlertId = Alert::create([
                'contents' => 'یک ملاحظه جدید در قرارداد "' . $data['contract_number'] . '" درج گردید'
            ])->id;

//            $loggedInUserId = auth('api')->user()->id; //todo remove this line
            $loggedInUserId = $request['user_id'];

            foreach ($data['recipients'] as $r) {

                if ($r['user_id'] == $loggedInUserId) {
                    continue;
                }

                $createdAlertInstanceId = AlertInstance::create([
                    'alert_id' => $createdAlertId,
                    'recipient_user_id' => $r['user_id']
                ])->id;

                AlertInstanceStatusLog::create([
                    'alert_instance_id' => $createdAlertInstanceId,
                    'alert_status_id' => $dropDownAlertStatusNew['id']
                ]);
            }

            foreach ($data['candidate_recipients'] as $r) {
                if ($r['is_selected']) {

                    if ($r['user_id'] == $loggedInUserId) {
                        continue;
                    }

                    $createdAlertInstanceId = AlertInstance::create([
                        'alert_id' => $createdAlertId,
                        'recipient_user_id' => $r['user_id']
                    ])->id;

                    AlertInstanceStatusLog::create([
                        'alert_instance_id' => $createdAlertInstanceId,
                        'alert_status_id' => $dropDownAlertStatusNew['id']
                    ]);
                }
            }

            // === Dealing with remark

            $data['alert_subcategory_id'] = $data['alert_subcategory']['id'];
            $createdRemarkId = Remark::create(array_only($data, Remark::$fields))->id;
            RemarkMessage::create([
                'remark_id' => $createdRemarkId,
                'alert_id' => $createdAlertId,
                'contents' => $data['message']['contents'],
                'creator_user_id' => $data['message']['creator_user_id']
            ]);
            foreach ($data['recipients'] as $r) {
                UserRemarksStatusLog::create([
                    'user_id' => $r['user_id'],
                    'remark_id' => $createdRemarkId,
                    'remark_status_id' => $dropDownRemarkStatusNew['id'],
                ]);
            }

            foreach ($data['candidate_recipients'] as $r) {
                if ($r['is_selected']) {
                    UserRemarksStatusLog::create([
                        'user_id' => $r['user_id'],
                        'remark_id' => $createdRemarkId,
                        'remark_status_id' => $dropDownRemarkStatusNew['id'],
                    ]);
                }
            }

            $projectId = Contract::find($data['contract_id'])->project_id;
            $request = Http::post(
                config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info',
                [],
                [
                    'ids' => [$projectId]
                ]
            );

            if ($request->status_code > 400) {
                throw new Exception($request->body);
            }

            $projectInfo = json_decode($request->body, true)[0];

            DB::commit();
            return response()->json([
                'contract_name_da' => $projectInfo['project_name_da']
            ], 201, [
                'location' => $createdRemarkId
            ]);

        } catch (Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function reply(Request $request)
    {
        try {
            $dropDownAlertStatusNew = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/alertStatus',
                'new'
            );
            DB::beginTransaction();
            $data = $request->all();
            $remarkToReply = Remark::where('id', $data['id'])->first();

            // === Dealing with alert

            $createdAlertId = Alert::create([
                'contents' => 'یک پاسخ جدید برای ملاحظه با عنوان "' . $remarkToReply->title . '" درج گردید'
            ])->id;

//            $loggedInUserId = auth('api')->user()->id; //todo remove this line
            $loggedInUserId = $request['user_id'];

            foreach ($data['recipients'] as $r) {

                if ($r['user_id'] == $loggedInUserId) {
                    continue;
                }

                $createdAlertInstanceId = AlertInstance::create([
                    'alert_id' => $createdAlertId,
                    'recipient_user_id' => $r['user_id']
                ])->id;

                AlertInstanceStatusLog::create([
                    'alert_instance_id' => $createdAlertInstanceId,
                    'alert_status_id' => $dropDownAlertStatusNew['id']
                ]);
            }
            if ($data['candidate_recipients'] === '[]' || $data['candidate_recipients'] === null) {
                $data['candidate_recipients'] = [];
            }
            foreach ($data['candidate_recipients'] as $r) {
                if ($r['is_selected']) {

                    if ($r['user_id'] == $loggedInUserId) {
                        continue;
                    }

                    $createdAlertInstanceId = AlertInstance::create([
                        'alert_id' => $createdAlertId,
                        'recipient_user_id' => $r['user_id']
                    ])->id;

                    AlertInstanceStatusLog::create([
                        'alert_instance_id' => $createdAlertInstanceId,
                        'alert_status_id' => $dropDownAlertStatusNew['id']
                    ]);
                }
            }

            // === Dealing with remark

            $data['alert_subcategory_id'] = $data['alert_subcategory']['id'];
            RemarkMessage::create([
                'remark_id' => $remarkToReply->id,
                'alert_id' => $createdAlertId,
                'contents' => $data['message']['contents'],
                'creator_user_id' => $data['message']['creator_user_id']
            ]);

            DB::commit();
            return response()->json([], 204);

        } catch (Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * @param Request $request
     * @param $remarkId
     * @return \Illuminate\Http\JsonResponse
     */
    public function archive(Request $request, $remarkId)
    {
        try {

            $archivedStatus = DropDowns::getBySlug(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/alertsAndRemarks/remarkStatus',
                'archived'
            );
            UserRemarksStatusLog::insert([
//                'user_id' => auth('api')->user()->id, // todo remove this
                'user_id' => $request['user_id'],
                'remark_id' => $remarkId,
                'remark_status_id' => $archivedStatus['id'],
            ]);
            return response()->json([], 204);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

}
