<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContractProgressPaymentsLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_progress_payments_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('status_id');
            $table->integer('amount');
            $table->integer('physical_progress_percentage');
            $table->text('major_activities');
            $table->text('remarks');
            $table->unsignedInteger('month_id');
            $table->integer('year');
            $table->unsignedInteger('amendment_id');
            $table->foreign('amendment_id')
                ->references('id')
                ->on('amendments')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_progress_payments_logs');
    }
}
