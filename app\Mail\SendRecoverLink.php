<?php

namespace NPA\ACPMS\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendRecoverLink extends Mailable
{
    use Queueable, SerializesModels;
    public $recoverPasswordFormLink;

    /**
     * Create a new message instance.
     *
     * @param $link
     */
    public function __construct($link)
    {
        $this->recoverPasswordFormLink = $link;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.recover-password-form')
            ->subject(Config('custom.MAIL_SUBJECT'));
    }
}
