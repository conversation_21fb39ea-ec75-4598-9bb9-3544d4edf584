<?php

namespace NPA\ACPMS\Console\Commands;

use Illuminate\Console\Command;
use NPA\ACPMS\Types\SyncDataWithGoogleSheet;

class SyncWithGoogleSheetCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ageops:sync-data-with-google-sheet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sync data with google sheet';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SyncDataWithGoogleSheet::prepareData();
    }
}
