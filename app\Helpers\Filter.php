<?php

namespace NPA\ACPMS\Helpers;

use Illuminate\Support\Facades\DB;
use <PERSON>rilog\Jalali\jDateTime;

class Filter
{
    static function prepareQueryStrings($queryArray)
    {
        $startYearArray = isset($queryArray['fiscal_year_start']) ? JDateTime::toGregorian($queryArray['fiscal_year_start'] - 1, 10, 1) : null;
        $endYearArray = isset($queryArray['fiscal_year_end']) ? JDateTime::toGregorian($queryArray['fiscal_year_end'], 9, 30) : null;
        $fiscal_start_year = $startYearArray ? $startYearArray[0] . '-' . $startYearArray[1] . '-' . $startYearArray[2] : null;
        $fiscal_end_year = $endYearArray ? $endYearArray[0] . '-' . $endYearArray[1] . '-' . $endYearArray[2] : null;
        $data['joins'] = '';
        $data['query_string'] = '';
        $costAmendmentPercentage = false;
        $timeAmendmentPercentage = false;
        $contractTotalValue = false;
        $actualPaymentsValue = false;
        $actualPaymentsPhysicalPercentage = false;
        $fiscalYear = false;
        $locationJoinCondition = true;
        foreach ($queryArray as $key => $value) {
            switch ($key) {
                case 'procurement_type':
                    $data['query_string'] .= ' and c.procurement_type_id = ' . $value;
                    break;
                case 'procurement_method':
                    $data['query_string'] .= ' and c.procurement_method_id = ' . $value;
                    break;
                case 'above_threshold':
                    $data['query_string'] .= ' and c.is_above_threshold = 1 ';
                    break;
                case 'below_threshold':
                    $data['query_string'] .= ' and c.is_above_threshold = 0 ';
                    break;
                case 'amendment_type':
                    $data['joins'] .= ' join amendments as qaj on qaj.contract_id = c.id and qaj.is_approved = true';
                    $data['query_string'] .= ' and qaj.type_id = ' . $value;
                    break;
                case (preg_match('/cost_amendment_percentage.*/', $key) ? true : false) :
                    if (!$costAmendmentPercentage) {
                        $costAmendmentPercentage = true;
                        $condition = isset($queryArray['cost_amendment_percentage_start']) && isset($queryArray['cost_amendment_percentage_end']) ?
                            ' between ' . $queryArray['cost_amendment_percentage_start'] . ' and ' . $queryArray['cost_amendment_percentage_end'] : (
                            isset($queryArray['cost_amendment_percentage_start']) ? ' >= ' . $queryArray['cost_amendment_percentage_start'] :
                                ' <= ' . $queryArray['cost_amendment_percentage_end']);

                        $data['query_string'] .= ' and c.id in 
                                    (
                                        select q_inner_c.id 
                                        from contracts as q_inner_c
                                        join contract_details as q_inner_cd 
                                        on q_inner_cd.contract_id = q_inner_c.id
                                        join contract_details_verifications as q_inner_cdv 
                                        on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                        and q_inner_cdv.is_approved = true
                                        where ((ifnull((
                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                from amendments as q_inner_a
                                                left join cost_amendments as q_inner_ca 
                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                left join time_and_cost_amendments as q_inner_tca
                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true
                                            ) * ifnull(q_inner_c.exchange_rate,1), 0) * 100) / 
                                            (ifnull(q_inner_cd.actual_value, 0) * ifnull(q_inner_c.exchange_rate,1)))  ' . $condition . ' 
                                
                                     ) ';
                    }
                    break;
                case (preg_match('/time_amendment_percentage.*/', $key) ? true : false):

                    if (!$timeAmendmentPercentage) {
                        $timeAmendmentPercentage = true;
                        $condition = isset($queryArray['time_amendment_percentage_start']) && isset($queryArray['time_amendment_percentage_end']) ?
                            ' between ' . $queryArray['time_amendment_percentage_start'] . ' and ' . $queryArray['time_amendment_percentage_end'] : (
                            isset($queryArray['time_amendment_percentage_start']) ? ' >= ' . $queryArray['time_amendment_percentage_start'] :
                                ' <= ' . $queryArray['time_amendment_percentage_end']);
                        $data['query_string'] .= ' and c.id in (
                                        select   q_inner_c.id
                                        from contracts as q_inner_c
                                        join contract_details as q_inner_cd
                                        on q_inner_cd.contract_id = q_inner_c.id
                                        join contract_details_verifications as q_inner_cdv
                                        on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                        and q_inner_cdv.is_approved = true
                                        join contract_progresses as q_inner_cp
                                        on q_inner_cp.contract_id = q_inner_c.id
                                        join contract_progress_verifications as q_inner_cpv
                                        on q_inner_cpv.contract_progress_id = q_inner_cp.id
                                        and q_inner_cpv.is_approved = true
                                        where (
                                            ifnull(TIMESTAMPDIFF(MINUTE,q_inner_cd.planned_end_date,
                                                        (select if( max(q_inner_ta.amended_end_date) > max(q_inner_tca.amended_end_date),
                                                        max(q_inner_ta.amended_end_date) ,
                                                        max(q_inner_tca.amended_end_date)) as maxDate
                                                        from amendments as q_inner_a
                                                        left join time_amendments as q_inner_ta
                                                            on q_inner_ta.amendment_id = q_inner_a.id
                                                        left join time_and_cost_amendments as q_inner_tca
                                                            on q_inner_tca.amendment_id = q_inner_a.id
                                                        where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true
                                                        )
                                                ), 0) * 100 /  ifnull(TIMESTAMPDIFF(MINUTE,q_inner_cp.actual_start_date, q_inner_cd.planned_end_date), 1))   ' . $condition . '
                                ) ';
                    }
                    break;
                case 'donor':
                    $data['joins'] .= ' join contract_donors as qcdj on qcdj.contract_id = c.id ';
                    $data['query_string'] .= ' and qcdj.donor_id = ' . $value;
                    break;
                case 'selection_method':
                    $data['joins'] .= ' join consultancy_contracts_selection_methods as qccsmj on qccsmj.contract_id = c.id ';
                    $data['query_string'] .= ' and qccsmj.selection_method_id = ' . $value;
                    break;
                case 'zone':
                case 'province':
                case 'district':
                    if ($locationJoinCondition) {
                        $data['joins'] .= ' join domestic_contract_execution_locations as qdcelj
                                        on qdcelj.contract_id = c.id
                                    join temp_districts as qtdj
                                        on qtdj.id = qdcelj.district_id
                                    join temp_provinces as qtpj
                                        on qtpj.id = qtdj.temp_province_id
                                    join temp_zones as qtzj
                                        on qtzj.id = qtpj.temp_zone_id ';
                    }
                    $locationJoinCondition = false;
                    if ($key === 'zone') {
                        $data['query_string'] .= ' and qtzj.id = ' . $value;
                    }
                    if ($key === 'province') {
                        $data['query_string'] .= ' and qtpj.id = ' . $value;
                    }
                    if ($key === 'district') {
                        $data['query_string'] .= ' and qtdj.id = ' . $value;
                    }
                    break;
                case 'contract_status':
                    $data['joins'] .= ' join contract_statuses as qcsj
                                    on qcsj.contract_id = c.id
                                join contract_status_verifications as qcsvj
                                    on qcsvj.contract_status_id = qcsj.id and qcsvj.is_approved = true ';

                    $data['query_string'] .= ' and qcsj.status_id  = ' . $value;
                    break;
                case (preg_match('/contract_total_value.*/', $key) ? true : false):

                    if (!$contractTotalValue) {
                        $contractTotalValue = true;
                        $condition = isset($queryArray['contract_total_value_start']) && isset($queryArray['contract_total_value_end']) ?
                            ' between ' . $queryArray['contract_total_value_start'] . ' and ' . $queryArray['contract_total_value_end'] : (
                            isset($queryArray['contract_total_value_start']) ? ' >= ' . $queryArray['contract_total_value_start'] :
                                ' <= ' . $queryArray['contract_total_value_end']);


                        $data['query_string'] .= ' and c.id in
                                            (select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            join contract_details as q_inner_cd
                                                on q_inner_cd.contract_id = q_inner_c.id
                                            join contract_details_verifications as q_inner_cdv
                                                on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                            where (
                                                    (q_inner_cd.actual_value * ifnull( c.exchange_rate, 1) ) +
                                                    (ifnull((
                                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                                from amendments as q_inner_a
                                                                left join cost_amendments as q_inner_ca
                                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                                left join time_and_cost_amendments as q_inner_tca
                                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                                where q_inner_a.contract_id = q_inner_c.id
                                                            ), 0) * ifnull( c.exchange_rate, 1))

                                                    )  ' . $condition . '
                                            ) ';
                    }
                    break;
                case (preg_match('/actual_payments_value.*/', $key) ? true : false):

                    if (!$actualPaymentsValue) {
                        $actualPaymentsValue = true;
                        $condition = isset($queryArray['actual_payments_value_start']) && isset($queryArray['actual_payments_value_end']) ?
                            ' between ' . $queryArray['actual_payments_value_start'] . ' and ' . $queryArray['actual_payments_value_end'] : (
                            isset($queryArray['actual_payments_value_start']) ? ' >= ' . $queryArray['actual_payments_value_start'] :
                                ' <= ' . $queryArray['actual_payments_value_end']);

                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (select ifnull(sum(q_cpp_inner.amount), 0) * ifnull(q_inner_c.exchange_rate, 1)
                                        from contract_progress_payments as q_cpp_inner
                                        where q_cpp_inner.contract_id = q_inner_c.id
                                        and q_cpp_inner.is_approved = true)  ' . $condition . '

                                    ) ';
                    }
                    break;
                case (preg_match('/actual_payments_physical_percentage.*/', $key) ? true : false):
                    if (!$actualPaymentsPhysicalPercentage) {
                        $actualPaymentsPhysicalPercentage = true;
                        $condition = isset($queryArray['actual_payments_physical_percentage_start']) && isset($queryArray['actual_payments_physical_percentage_end']) ?
                            ' between ' . $queryArray['actual_payments_physical_percentage_start'] . ' and ' . $queryArray['actual_payments_physical_percentage_end'] : (
                            isset($queryArray['actual_payments_physical_percentage_start']) ? ' >= ' . $queryArray['actual_payments_physical_percentage_start'] :
                                ' <= ' . $queryArray['actual_payments_physical_percentage_end']);

                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (select ifnull(sum(q_cpp_inner.physical_progress_percentage), 0)
                                        from contract_progress_payments as q_cpp_inner
                                        where q_cpp_inner.contract_id = q_inner_c.id
                                        and q_cpp_inner.is_approved = true) ' . $condition . '

                                    ) ';
                    }
                    break;
                case (preg_match('/fiscal_year.*/', $key) ? true : false):
                    if (!$fiscalYear) {
                        $cancelledStatusId = 2;
                        $closeOutStatusId = 4;
                        $fiscalYear = true;
                        $condition = isset($queryArray['fiscal_year_start']) && isset($queryArray['fiscal_year_end']) ?
                            ' between ' . $fiscal_start_year . ' and ' . $fiscal_end_year : (
                            isset($queryArray['fiscal_year_start']) ? ' >= ' . $fiscal_start_year :
                                ' <= ' . $fiscal_end_year);
                        $data['query_string'] .= '   and c.id in (
                                        select
                                            q_inner_c.id as contract_id
                                        from contracts as q_inner_c
                                        join contract_progresses as q_inner_cp
                                            on q_inner_cp.contract_id = q_inner_c.id
                                        join contract_progress_verifications as q_inner_cpv
                                            on q_inner_cpv.contract_progress_id = q_inner_cp.id
                                            and q_inner_cpv.is_approved = true
                                        join contract_details as q_inner_cd
                                            on q_inner_cd.contract_id = q_inner_c.id
                                        join contract_details_verifications as q_inner_cdv
                                            on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                            and q_inner_cdv.is_approved = true
                                        join contract_statuses as q_inner_cs
                                            on q_inner_cs.contract_id = q_inner_c.id
                                        join contract_status_verifications as q_inner_csv
                                            on q_inner_csv.contract_status_id = q_inner_cs.id
                                        where q_inner_cp.actual_start_date ' . $condition . ' and
                                        (q_inner_cp.actual_start_date <= "'. $fiscal_start_year .'" and q_inner_cs.status_id not in ('.$cancelledStatusId.', '.$closeOutStatusId.'))
                                            or if(q_inner_cd.planned_end_date > ifnull(
                                                    (select if( max(q_inner_ta.amended_end_date) > max(q_inner_tca.amended_end_date),
                                                            max(q_inner_ta.amended_end_date) ,
                                                            max(q_inner_tca.amended_end_date)
                                                        ) as maxDate
                                                    from amendments as q_inner_a
                                                    left join time_amendments as q_inner_ta
                                                        on q_inner_ta.amendment_id = q_inner_a.id
                                                    left join time_and_cost_amendments as q_inner_tca
                                                        on q_inner_tca.amendment_id = q_inner_a.id
                                                    where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true), 0) ,

                                                q_inner_cd.planned_end_date,

                                                (select if( max(q_inner_ta.amended_end_date) > max(q_inner_tca.amended_end_date),
                                                        max(q_inner_ta.amended_end_date) ,
                                                        max(q_inner_tca.amended_end_date)
                                                    ) as maxDate
                                                from amendments as q_inner_a
                                                left join time_amendments as q_inner_ta
                                                    on q_inner_ta.amendment_id = q_inner_a.id
                                                left join time_and_cost_amendments as q_inner_tca
                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                where q_inner_a.contract_id = q_inner_c.id and q_inner_a.is_approved = true)
                                                ) ' . $condition . '
                                        )  ';
                    }
                    break;
                case 'contract_manager_id':
                    $data['query_string'] .= ' and c.contract_manager_id = ' . $value . ' ';
                    break;
                case 'specialist_id':
                    $data['query_string'] .= ' and c.specialist_id  = ' . $value . ' ';
                    break;
                case 'award_authority_id':
                    $procurementEntityId = DB::select('
                            select
                              ua.procurement_entity_id as id
                            from user_award_authorities as ua
                            where ua.user_id = ' . $queryArray['award_authority_id'] . '
                    ');
                    if (count($procurementEntityId)) {
                        $procurementEntityId = $procurementEntityId[0]->id;
                        $data['query_string'] .= ' and c.procurement_entity_id  = ' . $procurementEntityId . ' ';
                    } else {
                        $data['query_string'] .= ' and 1 = 2 ';
                    }
                    break;
                case 'cpm_manager_id':
                    $sectorIds = DB::select('
                            select
                              uc.sector_id as id
                            from user_cpm_managers as uc
                            where uc.user_id = ' . $queryArray['cpm_manager_id'] . '
                    ');
                    if (count($sectorIds) !== 0) {
                        $temp = '';
                        $index = 1;
                        foreach ($sectorIds as $sectorId) {
                            $temp .= $sectorId->id;
                            if (count($sectorIds) !== $index) {
                                $temp .= ', ';
                            }
                            $index++;
                        }
                        $data['query_string'] .= ' and c.sector_id  in ( ' . $temp . ' ) ';
                    } else {
                        $data['query_string'] .= ' and 1 = 2 ';
                    }
                    break;
                case 'sector_id':
                    $data['query_string'] .= ' and c.sector_id  = ' . $value . ' ';
                    break;
                case 'procurement_entity_id':
                    $data['query_string'] .= ' and c.procurement_entity_id  = ' . $value . ' ';
                    break;
            }
        }

        return $data;
    }


}