<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\ContractProgressPayment;

class ContractController extends Controller
{
    private $contractStatuses = null;

    public function index(Request $request)
    {
        $queryParams = $request->all();
        $queryString = $this->prepareQueryStrings($queryParams);

        $pagination = '';
        if (isset($queryParams['page_size']) && isset($queryParams['page_index'])) {
            $limit = $queryParams['page_size'];
            $offset = $queryParams['page_index'] * $limit;
            $pagination = ' limit  ' . $limit . ' offset ' . $offset;
        }
        $data['contracts'] = DB::select('
                select distinct
                    SQL_CALC_FOUND_ROWS 
                    c.id,
                    c.npa_identification_number,
                    c.project_id,
                    ct.tag_id,
                    (
                      ifnull(cd.actual_value, 0) + 
                      ifnull(psc.provisional_sum_and_contingency, 0) + 
                      ifnull(amendment.amount, 0)
                    ) * 
                    if(c.exchange_rate, c.exchange_rate, 1) as actual_value_afg,
                    ifnull(cp.actual_start_date, cd.planned_start_date) as start_date,
                    
                    if( cs_last_log.status_id = 3,
                        cs_last_log.date,
                        ifnull((select if( ifnull(max(ta.amended_end_date), 0) > ifnull(max(tca.amended_end_date), 0), max(ta.amended_end_date) , max(tca.amended_end_date)) as maxdate
                                from amendments as a
                                left join time_amendments as ta 
                                    on ta.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id and a.is_approved = true
                        ), cd.planned_end_date)
                        ) as end_date,
                    cs_last_log.status_id
                    
                from contracts as c
                left join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications as cdv
                          on cdv.contract_detail_id = cd.id 
                left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id	 
                left join contract_progresses as cp
                    on c.id = cp.contract_id 
                left join contract_tags as ct
                        on ct.contract_id = c.id
                left join (
                    select 
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca 
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $queryString['joins'] . '
                left join (
                        select 
                            csl.contract_id,
                            inner_csl.date,
                            inner_csl.status_id
                        from contract_status_logs as csl
                        join(select id, status_id, date from contract_status_logs) as inner_csl 
                            on  inner_csl.id = csl.id
                        where csl.id = (
                            select 
                            max(outer_csl.id) 
                            from contract_status_logs as outer_csl 
                            where csl.contract_id = outer_csl.contract_id)
                        group by csl.contract_id
                ) as cs_last_log on (c.id = cs_last_log.contract_id)
                
                where c.is_published = true and cdv.is_approved=true
                ' . $queryString['query_string'] . '
                ' . $pagination . ';


        ');
        $data['count'] = DB::select('SELECT FOUND_ROWS() as count;')[0]->count;


        foreach ($data['contracts'] as &$contract) {
            $contract->companies = DB::select('
            select license_number 
            from companies 
            where company_general_information_id = ( select id from company_general_informations where contract_id = ' . $contract->id . ');
        ');
        }
        return response()->json($data, 200);
    }

    public function show($id)
    {
        $id = urldecode($id);
        $data = DB::select('
            select 
                c.id,
                c.contract_number as npa_identification_number,
                c.project_id,
                cd.agreement_signature_date as signed_date,
                c.currency_id,
                ifnull(cp.actual_start_date, cd.planned_start_date) as start_date,
                cs.status_id,
                (
                  ifnull(cd.actual_value, 0) + 
                  ifnull(psc.provisional_sum_and_contingency, 0) + 
                  ifnull(amendment.amount, 0)
                ) * 
                if(c.exchange_rate, c.exchange_rate, 1) as value,
                if( cs.status_id = 3,
                    cs.date,
                    ifnull((select if( ifnull(max(ta.amended_end_date), 0) > ifnull(max(tca.amended_end_date), 0), max(ta.amended_end_date) , max(tca.amended_end_date)) as maxdate
                            from amendments as a
                            left join time_amendments as ta 
                                on ta.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            where a.contract_id = c.id and a.is_approved = true
                    ), cd.planned_end_date)
                ) as end_date
                
            from contracts as c
            join contract_details as cd
              on c.id = cd.contract_id
            left join contract_progresses as cp
              on c.id = cp.contract_id
            left join (
                select 
                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                    a.contract_id as contract_id
                from amendments as a
                left join cost_amendments as ca 
                    on ca.amendment_id = a.id
                left join time_and_cost_amendments as tca
                    on tca.amendment_id = a.id
                where a.is_approved = true
                group by  a.contract_id 
            
            ) as amendment
                on c.id = amendment.contract_id
            join (
                  select
                        csl.id, csl.contract_id, csl.status_id, csl.date
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = c.id
            left join provisional_sum_and_contingencies as psc
                on psc.contract_detail_id = cd.id	  
            where c.is_published = true and c.npa_identification_number = "' . $id . '"
        ');
        if ($data){
            $data = $data[0];
            $companies = DB::select('
            select license_number 
            from companies 
            where company_general_information_id = ( select id from company_general_informations where contract_id = ' . $data->id . ');
        ');
            $donors = DB::select('
            select donor_id 
            from contract_donors 
            where contract_id = ' . $data->id . ';
        ');

            $data->companies = $companies;
            $data->donors = $donors;
        }
        return response()->json($data, 200);


    }

    public function schedulePerformingIndex(Request $request)
    {
        try {

            $contractId = $request->query('contract_id');
            $data['plan'] = DB::select('
                select
                    cpf.month_id,
                    cpf.year,
                    ifnull(cpp.physical_progress_percentage, 0) as ev,
                    ifnull(cpf.physical_progress_percentage, 0) as pv
                from contract_progress_payments as cpp
                join contract_planning_finance_affairs_and_physical_progresses as cpf
                    on cpf.contract_id = cpp.contract_id
                    and cpf.year = cpp.year
                    and cpf.month_id = cpp.month_id
                join contract_planning_f_a_a_p_p_verifications cpfv
                    on cpfv.contract_id = cpf.contract_id
                    and cpfv.is_approved = true
                where  cpp.contract_id = ' . $contractId . '  
                order by cpp.year, cpp.month_id 
            
            ');
            $data['re_plan'] = DB::select('
                select 
                    cpp.year,
                    cpp.month_id,
                    ifnull(crp.physical_progress_percentage, 0) as pv,
                    ifnull(cpp.physical_progress_percentage, 0) as ev,
                    cr.iteration_count
            from contract_progress_payments as cpp
            join contract_re_planning_date_f_a_a_p_ps as cr
                on cpp.contract_id = cr.contract_id
            join contracts as c
                on cr.contract_id = c.id
            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
                on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id and 
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year 
            where cpp.is_approved = true and cr.contract_id = ' . $contractId . '
            order by cr.iteration_count, cpp.year, cpp.month_id   
                    
            ');

            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }


    }

    public function costPerformingIndex(Request $request)
    {
        try {
            $contractId = $request->query('contract_id');
            $data = DB::select('
            select 
                cpp.month_id,
                cpp.year,
                ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) as amount,
                cpp.physical_progress_percentage,
                (
                    select 
                        (ifnull(cd.actual_value, 0) +  ifnull(amendment.amount, 0) + ifnull(psc.provisional_sum_and_contingency, 0)) * if(c.exchange_rate, c.exchange_rate, 1) as total_contract_value 
                    from contract_details as cd
                    join contract_details_verifications as cdv
                        on cd.id = cdv.contract_detail_id
                        and cdv.is_approved = true
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                    
                    ) as amendment
                          on cd.contract_id = amendment.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    where cd.contract_id = c.id
                ) as total_amount
            from contracts as c
            join contract_progress_payments as cpp
                on cpp.contract_id = c.id and
                cpp.is_approved = true
            where c.id = ' . $contractId . '
            order by cpp.year, cpp.month_id
            ');
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function contractPerformancePayments(Request $request)
    {
        $contract_id = $request->query('contract_id');
        $plan['payments'] = DB::select('
            select 
                cp.year,
                cp.month_id,
                ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)  as planned_amount,
                ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) as actual_amount
                
            from contract_planning_finance_affairs_and_physical_progresses as cp
            left join contract_progress_payments as cpp
                on  cpp.contract_id = cp.contract_id and 
                    cpp.month_id = cp.month_id and 
                    cpp.year = cp.year and 
                    cpp.is_approved = true	
            join contract_planning_f_a_a_p_p_verifications as cpv
                on cpv.contract_id = cp.contract_id
                and  cpv.is_approved = true 
            join (
                select 
                    csl.id, csl.contract_id, csl.status_id 
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                    on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cp.contract_id
            join contracts as c
                on cp.contract_id = c.id
            where c.id = ' . $contract_id . '
            order by cp.year, cp.month_id
        
        ');

        $rePlan['payments'] = DB::select('
            select 
                    cpp.year,
                    cpp.month_id,
                    ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)  as planned_amount,
                    ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) as actual_amount,
                    cr.iteration_count
            from contract_progress_payments as cpp
            join contract_re_planning_date_f_a_a_p_ps as cr
                on cpp.contract_id = cr.contract_id
            join contracts as c
                on cr.contract_id = c.id
            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
                on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id and 
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year 
            where cpp.is_approved = true and cr.contract_id = ' . $contract_id . '
            order by cpp.year, cpp.month_id
        
        ');

        return response()->json(['plan' => $plan, 're_plan' => $rePlan], 200);


    }

    public function physicalProgressPercentage(Request $request)
    {
        try {
            $plans = [];
            $contract_id = $request->query('contract_id');


            $planPayments['payments'] = ContractPlanningFinanceAffairsAndPhysicalProgress::
            select('physical_progress_percentage', 'year', 'month_id')
                ->where('contract_id', $contract_id)
                ->orderBy('id', 'asc')
                ->get();
            $planPayments['iteration_count'] = null;
            $planPayments['is_rePlan'] = false;
            array_push($plans, $planPayments);

            $actualProgresses = ContractProgressPayment::
            select('physical_progress_percentage', 'year', 'month_id')
                ->where('contract_id', $contract_id)
                ->orderBy('id', 'asc')
                ->get();

            foreach ($plans as &$plan) {
                foreach ($plan['payments'] as &$planPayment) {
                    foreach ($actualProgresses as $actualProgress) {
                        if ($planPayment['year'] == $actualProgress['year'] && $planPayment['month_id'] == $actualProgress['month_id']) {
                            $planPayment['planned_physical_progress_percentage'] = $planPayment['physical_progress_percentage'];
                            $planPayment['actual_physical_progress_percentage'] = $actualProgress['physical_progress_percentage'];
                            unset($planPayment['physical_progress_percentage']);

                        }
                    }
                    if (isset($planPayment['physical_progress_percentage'])) {
                        $planPayment['planned_physical_progress_percentage'] = $planPayment['physical_progress_percentage'];
                        $planPayment['actual_physical_progress_percentage'] = 0;
                        unset($planPayment['physical_progress_percentage']);
                    }
                }
            }
            if (isset($plans[0])) {
                $plans = $plans[0];
            }
            $rePlan['payments'] = DB::select('
            select 
                    cpp.year,
                    cpp.month_id,
                    ifnull(crp.physical_progress_percentage, 0) as planned_physical_progress_percentage,
                    ifnull(cpp.physical_progress_percentage, 0) as actual_physical_progress_percentage,
                    cr.iteration_count
            from contract_progress_payments as cpp
            join contract_re_planning_date_f_a_a_p_ps as cr
                on cpp.contract_id = cr.contract_id
            join contracts as c
                on cr.contract_id = c.id
            left join contract_re_planning_finance_affairs_and_physical_progresses as crp
                on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id and 
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year 
            where cpp.is_approved = true and cr.contract_id = ' . $contract_id . '
            order by cpp.year, cpp.month_id
        
        ');
            return response()->json(['plan' => $plans, 're_plan' => $rePlan], 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function approvedContracts(Request $request)
    {
        try {
            $startYear = $request->query('start_year');
            $endYear = $request->query('end_year');
            if (!$endYear) {
                $now = Carbon::now();

                $nowInJalali = jDateTime::toJalali($now->year, $now->month, $now->day);
                $endDateOFCurrentFinanceYear = jDateTime::toJalali($now->year - 1, 12, 21);
                $endYear = $nowInJalali[0];
                if ($nowInJalali[0] === $endDateOFCurrentFinanceYear[0] && $nowInJalali[1] > $endDateOFCurrentFinanceYear[1]) {
                    $endYear = $nowInJalali[0] + 1;
                }
            }

            $contractsPerYear = [];
            for ($year = $startYear; $year <= $endYear; $year++) {

                $financeStartDate = implode('-', jDateTime::toGregorian($year - 1, 10, 1));
                $financeEndDate = implode('-', jDateTime::toGregorian($year, 9, 30));

                $numberOfContracts = Contract::select('npa_identification_number', 'currency_id', 'id')
                    ->where('is_published', 1)
                    ->whereHas('contract_approval', function ($query) use ($financeStartDate, $financeEndDate) {
                        $query->whereBetween('award_date', [$financeStartDate, $financeEndDate]);
                    })->count();

                array_push($contractsPerYear, ['year' => $year, 'count' => $numberOfContracts]);

            }

            return response()->json($contractsPerYear, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function signedContracts(Request $request)
    {

        try {
            $startYear = $request->query('start_year') ? $request->query('start_year') : 1385;
            $endYear = $request->query('end_year');


            $now = Carbon::now();
            $nowInJalali = JDateTime::toJalali($now->year, $now->month, $now->day);
            if (!$endYear) {
                $nowInJalali[1] > 9 ?
                    $endYear = $nowInJalali[0] + 1 :
                    $endYear = $nowInJalali[0];
            }

            $contractsPerYear = [];
            for ($year = $startYear; $year <= $endYear; $year++) {
                $financeStartDate = implode('-', jDateTime::toGregorian($year - 1, 10, 1));
                $financeEndDate = implode('-', jDateTime::toGregorian($year, 9, 30));
                $numberOfContracts = DB::select('
                                select 
                                    count(case when c.is_above_threshold = true then 1 end) as awarded_above_threshold_count,
                                    count(case when c.is_above_threshold = false then 1 end) as awarded_below_threshold_count,
                                    count(case when c.is_above_threshold = true and c.is_published = true then 1 end) as published_above_threshold_count,
                                    count(case when c.is_above_threshold = false and c.is_published = true then 1 end) as published_below_threshold_count,
                                    count(case when c.is_published = true then 1 end) as published_count ,
                                    count(c.id) as award_count
                                from contracts as c
                                join contract_details as cd
                                    on cd.contract_id = c.id
                                join contract_details_verifications as cdv
                                    on cdv.contract_detail_id = cd.id
                                where cd.agreement_signature_date between  "' . $financeStartDate . '" and "' . $financeEndDate . '"
                            ')[0];
                array_push($contractsPerYear,
                    [
                        'year' => $year,
                        'award_count' => $numberOfContracts->award_count,
                        'published_count' => $numberOfContracts->published_count,
                        'published_above_threshold_count' => $numberOfContracts->published_above_threshold_count,
                        'published_below_threshold_count' => $numberOfContracts->published_below_threshold_count,
                        'awarded_above_threshold_count' => $numberOfContracts->awarded_above_threshold_count,
                        'awarded_below_threshold_count' => $numberOfContracts->awarded_below_threshold_count,
                    ]);
            }

            return response()->json($contractsPerYear, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function numberOfSignedContracts()
    {
        try {
            $data = DB::select('
                  select count(c.id) as counts
                  from contracts as c 
                  where c.is_published = true
            ')[0];
            return response()->json($data->counts, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function downloadAttachment($id)
    {
        $attachment = Attachment::find($id);
        try {
            return response()->json(config('custom.AWS_S3_BASE_URL') . $attachment['stored_path']);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function prepareQueryStrings($queryArray)
    {
        if (!$this->contractStatuses) {
            $this->contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        }
        $workInProgressStatus = DropDowns::getElementTypeBySlug($this->contractStatuses, 'work-in-process');
        $contractCompletionDefectLiabilityPeriodStatus = DropDowns::getElementTypeBySlug($this->contractStatuses, 'contract-completion-defect-liability-period');
        $suspendedStatus = DropDowns::getElementTypeBySlug($this->contractStatuses, 'suspended');

        $startYearArray = isset($queryArray['start_year']) ? JDateTime::toGregorian($queryArray['start_year'] - 1, 10, 1) : null;
        $endYearArray = isset($queryArray['end_year']) ? JDateTime::toGregorian($queryArray['end_year'], 9, 30) : null;
        $fiscal_start_year = $startYearArray ? $startYearArray[0] . '-' . $startYearArray[1] . '-' . $startYearArray[2] : null;
        $fiscal_end_year = $endYearArray ? ($endYearArray[0]) . '-' . $endYearArray[1] . '-' . $endYearArray[2] : null;
        $data['joins'] = '';
        $data['query_string'] = '';
        $contractTotalValue = false;
        $actualPaymentsValue = false;
        $actualPaymentsPhysicalPercentage = false;
        $fiscalYear = false;
        unset($queryArray['page_size']);
        unset($queryArray['page_index']);
        unset($queryArray['lang']);
        foreach ($queryArray as $key => $value) {
            switch ($key) {
                case 'npa_identification_number':
                    $data['query_string'] .= ' and c.npa_identification_number = \'' . $value . '\'';
                    break;
                case 'contract_number':
                    $data['query_string'] .= ' and c.contract_number like \'%' . $value . '%\'';
                    break;
                case 'procurement_entity_id':
                    $data['query_string'] .= ' and c.procurement_entity_id = ' . $value;
                    break;
                case 'projectIds':
                    $data['query_string'] .= ' and c.project_id in (\'' . $value . '\') ';
                    break;
                case 'procurement_type_id':
                    $data['query_string'] .= ' and c.procurement_type_id = ' . $value;
                    break;
                case 'above_threshold':
                    $data['query_string'] .= ' and c.is_above_threshold = 1 ';
                    break;
                case 'below_threshold':
                    $data['query_string'] .= ' and c.is_above_threshold = 0 ';
                    break;
                case 'donor_id':
                    $data['joins'] .= ' join contract_donors as qcdj on qcdj.contract_id = c.id ';
                    $data['query_string'] .= ' and qcdj.donor_id = ' . $value;
                    break;
                case 'tag_id':
                    $data['joins'] .= ' join contract_tags as ctag on ctag.contract_id = c.id ';
                    $data['query_string'] .= ' and ctag.tag_id= ' . $value;
                    break;
                case 'status_id':
                    $data['joins'] .= ' join contract_status_logs as qcslj
                                    on qcslj.contract_id = c.id';

                    $data['query_string'] .= ' 
                                        and c.id in (select 
                                            qcslj.contract_id
                                        from contract_status_logs as qcslj
                                        where qcslj.id = (
                                            select 
                                            max(inner_csl.id) 
                                            from contract_status_logs as inner_csl 
                                            where qcslj.contract_id = inner_csl.contract_id)
                                            and qcslj.status_id = ' . $value . '
                                        group by qcslj.contract_id)
                                ';
                    break;
                case (preg_match('/total_value.*/', $key) ? true : false):

                    if (!$contractTotalValue) {
                        $contractTotalValue = true;
                        $condition = ' between ' . $queryArray['total_value_from'] * 1000000 . ' and ' . $queryArray['total_value_to'] * 1000000;
                        $data['query_string'] .= ' and c.id in
                                            (select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            join contract_details as q_inner_cd
                                                on q_inner_cd.contract_id = q_inner_c.id
                                            join contract_details_verifications as q_inner_cdv
                                                on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                            where (
                                                    (q_inner_cd.actual_value * ifnull( c.exchange_rate, 1)) +
                                                    (ifnull((
                                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                                from amendments as q_inner_a
                                                                left join cost_amendments as q_inner_ca
                                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                                left join time_and_cost_amendments as q_inner_tca
                                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                                where q_inner_a.contract_id = q_inner_c.id
                                                            ), 0) * ifnull( c.exchange_rate, 1))

                                                    )  ' . $condition . '
                                            ) ';
                    }
                    break;
                case 'licence_number':
                    $data['query_string'] .= ' and c.id in
                                            (
                                            select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            where q_inner_c.id in (
                                                select
                                                cg.contract_id 
                                                from company_general_informations as cg
                                                join companies as com
                                                    on com.company_general_information_id = cg.id
                                                where com.license_number = "' . $value . '"
                                            
                                            )

                                            ) ';

                    break;
                case (preg_match('/actual_payment.*/', $key) ? true : false):

                    if (!$actualPaymentsValue) {
                        $actualPaymentsValue = true;
                        $condition = ' between ' . $queryArray['actual_payment_from'] . ' and ' . $queryArray['actual_payment_to'];
                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (select ifnull(sum(q_cpp_inner.amount), 0) * ifnull(q_inner_c.exchange_rate, 1)
                                        from contract_progress_payments as q_cpp_inner
                                        where q_cpp_inner.contract_id = q_inner_c.id
                                        and q_cpp_inner.is_approved = true)  ' . $condition . '

                                    ) ';
                    }
                    break;
                case (preg_match('/physical_progress.*/', $key) ? true : false):
                    if (!$actualPaymentsPhysicalPercentage) {
                        $actualPaymentsPhysicalPercentage = true;
                        $condition = ' between ' . $queryArray['physical_progress_from'] . ' and ' . $queryArray['physical_progress_to'];

                        $data['query_string'] .= ' and c.id in (
                                    select
                                        q_inner_c.id
                                    from contracts as q_inner_c
                                    where (select ifnull(sum(q_cpp_inner.physical_progress_percentage), 0)
                                        from contract_progress_payments as q_cpp_inner
                                        where q_cpp_inner.contract_id = q_inner_c.id
                                        and q_cpp_inner.is_approved = true) ' . $condition . '

                                    ) ';
                    }
                    break;
                case 'transferred_contracts_only':
                    $startYear = isset($queryArray['start_year']) ? $queryArray['start_year'] : 1394;
                    $endYear = isset($queryArray['end_year']) ? $queryArray['end_year'] : null;


                    $now = Carbon::now();
                    $nowInJalali = JDateTime::toJalali($now->year, $now->month, $now->day);
                    if (!$endYear) {
                        $nowInJalali[1] > 9 ?
                            $endYear = $nowInJalali[0] + 1 :
                            $endYear = $nowInJalali[0];
                    }
                    $data['query_string'] .= ' and (';
                    for ($year = $startYear; $year <= $endYear; $year++) {
                        $financeStartDate = implode('-', jDateTime::toGregorian($year - 1, 10, 1));
                        $financeEndDate = implode('-', jDateTime::toGregorian($year, 9, 30));
                        $data['query_string'] .= '
                              ((ifnull(cp.actual_start_date, cd.planned_start_date) between "' . $financeStartDate . '" and "' . $financeEndDate . '" )
                              and
                              (if( cs_last_log.status_id = 3,
                                    cs_last_log.date,
                                    ifnull((select if( ifnull(max(ta.amended_end_date), 0) > ifnull(max(tca.amended_end_date), 0), max(ta.amended_end_date) , max(tca.amended_end_date)) as maxdate
                                            from amendments as a
                                            left join time_amendments as ta 
                                                on ta.amendment_id = a.id
                                            left join time_and_cost_amendments as tca
                                                on tca.amendment_id = a.id
                                            where a.contract_id = c.id and a.is_approved = true
                                    ), cd.planned_end_date)
                                )) not  between "' . $financeStartDate . '" and "' . $financeEndDate . '" )
                        ';
                        if ($year == $endYear) {
                            $data['query_string'] .= ' ) ';
                        } else {
                            $data['query_string'] .= ' or ';
                        }

                    }

                    break;
                case 'transferred_contracts_year':
                    $startFiscalYearDate = JDateTime::toGregorian($queryArray['transferred_contracts_year'] - 1, 10, 1);
                    $startFiscalYearDate = $startFiscalYearDate[0] . '-' . $startFiscalYearDate[1] . '-' . $startFiscalYearDate[2];
                    $data['query_string'] .= ' and c.id in
                                            (
                                           select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            join (
                                              select
                                                    csl.id, csl.contract_id, csl.status_id
                                                from contract_status_logs as csl
                                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                                    on csl.id = max_value.max_id
                                            ) as cs
                                                on cs.contract_id = q_inner_c.id
                                            join contract_details as cd 
							                    on q_inner_c.id = cd.contract_id
                                            where cs.status_id in (' . $workInProgressStatus['id'] . ',' . $contractCompletionDefectLiabilityPeriodStatus['id'] . ',' . $suspendedStatus['id'] . ') and 
                                                cd.agreement_signature_date < "' . $startFiscalYearDate . '"
                                            ) ';
                    break;
                case (preg_match('/year.*/', $key) ? true : false):
                    if (!$fiscalYear) {
                        $fiscalYear = true;
                        $condition = ' between "' . $fiscal_start_year . '" and "' . $fiscal_end_year . '"';
                        $data['query_string'] .= '   and c.id in (
                                        select cs.id
                                        from contracts as cs
                                        left join contract_details as cd
                                            on cd.contract_id = cs.id 
                                        where cd.agreement_signature_date ' . $condition . '
                                        )  ';
                    }
                    break;
                default:
                    $data['query_string'] .= ' and 1=2 ';
                    break;
            }
        }
        return $data;

    }

    public function getCurrentYear()
    {
        $now = Carbon::now();
        $jalaliNow = JDateTime::toJalali($now->year, $now->month, $now->day);
        $currentFiscalYear = $jalaliNow[0];
        if ($jalaliNow[1] > 12)
            $currentFiscalYear = $jalaliNow[0] + 1;

        $data = DB::select('        
        select 
            max((
              ifnull(cd.actual_value, 0) + 
              ifnull(psc.provisional_sum_and_contingency, 0) + 
              ifnull(amendment.amount, 0)
            ) * 
            if(c.exchange_rate, c.exchange_rate, 1)) as max_value,
            min((
              ifnull(cd.actual_value, 0) + 
              ifnull(psc.provisional_sum_and_contingency, 0) + 
              ifnull(amendment.amount, 0)
            ) * 
            if(c.exchange_rate, c.exchange_rate, 1)) as min_value,
            min(cd.agreement_signature_date) as min_date,
            (max(cpp.amount * if(c.exchange_rate, c.exchange_rate, 1))) as max_payment,
            (min(cpp.amount * if(c.exchange_rate, c.exchange_rate, 1))) as min_payment
            
        from contracts as c
        join contract_details as cd
            on c.id = cd.contract_id
        left join provisional_sum_and_contingencies as psc
            on psc.contract_detail_id = cd.id	  
        left join (
            select 
                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                a.contract_id as contract_id
            from amendments as a
            left join cost_amendments as ca 
                on ca.amendment_id = a.id
            left join time_and_cost_amendments as tca
                on tca.amendment_id = a.id
            where a.is_approved = true
            group by  a.contract_id 
        
        ) as amendment
            on c.id = amendment.contract_id
        left join (
          select 
              cpp_inner.contract_id,
              sum(cpp_inner.amount) as amount
          from contract_progress_payments as cpp_inner
          where cpp_inner.is_published = true
          group by cpp_inner.contract_id
        ) as cpp
            on cpp.contract_id = c.id
        where c.is_published = true 
        ')[0];
        $data->month_id = $jalaliNow[1];
        $data->max_date = $currentFiscalYear;
        $data->min_date = jDateTime::strftime('Y-m-d', strtotime($data->min_date));
        $minDateInJalali = explode('-', $data->min_date);
        $data->min_date = (int)$minDateInJalali[0];
        if ((int)$minDateInJalali[1] > 12)
            $data->min_date = (int)$minDateInJalali[0] + 1;


        return response()->json($data, 200);
    }

    public function procurementEntitiesContractsData()
    {
        try {
            $data['awarded_contracts'] = DB::select('
                    select 
                        count(t.count) as published_count, 
                        round(sum(t.contract_value), 3) as awarded_contract_total_value, 
                        t.procurement_entity_id as procurement_entity_id,
                        ( 
                        select count(c.id)
                        from contracts as c 
                        join contract_details as cd
                          on cd.contract_id = c.id
                        join contract_details_verifications as cdv
                          on cdv.contract_detail_id = cd.id 
                        where cdv.is_approved = true and c.procurement_entity_id =t.procurement_entity_id
                        ) as signed_count
                    from
                        (select 
                                count(c.id) as count,
                                sum(ifnull(cd.actual_value, 0) + ifnull
                                        ((select 
                                                sum(ifnull(ca.amendment_amount, 0)) +
                                                sum(ifnull(tca.amendment_amount,0))
                                            from amendments as a
                                            left join cost_amendments as ca 
                                                on ca.amendment_id = a.id
                                            left join time_and_cost_amendments as tca
                                                on tca.amendment_id = a.id
                                            where a.contract_id =c.id
                                            
                                    ), 0
                                    ) +ifnull(psc.provisional_sum_and_contingency, 0))
                                * if(c.exchange_rate, c.exchange_rate, 1)as contract_value,
                                c.procurement_entity_id
                          from contracts as c 
                          join contract_details as cd
                            on cd.contract_id = c.id
                          join contract_details_verifications as cdv
                            on cdv.contract_detail_id = cd.id 
                            and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                            on psc.contract_detail_id = cd.id
                          where c.is_published = true
                          group by c.procurement_entity_id, c.exchange_rate,c.id
                        )as t
                    group by t.procurement_entity_id
            ');
            $data['paid_payment'] = DB::select('
                select 
                    round(sum(
                            ifnull(cpp.amount, 0) * 
                            if(c.exchange_rate, c.exchange_rate, 1)
                        ), 3) as paid_payments,
                        c.procurement_entity_id as procurement_entity_id 
                from contracts as c 
                join contract_progress_payments as cpp
                    on c.id = cpp.contract_id and cpp.is_approved = true
                where c.is_published = true
                group by c.procurement_entity_id
            ');
            $data['physical_progress_value'] = DB::select('
                select
                    c.procurement_entity_id,
                    round(sum(
                        (ifnull(cpp.total_physical_progress, 0) / 100) * 
                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)
                        * if(c.exchange_rate, c.exchange_rate, 1) ) ), 3)  as physical_progress_value
                        
                from contracts as c
                left join(
                    select 
                        inner_cpp.contract_id,
                        sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                    from contract_progress_payments as inner_cpp
                    where inner_cpp.is_approved = true
                    group by inner_cpp.contract_id 
                ) as cpp
                    on cpp.contract_id = c.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
                  ) as amendment
                        on c.id = amendment.contract_id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                    
                left join provisional_sum_and_contingencies as psc
                  on psc.contract_detail_id = cd.id
                where c.is_published = true
                group by c.procurement_entity_id
            ');
            $data['estimated_contracts_value'] = DB::select('
                select 
                    c.procurement_entity_id as procurement_entity_id,
                    round(sum(ifnull(cd.estimated_value, 0)) * 
                    if(c.exchange_rate, c.exchange_rate, 1), 3) as estimated_contract_value
                from contracts as c
                join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                where c.is_published = true
                group by c.procurement_entity_id, c.exchange_rate
              ');
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function procurementEntitiesContractsDetailsSearch(Request $request)
    {
        try {
            $pagination = '';
            if ($request->has('page_size') && $request->has('page_index')) {
                $limit = $request->query()['page_size'];
                $offset = $request->query()['page_index'] * $limit;
                $pagination = ' limit  ' . $limit . ' offset ' . $offset;
            }
            $queryParamsSize = sizeof($request->except(['page_size', 'page_index', 'lang']));

            if ($queryParamsSize === 0) {
                return response()->json([], 200);
            }
            $queryString['joins'] = '';
            $queryString['query_string'] = '';
            $contractTotalValue = false;
            $queryParams = $request->query();
            if ($request->has('projectIds')) {
                $queryParams['projectIds'] = implode($request->query()['projectIds'], ', ');
            }
            if ($request->has('procurement_entity_ids')) {
                $queryParams['procurement_entity_ids'] = implode($request->query()['procurement_entity_ids'], ', ');
            }
            foreach ($queryParams as $key => $value) {
                switch ($key) {
                    case 'procurement_entity_id':
                        $queryString['query_string'] .= ' and c.procurement_entity_id = ' . $value;
                        break;
                    case 'sector_id':
                        $queryString['query_string'] .= ' and c.sector_id = ' . $value;
                        break;
                    case 'projectIds':
                        $queryString['query_string'] .= ' and c.project_id in (' . $value . ') ';
                        break;
                    case 'procurement_entity_ids':
                        $queryString['query_string'] .= ' and c.procurement_entity_id in (' . $value . ') ';
                        break;
                    case 'contract_id':
                        $queryString['query_string'] .= ' and c.contract_number = ' . "'$value'";
                        break;
                    case 'procurement_type_id':
                        $queryString['query_string'] .= ' and c.procurement_type_id = ' . $value;
                        break;
                    case 'procurement_method_id':
                        $queryString['query_string'] .= ' and c.procurement_method_id = ' . $value;
                        break;
                    case 'contractStatus':
                        $queryString['joins'] .= ' join contract_status_logs as qcslj
                                    on qcslj.contract_id = c.id';
                        $queryString['query_string'] .= ' 
                                        and c.id in (select 
                                            qcslj.contract_id
                                        from contract_status_logs as qcslj
                                        where qcslj.id = (
                                            select 
                                            max(inner_csl.id) 
                                            from contract_status_logs as inner_csl 
                                            where qcslj.contract_id = inner_csl.contract_id)
                                            and qcslj.status_id = ' . $value . '
                                        group by qcslj.contract_id)
                                ';
                        break;
                    case (preg_match('/awarded_contract_count.*/', $key) ? true : false):
                        if (!$contractTotalValue) {
                            $contractTotalValue = true;
                            $condition = ' between ' . $queryParams['awarded_contract_count_from'] * 1000000 . ' and ' . $queryParams['awarded_contract_count_from'] * 1000000;
                            $queryString['query_string'] .= ' and c.id' . $condition;
                        }
                        break;
                    case (preg_match('/total_value.*/', $key) ? true : false):

                        if (!$contractTotalValue) {
                            $contractTotalValue = true;
                            $condition = ' between ' . $queryParams['total_value_from'] * 1000000 . ' and ' . $queryParams['total_value_to'] * 1000000;
                            $queryString['query_string'] .= ' and c.id in
                                            (select
                                                q_inner_c.id
                                            from contracts as q_inner_c
                                            join contract_details as q_inner_cd
                                                on q_inner_cd.contract_id = q_inner_c.id
                                            join contract_details_verifications as q_inner_cdv
                                                on q_inner_cdv.contract_detail_id = q_inner_cd.id
                                            where (
                                                    (q_inner_cd.actual_value * ifnull( c.exchange_rate, 1)) +
                                                    (ifnull((
                                                                select sum(ifnull(q_inner_ca.amendment_amount, 0)) + sum(ifnull(q_inner_tca.amendment_amount,0))
                                                                from amendments as q_inner_a
                                                                left join cost_amendments as q_inner_ca
                                                                    on q_inner_ca.amendment_id = q_inner_a.id
                                                                left join time_and_cost_amendments as q_inner_tca
                                                                    on q_inner_tca.amendment_id = q_inner_a.id
                                                                where q_inner_a.contract_id = q_inner_c.id
                                                            ), 0) * ifnull( c.exchange_rate, 1))

                                                    )  ' . $condition . '
                                            ) ';
                        }
                        break;
                }
            }
            $data['awarded_contracts'] = DB::select('
                    select 
                        count(t.count) as number_of_awarded_contracts, 
                        round(sum(t.contract_value), 3) as awarded_contract_total_value, 
                        t.procurement_entity_id as procurement_entity_id
                    from
                        (select 
                                count(c.id) as count,
                                sum(ifnull(cd.actual_value, 0) + ifnull
                                        ((select 
                                                sum(ifnull(ca.amendment_amount, 0)) +
                                                sum(ifnull(tca.amendment_amount,0))
                                            from amendments as a
                                            left join cost_amendments as ca 
                                                on ca.amendment_id = a.id
                                            left join time_and_cost_amendments as tca
                                                on tca.amendment_id = a.id
                                            where a.contract_id =c.id
                                            
                                    ), 0
                                    ) +ifnull(psc.provisional_sum_and_contingency, 0))
                                * if(c.exchange_rate, c.exchange_rate, 1)as contract_value,
                                c.procurement_entity_id,
                                c.project_id
                          from contracts as c
                            ' . $queryString['joins'] . ' 
                          join contract_details as cd
                            on cd.contract_id = c.id
                          join contract_details_verifications as cdv
                            on cdv.contract_detail_id = cd.id 
                            and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                            on psc.contract_detail_id = cd.id
                          where c.is_published = true
                            ' . $queryString['query_string'] . ' 
                          group by c.procurement_entity_id, c.exchange_rate,c.id,c.project_id
                        )as t
                    group by t.procurement_entity_id
                     ' . $pagination . ';
            ');
            $data['paid_payment'] = DB::select('
                select 
                    round(sum(
                            ifnull(cpp.amount, 0) * 
                            if(c.exchange_rate, c.exchange_rate, 1)
                        ), 3) as paid_payments,
                        c.procurement_entity_id as procurement_entity_id
                from contracts as c
                ' . $queryString['joins'] . '  
                join contract_progress_payments as cpp
                    on c.id = cpp.contract_id and cpp.is_approved = true
                where c.is_published = true
                    ' . $queryString['query_string'] . ' 
                group by c.procurement_entity_id
                 ' . $pagination . ';
            ');
            $data['physical_progress_value'] = DB::select('
                select
                    c.procurement_entity_id,
                    round(sum(
                        (ifnull(cpp.total_physical_progress, 0) / 100) * 
                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)
                        * if(c.exchange_rate, c.exchange_rate, 1) ) ), 3)  as physical_progress_value
                        
                from contracts as c
                left join(
                    select 
                        inner_cpp.contract_id,
                        sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                    from contract_progress_payments as inner_cpp
                    where inner_cpp.is_approved = true
                    group by inner_cpp.contract_id 
                ) as cpp
                    on cpp.contract_id = c.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
                  ) as amendment
                        on c.id = amendment.contract_id
                  ' . $queryString['joins'] . ' 
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                  on psc.contract_detail_id = cd.id
                where c.is_published = true
                  ' . $queryString['query_string'] . ' 
                group by c.procurement_entity_id
                 ' . $pagination . ';
            ');
            $data['estimated_contracts_value'] = DB::select('
                select 
                    c.procurement_entity_id as procurement_entity_id,
                    round(sum(ifnull(cd.estimated_value, 0)) * 
                    if(c.exchange_rate, c.exchange_rate, 1), 3) as estimated_contract_value
                from contracts as c
                 ' . $queryString['joins'] . ' 
                join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                where c.is_published = true
                    ' . $queryString['query_string'] . ' 
                group by c.procurement_entity_id, c.exchange_rate
                 ' . $pagination . ';
              ');
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function procurementEntitiesContractsDetails(Request $request)
    {
        try {
            if ($request->has('id')) {
                $id = $request->query()['id'];
            }
            $data['awarded_contracts'] = DB::select('
                    select 
                        count(t.count) as published_count, 
                        round(sum(t.contract_value), 3) as awarded_contract_total_value, 
                        t.procurement_entity_id as procurement_entity_id,
                        ( 
                        select count(c.id)
                        from contracts as c 
                        join contract_details as cd
                          on cd.contract_id = c.id
                        join contract_details_verifications as cdv
                          on cdv.contract_detail_id = cd.id 
                        where cdv.is_approved = true and c.procurement_entity_id =t.procurement_entity_id
                        ) as signed_count
                    from
                        (select 
                                count(c.id) as count,
                                sum(ifnull(cd.actual_value, 0) + ifnull
                                        ((select 
                                                sum(ifnull(ca.amendment_amount, 0)) +
                                                sum(ifnull(tca.amendment_amount,0))
                                            from amendments as a
                                            left join cost_amendments as ca 
                                                on ca.amendment_id = a.id
                                            left join time_and_cost_amendments as tca
                                                on tca.amendment_id = a.id
                                            where a.contract_id =c.id
                                            
                                    ), 0
                                    ) +ifnull(psc.provisional_sum_and_contingency, 0))
                                * if(c.exchange_rate, c.exchange_rate, 1)as contract_value,
                                c.procurement_entity_id
                          from contracts as c 
                          join contract_details as cd
                            on cd.contract_id = c.id
                          join contract_details_verifications as cdv
                            on cdv.contract_detail_id = cd.id 
                            and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                            on psc.contract_detail_id = cd.id
                          where c.is_published = true
                          group by c.procurement_entity_id, c.exchange_rate,c.id
                        )as t
                    where t.procurement_entity_id = ' . $id . '
                    group by t.procurement_entity_id
            ');
            $data['paid_payment'] = DB::select('
                select 
                    round(sum(
                            ifnull(cpp.amount, 0) * 
                            if(c.exchange_rate, c.exchange_rate, 1)
                        ), 3) as paid_payments,
                        c.procurement_entity_id as procurement_entity_id 
                from contracts as c 
                join contract_progress_payments as cpp
                    on c.id = cpp.contract_id and cpp.is_approved = true
                where c.is_published = true
                  and c.procurement_entity_id = ' . $id . '
                group by c.procurement_entity_id
            ');
            $data['physical_progress_value'] = DB::select('
                select
                    c.procurement_entity_id,
                    round(sum(
                        (ifnull(cpp.total_physical_progress, 0) / 100) * 
                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)
                        * if(c.exchange_rate, c.exchange_rate, 1) ) ), 3)  as physical_progress_value
                        
                from contracts as c
                left join(
                    select 
                        inner_cpp.contract_id,
                        sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                    from contract_progress_payments as inner_cpp
                    where inner_cpp.is_approved = true
                    group by inner_cpp.contract_id 
                ) as cpp
                    on cpp.contract_id = c.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
                  ) as amendment
                        on c.id = amendment.contract_id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                    
                left join provisional_sum_and_contingencies as psc
                  on psc.contract_detail_id = cd.id
                where c.is_published = true
                  and c.procurement_entity_id = ' . $id . '
                group by c.procurement_entity_id
            ');
            $data['estimated_contracts_value'] = DB::select('
                select 
                    c.procurement_entity_id as procurement_entity_id,
                    round(sum(ifnull(cd.estimated_value, 0)) * 
                    if(c.exchange_rate, c.exchange_rate, 1), 3) as estimated_contract_value
                from contracts as c
                join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                where c.is_published = true
                  and c.procurement_entity_id = ' . $id . '
                group by c.procurement_entity_id, c.exchange_rate
              ');
            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function numberOfContractInEachProvince(Request $request)
    {
        try {
            if ($request->has('id')) {
                $id = $request->query()['id'];
            }
            $data = DB::select('
                select 
                    p.id as province_id, 
                    count(distinct(c.id)) as contracts_count,
                    c.procurement_entity_id as procurement_entity_id
                from contracts as c 
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                where c.is_published = true
                    and c.procurement_entity_id = ' . $id . '
                    group by p.id, c.procurement_entity_id       
        ');

            return response()->json($data, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function getSearchDataForRange()
    {
        $data = DB::select('        
                    select 
                        max((
                          ifnull(cd.actual_value, 0) + 
                          ifnull(psc.provisional_sum_and_contingency, 0) + 
                          ifnull(amendment.amount, 0)
                        ) * 
                        if(c.exchange_rate, c.exchange_rate, 1)) as max_value,
                        min((
                          ifnull(cd.actual_value, 0) + 
                          ifnull(psc.provisional_sum_and_contingency, 0) + 
                          ifnull(amendment.amount, 0)
                        ) * 
                        if(c.exchange_rate, c.exchange_rate, 1)) as min_value,
                        max(c.id) as max_awarded_contract_count,
                        min(c.id) as min_awarded_contract_count
                    from contracts as c
                    join contract_details as cd
                        on c.id = cd.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                    ) as amendment
                        on c.id = amendment.contract_id
                    where c.is_published = true
                  
        ')[0];
        return response()->json($data, 200);
    }
}
