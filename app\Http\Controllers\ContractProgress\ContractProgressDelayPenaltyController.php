<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractDetailsDelayPenalty;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractProgressDelayPenalty;
use Illuminate\Http\Request;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ContractProgressDelayPenaltyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $contractId = $request->query('contract_id');
            $delayPenalties = ContractProgressDelayPenalty::where('contract_id', $contractId)->orderBy('id', 'asc')->get();
            if (!$delayPenalties) {
                return response()->json([], 404);
            }
            return response()->json($delayPenalties);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {

            $data = $request->all();
            $files['delay_penalty_document'] = $data['delay_penalty_document'];
            DB::beginTransaction();
            $contractProgressDelayPenalty = ContractProgressDelayPenalty::create(array_only($data, ContractProgressDelayPenalty::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractProgressDelayPenalty, $fieldName, $fileContent);
                }
            }

            DB::commit();
            return response()->json([], 201, [
                'location' => $contractProgressDelayPenalty['id'],
            ]);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $contractId
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $contractId)
    {
        try {
            $data = [];

            if (isset($request['delay_penalty_id']) && $request['delay_penalty_id'] !== 'undefined' && $request['delay_penalty_id']) {
                $delayPenaltyId = $request->query('delay_penalty_id');
                $contractProgressDelayPenalty = ContractProgressDelayPenalty::where('id', $delayPenaltyId)->first();
                $attachments = $contractProgressDelayPenalty->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
                foreach ($attachments as $key => $value) {
                    $index = $attachments[$key]['field_name'];
                    $att = [];
                    foreach ($attachments as $k => $v) {
                        if ($v['field_name'] == $index && $v['table_name'] == 'contract_progress_delay_penalties') {
                            array_push($att, $v);
                            $data[$attachments[$key]['field_name']] = $att;
                        }
                    }
                }
            }
            $contractDetails = ContractDetails::where('contract_id', $contractId)->first();

            if (!$contractDetails) {
                return response()->json([], 204);
            }
            $grandTotalAmount = 0;
            $grandTotalAmount = $grandTotalAmount + $contractDetails['actual_value'];

            $data['planned_end_date'] = $contractDetails['planned_end_date'];

            if ($contractDetails &&
                $contractDetails['is_delay_penalty_applicable']) {
                $data['is_contract_category_transfers_or_rental_of_vehicles'] = $contractDetails['is_contract_category_transfers_or_rental_of_vehicles'];
            }

            if ($contractDetails &&
                $contractDetails['is_delay_penalty_applicable'] &&
                !$contractDetails['is_contract_category_transfers_or_rental_of_vehicles']) {
                $contractDetailsDelayPenalty = ContractDetailsDelayPenalty::where('contract_detail_id', $contractDetails['id'])->first();
                $data['delay_penalty_percentage'] = $contractDetailsDelayPenalty['percentage'];
                $data['delay_penalty_period_id'] = $contractDetailsDelayPenalty['period_id'];
            }
            $amendments = Amendment::where('contract_id', $contractId)->get();


            if ($amendments) {
                $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
                foreach ($amendments as $amendment) {

                    foreach ($amendmentTypes as $amendmentType) {
                        if ($amendment['type_id'] == $amendmentType['id']) {
                            $typeSlug = $amendmentType['slug'];
                            continue;
                        }
                    }

                    if ($typeSlug == 'cost-amendment') {
                        $costAmendment = CostAmendment::where('amendment_id', $amendment['id'])->first();
                        $grandTotalAmount = $grandTotalAmount + $costAmendment['amendment_amount'];
                    }
                    if ($typeSlug == 'time-and-cost-amendment') {
                        $timeAndCostAmendment = TimeAndCostAmendment::where('amendment_id', $amendment['id'])->first();
                        $grandTotalAmount = $grandTotalAmount + $timeAndCostAmendment['amendment_amount'];
                    }
                }
            }

            $data['grand_total_amount'] = $grandTotalAmount;

            $contractProgress = ContractProgress::where('contract_id', $contractId)->first();
            if ($contractProgress) {
                $data['actual_start_date'] = $contractProgress['actual_start_date'];
            }

            return response()->json($data);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressDelayPenalty $contractProgressDelayPenalty
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractProgressDelayPenalty $contractProgressDelayPenalty)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\ContractProgressDelayPenalty $contractProgressDelayPenalty
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $contractProgressDelayPenaltyId)
    {
        try {
            $data = $request->all();

            $currentDate = Carbon::now()->toDateTimeString();
            DB::beginTransaction();

            if (!array_key_exists('parent_id', $data)) {
                $files['delay_penalty_document'] = $data['delay_penalty_document'];
                $contractProgressDelayPenalty = ContractProgressDelayPenalty::where('id', $contractProgressDelayPenaltyId)->first();
                ContractProgressDelayPenalty::where('id', $contractProgressDelayPenaltyId)
                    ->update(array_only($data, ContractProgressDelayPenalty::$fields));

                foreach ($files as $fieldName => $fileContent) {
                    if ($fileContent !== null) {
                        AttachmentController::saveFile($request, $contractProgressDelayPenalty, $fieldName, $fileContent);
                    }
                }
            } else if (isset($data['is_published']) && $data['is_published']) {
                ContractProgressDelayPenalty::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
            } else {
                ContractProgressDelayPenalty::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                        ]
                    );
            }
            DB::commit();
            return response()->json([], 201);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\ContractProgressDelayPenalty $contractProgressDelayPenalty
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $isDeleted = ContractProgressDelayPenalty::where('id', $id)->delete();
        if (!$isDeleted) {
            return response()->json([], 404);
        }
        return response()->json([], 204);
    }
}
