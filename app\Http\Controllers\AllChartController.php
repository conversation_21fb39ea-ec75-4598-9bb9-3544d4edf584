<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Filter;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Contract;

class AllChartController extends Controller
{
    public function index(Request $request)
    {
        $resourcesAssigned = Helper::getResourceAssigned($request['user_id']);
        $dashboardAccessList = Helper::getUserContextAccessList($request['user_id']);
        $functionParams['user_id'] = $request['user_id'];
        $functionParams['contractWhereCondition'] = '';
        $logicalOperator = ' and ';
        if (!count((array)$resourcesAssigned)) {
            $functionParams['contractWhereCondition'] = $logicalOperator . ' 1=2 ';
        }
        foreach ($resourcesAssigned as $key => $value) {
            switch ($key) {
                case 'user_role_pes':
                    if (count($value) <= 0) {
                        break;
                    }
                    $procurementEntityIds = [];
                    foreach ($value as $item) {
                        array_push($procurementEntityIds, $item->procurement_entity_id);
                    }
                    $functionParams['contractWhereCondition'] .= $logicalOperator . ' c.procurement_entity_id in (' . implode(',', $procurementEntityIds) . ') ';
                    break;
                case 'user_role_sectors':
                    if (count($value) <= 0) {
                        break;
                    }
                    $sectorIds = [];
                    foreach ($value as $item) {
                        array_push($sectorIds, $item->sector_id);
                    }
                    $functionParams['contractWhereCondition'] .= $logicalOperator . '  c.sector_id in (' . implode(',', $sectorIds) . ') ';
                    break;
                case 'user_role_vendor':
                    if (!$value) {
                        break;
                    }
                    $contractIds = [];
                    $licenseNumber = $value->vendor_license_number;
                    $data = DB::select
                    ('  select distinct 
                                cgi.contract_id
                            from company_general_informations as cgi
                            join companies as com
                                on cgi.id = com.company_general_information_id
                            where com.license_number = "' . $licenseNumber . '"
                            ');
                    foreach ($data as $item) {
                        array_push($contractIds, $item->contract_id);
                    }
                    $functionParams['contractWhereCondition'] .= $logicalOperator . '  c.id in (' . implode(',', $contractIds) . ') ';
                    break;
                case 'user_role_records':
                    $contractIds[] = 0;
                    foreach ($value as $item) {
                        array_push($contractIds, $item->record_id);
                    }
                    $functionParams['contractWhereCondition'] .= $logicalOperator . ' c.id in (' . implode(',', $contractIds) . ') ';
                    break;
                default:
                    throw new Exception('Invalid Resource assigment category!');
            }
            $logicalOperator = ' or ';
        }
        $functionParams['contractStatuses'] = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $functionParams['procurementTypes'] = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $functionParams['amendmentTypes'] = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $functionParams['search'] = Filter::prepareQueryStrings($request->query());
        $data = [];
        foreach ($dashboardAccessList as $item) {
            $functionName = str_replace('-', '', ucwords($item->context->slug, '-'));
            $key = str_replace('-', '_', $item->context->slug);
            $functionName = lcfirst($functionName);

            $data[$key] = $this->$functionName($functionParams);

        }
        return response()->json($data);
    }

    public function generalInformationApprovedContractsCountWidget($params)
    {

        $data = DB::select('
                        select
                            count(distinct c.id) as count
                        from contracts as c 
                        join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                          ' . $params['search']['joins'] . '
                        where cgiv.is_approved = true
                          ' . $params['contractWhereCondition'] . '
                          ' . $params['search']['query_string'] . ' ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->count;

    }

    public function contractsCountWidget($params)
    {

        $data = DB::select('
                        select
                            count(distinct c.id) as count
                        from contracts as c 
                         ' . $params['search']['joins'] . '
                        where 1 = 1
                         ' . $params['contractWhereCondition'] . '
                         ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->count;
    }

    public function generalInformationNotApprovedContractsCountWidget($params)
    {

        $data = DB::select('
                        select
                            count(distinct c.id) - count(case when cgiv.is_approved = true then 1 end) as count  
                        from contracts as c
                        left join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                          ' . $params['search']['joins'] . '
                        where 1 = 1
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->count;
    }

    public function publishedContractsCountWidget($params)
    {
        $data = DB::select('
                        select
                             count(c.id) as count  
                        from contracts as c
                          ' . $params['search']['joins'] . '
                        where c.is_published = true
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->count;
    }

    public function detailsApprovedContractsTotalValueWidget($params)
    {
        $data = DB::select('
                        select 
                            sum(
                                (ifnull(cd.actual_value, 0) + 
                                ifnull(psc.provisional_sum_and_contingency, 0) + 
                                ifnull(amendment.amount, 0))
                                * if(c.exchange_rate, c.exchange_rate, 1)
                            ) as contracts_total_value
                          from contracts as c
                          left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                             from amendments as a
                             left join cost_amendments as ca 
                                 on ca.amendment_id = a.id
                             left join time_and_cost_amendments as tca
                                 on tca.amendment_id = a.id
                             where a.is_approved = true
                             group by  a.contract_id 
                          ) as amendment
                                on c.id = amendment.contract_id
                          join contract_details as cd
                                on cd.contract_id = c.id
                          join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id
                                and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                              on psc.contract_detail_id = cd.id
                                    
                             ' . $params['search']['joins'] . '
                          where 1 = 1 ' . $params['contractWhereCondition'] . '
                          ' . $params['search']['query_string'] . ' ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_total_value;
    }

    public function contractsTotalPaymentsWidget($params)
    {
        $data = DB::select('
                           select 
                                sum(  ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)
                                    ) as paid_payments 
                                from contracts as c 
                                join contract_progress_payments as cpp
                                    on c.id = cpp.contract_id 
                                    and cpp.is_approved = true
                                ' . $params['search']['joins'] . '
                                where 1 = 1  
                                ' . $params['contractWhereCondition'] . '
                                ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->paid_payments;
    }

    public function contractsTotalPhysicalProgressValueWidget($params)
    {
        $data = DB::select('
                            select
                                sum(
                                    (ifnull(cpp.total_physical_progress, 0) / 100) * 
                                    (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)
                                    * if(c.exchange_rate, c.exchange_rate, 1) ) )  as actual_progress_value
                                        
                                from contracts as c
                                left join(
                                    select 
                                        inner_cpp.contract_id,
                                        sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                                    from contract_progress_payments as inner_cpp
                                    where inner_cpp.is_approved = true
                                    group by inner_cpp.contract_id 
                                ) as cpp
                                    on cpp.contract_id = c.id
                                left join (
                                     select 
                                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                            a.contract_id as contract_id
                                     from amendments as a
                                     left join cost_amendments as ca 
                                         on ca.amendment_id = a.id
                                     left join time_and_cost_amendments as tca
                                         on tca.amendment_id = a.id
                                     where a.is_approved = true
                                     group by  a.contract_id 
                                  ) as amendment
                                        on c.id = amendment.contract_id
                                join contract_details as cd
                                    on cd.contract_id = c.id
                                join contract_details_verifications as cdv
                                    on cdv.contract_detail_id = cd.id
                                    and cdv.is_approved = true
                                left join provisional_sum_and_contingencies as psc
                                  on psc.contract_detail_id = cd.id
                                    ' . $params['search']['joins'] . '
                                where 1 = 1 
                                    ' . $params['contractWhereCondition'] . '
                                    ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->actual_progress_value;
    }

    public function aboveThresholdContractsCountWidget($params)
    {
        $data = DB::select('
                         select 
                                count(c.id) as above_threshold_contracts_count
                                from contracts as c 
                            ' . $params['search']['joins'] . '
                            where c.is_above_threshold = true
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->above_threshold_contracts_count;
    }

    public function belowThresholdContractsCountWidget($params)
    {
        $data = DB::select('
                          select 
                                count(c.id) as below_threshold_contracts_count
                                from contracts as c 
                            ' . $params['search']['joins'] . '
                            where c.is_above_threshold = false
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->below_threshold_contracts_count;
    }

    public function cpmdSpecialistsRemarksCountWidget($params)
    {
        $data = DB::select('
                          select
                            count(distinct c.id) as specialists_remarks 
                            from contracts as c
                            join remarks 
                                on remarks.contract_id = c.id
                            ' . $params['search']['joins'] . '
                            where 1 = 1
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->specialists_remarks;
    }

    public function contractStatusCompletedCountWidget($params)
    {
        $contractStatusCompleted = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'contract-completion-defect-liability-period');
        $data = DB::select('
                      select 
                        count(distinct c.id) as completed_contracts 
                        from contracts as c
                        join (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                        ) as csl_max
                            on csl_max.contract_id = c.id
                            ' . $params['search']['joins'] . '
                           where  csl_max.status_id=' . $contractStatusCompleted['id'] . '
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->completed_contracts;
    }

    public function detailsApprovedAmendedContractsCountWidget($params)
    {
        $data = DB::select('
                      select count(distinct c.id) as amended_contracts 
                            from contracts as c
                            join amendments 
                                on amendments.contract_id = c.id
                             join contract_details as cd
                                on cd.contract_id = c.id
                            join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id 
                                and cdv.is_approved = true
                            ' . $params['search']['joins'] . '
                           where amendments.is_approved = true
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->amended_contracts;
    }

    public function contractStatusCancelledCountWidget($params)
    {
        $contractStatusCancelled = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'cancelled');
        $data = DB::select('
                        select count(distinct c.id) as cancelled_contracts 
                            from contracts as c
                           join (
                                	select 
                                        csl.id, csl.contract_id, csl.status_id 
                                    from contract_status_logs as csl
                                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                    on csl.id = max_value.max_id
                            ) as csl_max
                                on csl_max.contract_id = c.id
                            ' . $params['search']['joins'] . '
                          where  csl_max.status_id=' . $contractStatusCancelled['id'] . '
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->cancelled_contracts;
    }

    public function analyzedContractsCountWidget($params)
    {
        $data = DB::select('
                          select 
                            count(max_value.max_id) as contracts_analysis
                            from contracts as c
                            join 
                                (
                                    select 
                                        contract_id
                                        , max(id) as max_id 
                                    from npa_other_comments
                                    where is_confirmed = true 
                                    group by contract_id
                                ) as max_value
                            on c.id = max_value.contract_id 
                            ' . $params['search']['joins'] . '
                          where 1=1 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_analysis;
    }

    public function notAnalyzedContractsCountWidget($params)
    {
        $data = DB::select('
                   select 
                    (contracts_count-contracts_analysis) as 
                     contracts_not_analysis
                    from(
                            select
                            (
                            select count(c.id) as contracts_count
                            from contracts as c
                             ' . $params['search']['joins'] . '
                            where 1=1 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            ) as contracts_count
                            ,(
                            select 
                                count(max_value.max_id) as contracts_analysis
                            from contracts as c
                            join 
                                (
                                    select 
                                        contract_id
                                        , max(id) as max_id 
                                    from npa_other_comments
                                    where is_confirmed = true 
                                    group by contract_id
                                ) as max_value
                            on c.id = max_value.contract_id 
                             ' . $params['search']['joins'] . '
                            where 1=1 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            ) as contracts_analysis
                        ) as t  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_not_analysis;
    }

    public function contractAnalysisPublishedByCpmdManagersCountWidget($params)
    {
        $data = DB::select('
                      select 
                        count(distinct c.id) as contracts_analysis_by_managers 
                        from npa_other_comments
                        left join contracts as c
                            on c.id = npa_other_comments.contract_id
                            ' . $params['search']['joins'] . '
                        where  npa_other_comments.is_published = true 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_analysis_by_managers;
    }

    public function contractAnalysisPrepareToPublishCountWidget($params)
    {
        $data = DB::select('
                       select 
                        count(distinct c.id) as contracts_analysis_ready_to_published_to_website 
                        from npa_other_comments
                        left join contracts as c 
                            on c.id = npa_other_comments.contract_id
                            ' . $params['search']['joins'] . '
                        where  npa_other_comments.is_confirmed = true and npa_other_comments.is_approved = false
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_analysis_ready_to_published_to_website;
    }

    public function changeRequestContractsCountWidget($params)
    {
        $data = DB::select('
                      select 
                        count(distinct c.id) as contracts_to_change_information 
                        from contracts as c
                            ' . $params['search']['joins'] . '
                        where  c.has_requested_to_unpublish = true 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (count($data) === 0) {
            return null;
        }
        return $data[0]->contracts_to_change_information;
    }

    public function publishedDocumentsPercentageWidget($params)
    {
        $data['total'] = DB::select('
                select 
                    count(c.id ) * 15 as contract_count
                from contracts as c
                ' . $params['search']['joins'] . '
                where 1 = 1 
                 ' . $params['contractWhereCondition'] . '
                 ' . $params['search']['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                count( att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    ' . $params['search']['joins'] . '
                    where  1 = 1 
                    ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                        ' . $params['search']['joins'] . '
                        where  1 = 1 
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                        ' . $params['search']['joins'] . '
                        where  1 = 1  
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                        ' . $params['search']['joins'] . '
                        where  1 = 1 
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $params['search']['joins'] . '
                            where  1 = 1 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
        ');
        if ($data['uploaded'][0]->count === 0) {
            return null;
        }
        return $data['uploaded'][0]->count * 100 / $data['total']->contract_count;
    }

    public function contractCountAndValueBasedOnProcurementType($params)
    {
        $data = DB::select('
                    select
                      count(c.id) as count,
                      round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            )/ 1000000,4) as total_value,
                      c.procurement_type_id
                        from contracts as c
                        join contract_details as cd
                          on c.id = cd.contract_id
                        join contract_details_verifications as cdv
                          on cd.id = cdv.contract_detail_id
                        left join provisional_sum_and_contingencies as psc
                          on psc.contract_detail_id = cd.id
                        left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.is_approved = true
                                group by  a.contract_id 
                        
                      ) as amendment
                        on c.id = amendment.contract_id
                      ' . $params['search']['joins'] . '
                    where  cdv.is_approved = true
                    ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    group by c.procurement_type_id ');
        return $data;
    }

    public function publishedAndUnpublishedContractsCount($params)
    {
        $data = DB::select('
                     select 
                        count(case when c.is_published = true then 1 end) as published_contracts,
                        count(case when c.is_published = false then 1 end) as unpublished_contracts
                        from contracts as c
                            ' . $params['search']['joins'] . '
                        where  1 = 1
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '  ');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    public function publishedAndSignedContractsCount($params)
    {
        $contractStatusNotSigned = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'not-signed');
        $data = DB::select('
                       select 
                        c.procurement_type_id,
                        count(case when c.is_published = true then 1 end) as published_contracts_count,
                        count(c.id) signed_contracts_count
                        from contracts as c
                        join (
                            select
                                csl.id, csl.contract_id, csl.status_id
                            from contract_status_logs as csl
                            join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                on csl.id = max_value.max_id
                            ) as cs
                                on cs.contract_id = c.id
                         ' . $params['search']['joins'] . '
                        where cs.status_id != ' . $contractStatusNotSigned['id'] . ' 
                         ' . $params['contractWhereCondition'] . '
                         ' . $params['search']['query_string'] . '
                        group by c.procurement_type_id ');
        return $data;
    }

    public function contractsRelatedToContractManagerCount($params)
    {
        $data = DB::select('
                         select
                         c.contract_manager_id, 
                         count(distinct c.id) as contracts_count
                         from contracts as c
                            ' . $params['search']['joins'] . '
                            where c.contract_manager_id is not null  
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            group by c.contract_manager_id');
        $userIds = [];
        foreach ($data as $item) {
            array_push($userIds, $item->contract_manager_id ? $item->contract_manager_id : 0);
        }
        $users = Helper::getUsersByIds($userIds);
        foreach ($data as &$item) {
            foreach ($users as $user) {
                if ($item->contract_manager_id == $user->id) {
                    $item->full_name = $user->full_name;
                }
            }
        }
        return $data;
    }

    public function contractsBasedOnThresholdAndProcurementTypeCount($params)
    {
        $data = DB::select('
                        select 
                            c.procurement_type_id, 
                            count(case when c.is_above_threshold = true then 1 end) as above_threshold,
                            count(case when c.is_above_threshold = false then 1 end) as below_threshold
                            from contracts as c 
                            ' . $params['search']['joins'] . '
                            where 1 = 1 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            group by c.procurement_type_id ');
        return $data;
    }

    public function publishedContractsBasedOnThresholdCount($params)
    {
        $contractStatusNotSigned = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'not-signed');
        $data = DB::select('
                       select 
                            is_above_threshold as threshold,
                            count(case when c.is_published = true then 1 end) as published_contracts,
                            count(c.id) as signed_contract
                            from contracts as c 
                            join (
                                   select
                                      csl.id, csl.contract_id, csl.status_id
                                   from contract_status_logs as csl
                                   join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                       on csl.id = max_value.max_id
                                   ) as cs
                                       on cs.contract_id = c.id
                            ' . $params['search']['joins'] . '
                            where cs.status_id != ' . $contractStatusNotSigned['id'] . ' 
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            group by is_above_threshold ');
        return $data;
    }

    public function plannedAndActualUploadedDocumentsCount($params)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($params['procurementTypes'], 'consultancy');
        $data['total_contracts_count'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $params['search']['joins'] . '
                where c.procurement_type_id != ' . $consultancyServices['id'] . ' 
                ' . $params['contractWhereCondition'] . '
                ' . $params['search']['query_string'] . '
        ')[0];
        $data['total_contracts_count'] = $data['total_contracts_count']->contract_count;
        $data['documents_uploaded'] = DB::select('
                        select 
                            att.field_name as document_name, 
                            count(distinct att.foreign_key) as count
                            from attachments as att
                            where (att.foreign_key in (
                                    select 
                                        cd.id as contract_details_id	
                                    from contracts as c
                                    join contract_details as cd
                                        on cd.contract_id  = c.id
                                    join contract_details_verifications as cdv
                                        on cdv.contract_detail_id = cd.id
                                        and cdv.is_approved = true
                                     ' . $params['search']['joins'] . '
                                    where  c.procurement_type_id != ' . $consultancyServices['id'] . '
                                     ' . $params['contractWhereCondition'] . '
                                     ' . $params['search']['query_string'] . '
                                                    )
                                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                                 ,\'contract_performance_guarantee\')
                                 ) or (att.foreign_key in (
                                        select 
                                            cpa.id as cpa_id				
                                        from contracts as c
                                        join contract_progress_advances as cpa
                                            on cpa.contract_id = c.id
                                         ' . $params['search']['joins'] . '
                                        where   c.procurement_type_id != ' . $consultancyServices['id'] . '
                                         ' . $params['contractWhereCondition'] . '
                                         ' . $params['search']['query_string'] . '
                                                    )	
                                and att.field_name = \'advance_payment_guarantee\'
                                        )
                                or (att.foreign_key in (
                                        select 
                                            cs.id as cs_id
                                        from contracts as c
                                        join contract_statuses as cs 
                                            on cs.contract_id = c.id
                                        join contract_status_verifications as csv
                                            on csv.contract_status_id = cs.id
                                            and csv.is_approved = true
                                        ' . $params['search']['joins'] . '
                                        where  c.procurement_type_id != ' . $consultancyServices['id'] . '
                                        ' . $params['contractWhereCondition'] . '
                                        ' . $params['search']['query_string'] . '
                                
                                                    )
                                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                                    )
                                
                                or (att.foreign_key in (
                                        select 
                                            comp.id as comp_id
                                        from contracts as c
                                        join company_general_informations as cgi
                                            on cgi.contract_id = c.id
                                            and cgi.is_approved = true
                                        join companies as comp
                                            on comp.company_general_information_id = cgi.id
                                            and cgi.is_approved = true
                                        ' . $params['search']['joins'] . '
                                        where  c.procurement_type_id != ' . $consultancyServices['id'] . '
                                        ' . $params['contractWhereCondition'] . '
                                        ' . $params['search']['query_string'] . '
                                                )
                                and att.field_name = \'joint_venture_licence\'
                                    )
                                or (att.foreign_key in (
                                            select 
                                                c.id as c_id
                                            from contracts as c
                                            ' . $params['search']['joins'] . '
                                            where  c.procurement_type_id != ' . $consultancyServices['id'] . '
                                            ' . $params['contractWhereCondition'] . '
                                            ' . $params['search']['query_string'] . '
                                )
                                and att.field_name = \'planned_payments_schedule\'
                                    )
                            group by att.field_name');
        return $data;
    }

    public function consultancyServicesPlannedAndActualUploadedDocumentsCount($params)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($params['procurementTypes'], 'consultancy');

        $data['total_contracts_count'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $params['search']['joins'] . '
                where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                 ' . $params['contractWhereCondition'] . '
                 ' . $params['search']['query_string'] . '
        ')[0];
        $data['total_contracts_count'] = $data['total_contracts_count']->contract_count;
        $data['documents_uploaded'] = DB::select('
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                        ' . $params['search']['joins'] . '
                    where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                     ' . $params['contractWhereCondition'] . '
                     ' . $params['search']['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'reference_terms\'
                 ,\'financial_tables\', \'action_plan\', \'negotiation_meeting_minutes\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'key_staffs\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                           ' . $params['search']['joins'] . '
                        where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                            ' . $params['search']['joins'] . '
                        where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $params['contractWhereCondition'] . '
                         ' . $params['search']['query_string'] . '
                
                                    )
                and att.field_name  = \'contract_closeout_report\'
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                            ' . $params['search']['joins'] . '
                        where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $params['contractWhereCondition'] . '
                         ' . $params['search']['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $params['search']['joins'] . '
                            where  c.procurement_type_id = ' . $consultancyServices['id'] . '
                             ' . $params['contractWhereCondition'] . '
                             ' . $params['search']['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');
        return $data;
    }

    public function contractsBasedOnProcurementMethodCount($params)
    {
        $data = DB::select('
                       select
                        count(c.id) as contrats_count,
                        c.procurement_method_id
                        from contracts as c
                        ' . $params['search']['joins'] . '
                        where  1 = 1 
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by c.procurement_method_id');
        return $data;
    }

    public function cpmdManagerContractsBasedOnProcurementMethodCount($params)
    {
        $data = DB::select('
                       select
                        c.procurement_entity_id,
                        c.procurement_method_id,
                        count(c.id) as contracts_count
                        from contracts as c
                        ' . $params['search']['joins'] . '
                        where 1 = 1 
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by c.procurement_entity_id , c.procurement_method_id');
        return $data;
    }

    public function contractsBasedOnChallengesCount($params)
    {
        $data = DB::select('
                        select 
                            total_contracts_count, 
                            challenges_contracts_count,
                            (total_contracts_count-challenges_contracts_count) as not_challenge_contracts_count
                            from 
                            (
                                select 
                                    count(distinct(c.id)) as total_contracts_count, 
                                    count(distinct (cr.contract_id)) as challenges_contracts_count
                                from contracts as c
                                left Join challenges_and_remarks as cr 
                                    on c.id = cr.contract_id and cr.is_approved = true
                                ' . $params['search']['joins'] . '
                                where 1 = 1 
                                 ' . $params['contractWhereCondition'] . '
                                 ' . $params['search']['query_string'] . '
                            ) as t');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    public function contractsBasedOnSelectionMethodCount($params)
    {
        $consultancy = DropDowns::getElementTypeBySlug($params['procurementTypes'], 'consultancy');
        $data = DB::select('
                       select 
                        cc.selection_method_id,
                        count(cc.id) as count
                        from contracts as c 
                        join consultancy_contracts_selection_methods as cc
                             on cc.contract_id = c.id 
                        ' . $params['search']['joins'] . '
                        where  c.procurement_type_id = ' . $consultancy['id'] . '   
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                         group by cc.selection_method_id');
        return $data;
    }

    public function contractsBasedOnStatusCount($params)
    {
        $data['contract_count'] = DB::select('
                        select 
                            count(distinct c.id) as contract_count
                        from contracts as c 
                        join (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                            ) as csl_max
                                on csl_max.contract_id = c.id
                        ' . $params['search']['joins'] . '
                        where  1 = 1
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                    ')[0];
        $data['contract_count'] = $data['contract_count']->contract_count;
        $data['status_based_contracts_count'] = DB::select('
                        select 
                            cs.status_id as status_id,
                            count( distinct cs.id) as count
                        from (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                            ) as cs
                        join contracts as c 
                            on c.id = cs.contract_id
                        ' . $params['search']['joins'] . '
                        where 1 = 1
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by cs.status_id;
             
                        
                    ');
        return $data;
    }

    public function contractsValueBasedOnStatus($params)
    {
        $data['total_value'] = DB::select('
                        select round(sum(temp.contract_value)/ 1000000, 4) as contracts_total_value
                            from	
                            (select 
                                sum(
                                                    (ifnull(cd.actual_value, 0) + 
                                                    ifnull(psc.provisional_sum_and_contingency, 0) + 
                                                    ifnull(amendment.amount, 0))
                                                    * if(c.exchange_rate, c.exchange_rate, 1)
                                                )  as contract_value
                            from contracts as c
                            left join (
                                                 select 
                                                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                                        a.contract_id as contract_id
                                                 from amendments as a
                                                 left join cost_amendments as ca 
                                                     on ca.amendment_id = a.id
                                                 left join time_and_cost_amendments as tca
                                                     on tca.amendment_id = a.id
                                                 where a.is_approved = true
                                                 group by  a.contract_id 
                                              ) as amendment
                                                    on c.id = amendment.contract_id 
                            join (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                            ) as cs
                              on cs.contract_id = c.id
                            join contract_details as cd
                                on cd.contract_id = c.id
                            join contract_details_verifications as cdv
                                on cdv.contract_detail_id = cd.id 
                                and cdv.is_approved = true
                            left join provisional_sum_and_contingencies as psc
                                on psc.contract_detail_id = cd.id
                            ' . $params['search']['joins'] . '
                            where 1 = 1
                            ' . $params['contractWhereCondition'] . '
                            ' . $params['search']['query_string'] . '
                            group by c.id, c.exchange_rate) as temp;
                    ')[0];
        $data['total_value'] = $data['total_value']->contracts_total_value;
        $data['value_based_on_status'] = DB::select('
                        select 
                            cs.status_id as status_id,
                            round(sum(
                                    (ifnull(cd.actual_value, 0) + 
                                    ifnull(psc.provisional_sum_and_contingency, 0) + 
                                    ifnull(amendment.amount, 0))
                                    * if(c.exchange_rate, c.exchange_rate, 1)
                                ) / 1000000, 4)  as value
                        from (
                                select 
                                    csl.id, csl.contract_id, csl.status_id 
                                from contract_status_logs as csl
                                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                                on csl.id = max_value.max_id
                            ) as cs
                        join contracts as c
                            on c.id = cs.contract_id
                        left join (
                                 select 
                                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                        a.contract_id as contract_id
                                 from amendments as a
                                 left join cost_amendments as ca 
                                     on ca.amendment_id = a.id
                                 left join time_and_cost_amendments as tca
                                     on tca.amendment_id = a.id
                                 where a.is_approved = true
                                 group by  a.contract_id 
                        ) as amendment
                            on c.id = amendment.contract_id 
                        join contract_details as cd
                            on cd.contract_id = c.id
                        join contract_details_verifications as cdv
                            on cdv.contract_detail_id = cd.id 
                            and cdv.is_approved = true
                        left join provisional_sum_and_contingencies as psc
                                on psc.contract_detail_id = cd.id
                        ' . $params['search']['joins'] . '
                        where 1 = 1
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by cs.status_id;
                    ');
        return $data;
    }

    public function paymentsPercentageInComparisonWithPlan($params)
    {
        $data['plan_and_actual_percentage'] = DB::select('
                    select distinct
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                          sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        ) as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where  1 = 1
                    ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
            ')[0];
        $data['plan_and_actual_percentage'] = $data['plan_and_actual_percentage']->percentage;
        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                          if(
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where  1 = 1
                    ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    group by cr.iteration_count;   
            ');
        return $data;
    }

    public function completeContractsPaymentsValueBasedOnProcurementTypes($params)
    {
        $contractStatusCompleted = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'contract-completion-defect-liability-period');
        $data = DB::select('
            select 
                round(
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as total_value,
                    
                round(
                  sum(
                          ifnull(cpp.amount,0) * 
                          if(c.exchange_rate, c.exchange_rate, 1)
                      ) / 1000000, 4) as paid_amounts,
                c.procurement_type_id
            from contracts as c
            join contract_details as cd
                on c.id = cd.contract_id
            join contract_details_verifications as cdv
                on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cd.contract_id
            left join (
                select 
                    cpp_inner.contract_id ,
                    sum(cpp_inner.amount) as amount
                from  contract_progress_payments as cpp_inner
                where cpp_inner.is_approved = true
                group by cpp_inner.contract_id 
              ) as cpp
                on c.id = cpp.contract_id 
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
               ) as amendment
                   on c.id = amendment.contract_id
             ' . $params['search']['joins'] . '
              where cs.status_id = ' . $contractStatusCompleted['id'] . '
             ' . $params['contractWhereCondition'] . '
             ' . $params['search']['query_string'] . '
            group by c.procurement_type_id 
             ');
        return $data;
    }

    public function workInProgressContractsPhysicalProgressValue($params)
    {

        $workInProgressStatus = DropDowns::getElementTypeBySlug($params['contractStatuses'], 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $tempPlan = 0;
        $tempActual = 0;
        $data = [];
        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select
            (' select
                        round(
                        sum((cp.physical_progress_percentage  / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000, 4) + ' . $tempPlan . ' as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000 , 4) + ' . $tempActual . ' as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and cpv.is_approved = true


                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id


                    join contracts as c
                        on cp.contract_id = c.id
                    join contract_details as cd
	                    on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1  
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        cp.year in  ( ' . $value['year'] . ' ) and
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $tempPlan = $data['plan_and_actual'][$key]->planning_progress_value ? $data['plan_and_actual'][$key]->planning_progress_value : 0;
            $tempActual = $data['plan_and_actual'][$key]->actual_progress_value ? $data['plan_and_actual'][$key]->actual_progress_value : 0;

            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        round(sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as actual_progress_value
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment
                        on c.id = amendment.contract_id
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                   ' . $params['search']['joins'] . '
                    where 1 = 1
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . ' and
                         cs.status_id = ' . $contract_status_id . ' and
                        crp.year in  ( ' . $value['year'] . ' ) and
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }
        return $data;
    }

    public function getProcurementTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->procurement_type(), $slug)['id'];
    }

    public function procurement_type()
    {
        $path = 'api/dropDown/procurementType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function specialistAnalysedContractsCount($params)
    {
        $contract = Contract::select('sector_id')->with(['contract_specialists' => function ($query) use ($params) {
            $query->select(
                'contract_id',
                'id',
                'specialist_id'
            )->where('specialist_id', $params['user_id']);
        }])->first();
        if (!isset($contract['sector_id'])) {
            return [];
        }
        $data = DB::select('
                    select
                      cs.specialist_id,
                      count(case when noc.is_confirmed = true then 1 end) as contracts_count
                    from contract_specialists as cs
                    left join contracts as c
                      on c.id = cs.contract_id
                    left join npa_other_comments as noc
                       on c.id = noc.contract_id
                    ' . $params['search']['joins'] . '
                    where  c.sector_id = ' . $contract['sector_id'] . '
                    and cs.specialist_id is not null
                    ' . $params['search']['query_string'] . '
                    group by cs.specialist_id');

        $userIds = [];
        foreach ($data as $item) {
            array_push($userIds, $item->specialist_id);
        }
        $users = Helper::getUsersByIds($userIds);
        foreach ($data as &$item) {
            foreach ($users as $user) {
                if ($item->specialist_id == $user->id) {
                    $item->full_name = $user->full_name;
                }
            }
        }
        return $data;
    }

    public function specialistContractsAnalysisStatusCount($params)
    {
        $data = DB::select
        ('
          select 
                (
                    select
                        count(c.id) as  assigned_contracts_to_specialist 
                    from contracts as c
                     where 1 = 1  ' . $params['contractWhereCondition'] . '
                ) as assigned_contracts_to_specialist
                ,(
                 select 
                    count(max_value.max_id) as number_of_analysis_contracts
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                        ' . $params['search']['joins'] . '
                         where 1=1 ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                 ) as number_of_analysis_contracts
                 ,(select 
                    count(max_value.max_id) as number_of_analysis_contracts
                from contracts as c
                join 
                    (
                        select 
                            contract_id
                            , max(id) as max_id 
                        from npa_other_comments
                        where is_confirmed = true
                        and is_published = true 
                        group by contract_id
                    ) as max_value
                on c.id = max_value.contract_id 
                         ' . $params['search']['joins'] . '
                where 1 = 1 ' . $params['contractWhereCondition'] . '
                ' . $params['search']['query_string'] . '
              ) as specialist_contracts_published_to_website
        ');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    public function specialistContractsAnalysisStatusCountInPe($params)
    {
        $data = DB:: select
        ('
                select 
                    (
                    select
                        count(c.id) as  assigned 
                        from contracts as c
                        ' . $params['search']['joins'] . '
                    where 1 = 1  ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    ) as assigned
                    ,(
                    select 
                      count(c.id) as inserted
                    from contracts as c
                    join contract_general_information_verifications as cgiv
                    on cgiv.contract_id = c.id and 
                    cgiv.is_approved = true 
                     ' . $params['search']['joins'] . '
                    where 1 = 1' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    ) as inserted
                    ,(
                    select 
                      count(c.id) as confirmed
                    from contracts as c
                    ' . $params['search']['joins'] . '
                    where c.is_confirmed = true ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    ) as confirmed
                    ,(
                    select
                        count(c.id) published 
                        from contracts as c
                        ' . $params['search']['joins'] . '
                    where c.is_published = true and c.is_confirmed = true ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    ) as published
        ');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    public function completedContractsPaymentsPercentage($params)
    {
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $contractCompleteStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');
        $contract_status_id = $contractCompleteStatus['id'];
        $data['plan_and_actual'] = DB::select('
                    select distinct
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                            sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        )   as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                     join (
                            select 
                                csl.id, csl.contract_id, csl.status_id 
                            from contract_status_logs as csl
                            join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                            on csl.id = max_value.max_id
                        ) as csl_max
                          on csl_max.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where  1 = 1 ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    and csl_max.status_id = ' . $contract_status_id . '
                    
            ')[0];

        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                            sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                            if(
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                            1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                     join (
                            select 
                                csl.id, csl.contract_id, csl.status_id 
                            from contract_status_logs as csl
                            join ( select contract_id, max(id) as max_id from contract_status_logs group by contract_id) as max_value    	 
                            on csl.id = max_value.max_id
                        ) as csl_max
                          on csl_max.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1 ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                    and csl_max.status_id = ' . $contract_status_id . '
                    group by cr.iteration_count;   
            ');
        return $data;
    }

    public function vendorsSignedContractsValueAndNumberCount($params)
    {
        $data = DB::select('
                  select
                com.license_number,
                count(c.id) as contract_count,
                round(sum(
                        ((ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)) * ifnull(com.percentage_contract_share, 100) / 100
                    ) / 1000000 , 4) as total_contracts_amount
            from companies as com
            join company_general_informations as cgi
                on cgi.id = com.company_general_information_id
            join contracts as c
                on cgi.contract_id = c.id
            left join contract_details as cd
                on c.id = cd.contract_id
            left join contract_details_verifications cdv
                on cd.id = cdv.contract_detail_id
                and cdv.is_approved = true
            left join (
                 select 
                        (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                        a.contract_id as contract_id
                    from amendments as a
                    left join cost_amendments as ca 
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    group by  a.contract_id 
            
               ) as amendment
                      on c.id = amendment.contract_id
            left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
            ' . $params['search']['joins'] . '
            where com.license_number is not null ' . $params['contractWhereCondition'] . '
            ' . $params['search']['query_string'] . '
            group by com.license_number            
        ');
        $companies = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company');
        foreach ($companies as $company) {
            foreach ($data as &$companyContracts) {
                if ($company['licence_number'] === $companyContracts->license_number) {
                    $companyContracts->name_da = $company['name_da'];
                    break;
                }

            }
        }
        return $data;
    }

    public function pesContractsValuePercentageCount($params)
    {
        $data['grand_total_cost_of_contracts'] = DB::select
        ('
                   select	
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as grand_total_cost
                  from contracts as c 
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
              ' . $params['search']['joins'] . '
            where 1 = 1 ' . $params['contractWhereCondition'] . '
              ' . $params['search']['query_string'] . '
        ');

        $data['total_cost_per_procurement_entity'] = DB::select
        ('
             select	
                   sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) as  total_cost, 
                  c.procurement_entity_id
                  from contracts as c 
                  left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                  ) as amendment
                    on c.id = amendment.contract_id
                  join contract_details as cd
                      on c.id = cd.contract_id
                  join contract_details_verifications cdv
                      on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                  left join provisional_sum_and_contingencies as psc
                  		on cd.id = psc.contract_detail_id
                  ' . $params['search']['joins'] . '
                  where 1 = 1 ' . $params['contractWhereCondition'] . '
                  ' . $params['search']['query_string'] . '
                 group by c.procurement_entity_id
        ');
        for ($i = 0; $i < sizeof($data['total_cost_per_procurement_entity']); $i++) {
            $data['total_cost_per_procurement_entity'][$i]->percentage =
                $data['grand_total_cost_of_contracts'][0]->grand_total_cost == 0 ? 0 :
                    round(
                        ($data['total_cost_per_procurement_entity'][$i]->total_cost * 100) /
                        $data['grand_total_cost_of_contracts'][0]->grand_total_cost, 2
                    );
        }
        return $data;
    }

    public function pesNewContractsVsTransferredContractsCount($params)
    {
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $year = $current_jalali_date[0];
        if ($current_jalali_date[1] > 9) {
            $year = $current_jalali_date[0] + 1;
        }


        $fiscalStartArray = JDateTime::toGregorian($year - 1, 10, 1);
        $fiscalEndArray = JDateTime::toGregorian($year, 9, 30);
        $fiscalStartDate = $fiscalStartArray ? $fiscalStartArray[0] . '-' . $fiscalStartArray[1] . '-' . $fiscalStartArray[2] : null;
        $fiscalEndDate = $fiscalEndArray ? $fiscalEndArray[0] . '-' . $fiscalEndArray[1] . '-' . $fiscalEndArray[2] : null;

        $statuses_id = DropDowns::getBySlugs(
            config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
            ['contract-close-out', 'cancelled']);
        $idQuery = '';
        foreach ($statuses_id as $item) {
            $idQuery .= $item['id'] . ',';
        }
        $idQuery = substr($idQuery, 0, -1);

        $data = DB::select
        ('
             select
                count(case when cp.actual_start_date < "' . $fiscalStartDate . '"  and ( cs.status_id not in (' . $idQuery . ') or cs.status_id is null) then 1 end) as transferred_contracts_count,
                count(case when cp.actual_start_date between "' . $fiscalStartDate . '" and "' . $fiscalEndDate . '" then 1 end) as new_contracts_count,
                c.procurement_entity_id
            from contracts as c
            inner join contract_progresses as cp
                on c.id = cp.contract_id
            join (
                select 
                    csl.id, csl.contract_id, csl.status_id 
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                    on csl.id = max_value.max_id
            ) as cs
                on cs.contract_id = cp.contract_id
            ' . $params['search']['joins'] . '
            where 1 = 1 
            ' . $params['contractWhereCondition'] . '
            ' . $params['search']['query_string'] . '
            group by c.procurement_entity_id       
        ');
        return $data;
    }

    public function departmentSpecialistsAnalyzedContractsCount($params)
    {
        $data = DB::select('
                    select
                           cs.specialist_id,
                              round(ifnull(count(case when noc.is_confirmed = true then 1 end), 0) * 
                              100 / 
                              if(count(distinct cs.contract_id) , count(distinct cs.contract_id) , 1), 2) as total_contract_percentage
                           
                        from contract_specialists as cs
                        left join contracts as c 
                           on cs.contract_id = c.id
                        left join npa_other_comments as noc
                           on cs.id = noc.contract_id
                        ' . $params['search']['joins'] . '
                        where  1 = 1 and cs.specialist_id is not null
                        ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by cs.specialist_id');


        $userIds = [];
        foreach ($data as $item) {
            array_push($userIds, $item->specialist_id ? $item->specialist_id : 0);
        }
        $users = Helper::getUsersByIds($userIds);
        foreach ($data as &$item) {
            foreach ($users as $user) {
                if ($item->specialist_id == $user->id) {
                    $item->full_name = $user->full_name;
                }
            }
        }
        return $data;
    }

    public function departmentContractsStatusCount($params)
    {
        $published_contracts_by_sector_to_website = DB::select('select 
                                                                  ifnull(count(distinct c.id),0) as published_contracts_by_sector_to_website 
                                                                from contracts as c
                                                                ' . $params['search']['joins'] . '
                                                                where  c.is_published = true 
                                                                ' . $params['contractWhereCondition'] . '
                                                                ' . $params['search']['query_string'] . ' ');
        $data['published_contracts_by_sector_to_website'] = $published_contracts_by_sector_to_website[0]->published_contracts_by_sector_to_website;

        $approved_contracts_by_contract_manager = DB::select('select 
                                                                ifnull(count(distinct c.id),0) as approved_contracts_by_contract_manager 
                                                              from contracts as c
                                                              ' . $params['search']['joins'] . '
                                                              where  c.is_confirmed = true 
                                                              ' . $params['contractWhereCondition'] . '
                                                              ' . $params['search']['query_string'] . ' ');
        $data['approved_contracts_by_contract_manager'] = $approved_contracts_by_contract_manager[0]->approved_contracts_by_contract_manager;

        $saved_contracts = DB::select('select 
                                          ifnull(count(distinct c.id),0) as saved_contracts_by_sector 
                                        from contracts as c
                                          ' . $params['search']['joins'] . '
                                        where 1 = 1 
                                          ' . $params['contractWhereCondition'] . '
                                          ' . $params['search']['query_string'] . ' ');
        $data['saved_contracts_by_sector'] = $saved_contracts[0]->saved_contracts_by_sector;

        $sector_contract_amount = DB::select('select 
                                                    ifnull(count(distinct c.id),0) as sector_contract_amount 
                                                from contracts as c
                                                ' . $params['search']['joins'] . '
                                                where 1 = 1  ' . $params['contractWhereCondition'] . '
                                                ' . $params['search']['query_string'] . ' ');
        $data['sector_contract_amount'] = $sector_contract_amount[0]->sector_contract_amount;
        return $data;
    }

    public function contractsAmendmentsCountByType($params)
    {
        $data = DB::select('
            select
              count(c.id) as number_of_contracts, a.type_id
            from contracts as c
            inner join amendments as a
            on c.id = a.contract_id
            ' . $params['search']['joins'] . '
            where a.is_approved = true
            ' . $params['contractWhereCondition'] . '
            ' . $params['search']['query_string'] . '
            group by a.type_id');
        return $data;
    }

    public function contractManagerContractsAmendmentsCountByType($params)
    {
        $data = DB::select('
                select
                  count(distinct c.id) as contracts_number,
                  a.type_id
                from contracts as c
                join amendments as a
                  on c.id = a.contract_id
                join contract_details as cd
                        on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                  ' . $params['search']['joins'] . '
                where a.is_approved = true
                and  1 = 1   ' . $params['contractWhereCondition'] . '
                ' . $params['search']['query_string'] . '
                group by a.type_id');
        return $data;
    }

    public function contractManagersAssignedContractsStatusPercentage($params)
    {
        $contract = Contract::select('procurement_entity_id')->where('contract_manager_id', $params['user_id'])->first();
        if (!isset($contract['procurement_entity_id'])) {
            return [];
        }
        $data = DB::select('
                select 
                    t1.total_assigned_contracts_to_contract_manager, 
                    t1.total_inserted_contracts, 
                    t1.total_assigned_contracts_to_contract_manager -  t1.total_inserted_contracts as total_not_inserted_contracts,
                    t2.total_contracts_belongs_to_an_award_authority, 
                    t3.total_confirmed_contracts,
                    t4.total_published_contracts
                from
                (
                    select 
                        count(c.id) as total_assigned_contracts_to_contract_manager,
                        count(cgiv_for_true.id) as total_inserted_contracts
                    from contracts as c 
                    left join contract_general_information_verifications as cgiv_for_true
                        on cgiv_for_true.contract_id = c.id and cgiv_for_true.is_approved = true
                    ' . $params['search']['joins'] . '
                    where  1 = 1  ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
                ) as t1,
                (
                    select 
                        count(c.id) as total_contracts_belongs_to_an_award_authority
                    from contracts as c
                     ' . $params['search']['joins'] . '
                    where c.procurement_entity_id = ' . $contract['procurement_entity_id'] . '
                    ' . $params['search']['query_string'] . '
                ) as t2,
                (
                    select 
                        count(c.id) as total_confirmed_contracts
                    from contracts as c
                    ' . $params['search']['joins'] . '
                    where 1 = 1  ' . $params['contractWhereCondition'] . ' and c.is_confirmed = true 
                    ' . $params['search']['query_string'] . '
                ) as t3,
                (
                    select
                        count(c.id) as total_published_contracts
                    from contracts as c 
                    ' . $params['search']['joins'] . '
                    where 1 = 1  ' . $params['contractWhereCondition'] . ' and c.is_published = true 
                    ' . $params['search']['query_string'] . '
                ) as t4
        ');
        return $data;
    }

    public function contractManagerAssignedContractsCount($params)
    {
        $contract = Contract::select('procurement_entity_id')->where('contract_manager_id', $params['user_id'])->first();
        if (!isset($contract['procurement_entity_id'])) {
            return [];
        }
        $data = DB::select('
                         select
                         c.contract_manager_id, 
                         count(distinct c.id) as contracts_count
                         from contracts as c
                            ' . $params['search']['joins'] . '
                            where c.contract_manager_id is not null  and c.procurement_entity_id = ' . $contract['procurement_entity_id'] . '
                            ' . $params['search']['query_string'] . '
                            group by c.contract_manager_id');

        $userIds = [];
        foreach ($data as $item) {
            array_push($userIds, $item->contract_manager_id ? $item->contract_manager_id : 0);
        }
        $users = Helper::getUsersByIds($userIds);
        foreach ($data as &$item) {
            foreach ($users as $user) {
                if ($item->contract_manager_id == $user->id) {
                    $item->full_name = $user->full_name;
                }
            }
        }
        return $data;
    }

    public function transferredContractsToContractManagerStatusCount($params)
    {
        $data = DB::select('
                select 
                    c.procurement_type_id as id,
                    count(case when cgiv.is_approved = false then 1 end) as unsaved_contracts,
                    count(case when c.is_published = true then 1 end) as published_contracts_count,
                    count(case when c.is_confirmed = true then 1 end) as confirme_contracts,
                    count(case when cgiv.is_approved = true then 1 end) as saved_contracts
                    
                from contracts as c
                left join contract_general_information_verifications as cgiv
                on cgiv.contract_id = c.id
                 ' . $params['search']['joins'] . '
                where  1 = 1  ' . $params['contractWhereCondition'] . '
                 ' . $params['search']['query_string'] . '
                group by c.procurement_type_id
        ');
        return $data;
    }

    public function provincesContractsValueCountAndStatus($params)
    {
        $data['no_shared_contracts'] = DB::select('
                select 
                    p.id as province_id, 
                    cs.status_id as contract_status_id, 
                    count(distinct(c.id)) as contracts_count,
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1) 
                    ) as total_contract_value
                from contracts as c 
                join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = c.id
                left join contract_details as cd 
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $params['search']['joins'] . '
                where 1=1 ' . $params['search']['query_string'] . '
                ' . $params['contractWhereCondition'] . '
                and c.id not in
                    (
                    select 
                        c.id as contract_id
                    from contracts as c 
                    join domestic_contract_execution_locations as dc 
                        on dc.contract_id = c.id 
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                    )
                group by p.id, cs.status_id;
        ');

        $data['shared_contracts'] = DB::select('
            select province_id, 
            sum(shared_contracts_count) as shared_contracts_count, 
            sum(total_contract_shared_value) as total_contract_shared_value 
            from (
                select 
                    provinces_contracts.province_id, 
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(join_c.exchange_rate, join_c.exchange_rate, 1) 
                    ) as total_contract_shared_value,
                    count(provinces_contracts.province_id) as shared_contracts_count
                from 
                    (
                        select distinct
                            p.id as province_id,
                            c.id as contract_id
                        from contracts as c
                        join domestic_contract_execution_locations as dcel
                            on dcel.contract_id = c.id
                        join temp_districts as d 
                            on d.id = dcel.district_id
                        join temp_provinces as p
                            on p.id = d.temp_province_id
                        
                    ) as provinces_contracts
                left join contract_details as cd
                    on cd.contract_id = provinces_contracts.contract_id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on provinces_contracts.contract_id = amendment.contract_id
                join contracts as join_c
                    on join_c.id = provinces_contracts.contract_id 
                where provinces_contracts.contract_id not in (
                    select 
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dcel
                        on dcel.contract_id = c.id
                    join temp_districts as d 
                        on d.id = dcel.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    ' . $params['search']['joins'] . '
                    where 1 = 1 ' . $params['search']['query_string'] . '
                    ' . $params['contractWhereCondition'] . '
                    group by c.id
                    having count(distinct p.id) = 1
                )
                group by provinces_contracts.province_id, provinces_contracts.contract_id) as temp_table
            group by province_id;
        ');

        return $data;

    }

    public function contractsCountBasedOnDonor($params)
    {
        $data = DB::select('
                        select
                          count(c.id) as number_of_contracts,
                          cd.donor_id
                        from contracts as c
                        join contract_donors as cd
                          on c.id = cd.contract_id
                          ' . $params['search']['joins'] . '
                        where  1 = 1   ' . $params['contractWhereCondition'] . '
                        ' . $params['search']['query_string'] . '
                        group by cd.donor_id
                                ');

        return $data;
    }

    public function contractsValueBasedOnDonor($params)
    {
        $data = DB::select('select
                          round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            ) / 1000000, 4) as total_contracts_value,cdo.donor_id as donor_id
                          from contracts as c
                          join contract_donors as cdo
                            on c.id = cdo.contract_id
                          join contract_details as cd
                              on c.id = cd.contract_id
                          join contract_details_verifications cdv
                              on cd.id = cdv.contract_detail_id
                              and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                              on psc.contract_detail_id = cd.id
                          left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                group by  a.contract_id 
                        
                            ) as amendment
                                on c.id = amendment.contract_id
                          ' . $params['search']['joins'] . '
                          where 1 = 1 ' . $params['contractWhereCondition'] . '
                          ' . $params['search']['query_string'] . '
                          group by cdo.donor_id;
                        ');

        return $data;
    }

    public function contractsImplementationsChallengesCount($params)
    {
        $data = DB::select('
                  select 
                    car_distinct.category_id,
                    count(car_distinct.contract_id) contracts_count,
                    round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amount
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                   ) as amendment
                          on c.id = amendment.contract_id
               left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
               join (
                    select distinct 
                      inner_car.category_id,
                      inner_car.contract_id
                    from challenges_and_remarks as inner_car
                    where inner_car.is_approved = true
                    group by inner_car.category_id, inner_car.contract_id, inner_car.id
                 )as car_distinct 
                    on car_distinct.contract_id = c.id
                 ' . $params['search']['joins'] . '
               where 1 = 1    ' . $params['contractWhereCondition'] . '
               ' . $params['search']['query_string'] . ' 
               group by car_distinct.category_id
                  
                  
                  ');
        return $data;
    }

    public function contractsAmendmentsTypesCountBasedOnProcurementType($params)
    {
        $data = DB::select('
        select 
            c.procurement_type_id as procurement_type_id,
            a.type_id as amendment_type,
            count(a.id) as amendment_count
        from contracts as c
        join amendments as a 
            on a.contract_id = c.id
            and a.is_approved = true 
        ' . $params['search']['joins'] . '
        where  1 = 1   ' . $params['contractWhereCondition'] . '
         ' . $params['search']['query_string'] . '
        group by c.procurement_type_id, a.type_id;
       ');
        return $data;
    }

    public function awardAuthorityContractsTimeAndCostAmendmentsPercentageVsOriginTimeAndCost($params)
    {
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $timeAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-amendment');
        $timeAndCostAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-and-cost-amendment');
        $data = DB::select('
                    select
                        (
                          sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)))
                        )  as total_value_percentage,
                        (
                          sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)))
                        )  as cost_amendment_percentage,
                        (
                          sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))
                        ) as total_time_percentage,
                        (
                          sum(ifnull(amendment_time.days, 0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))
                        ) as time_amendment_percentage

                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    join contract_progresses as cp
                        on c.id = cp.contract_id
                    join contract_progress_verifications as cpv
                        on cp.id = cpv.contract_progress_id
                        and cpv.is_approved = true
                     left join (
                     select
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id

                    ) as amendment_cost
                       on amendment_cost.contract_id = c.id

                    join (
                        select
                             a.contract_id,
                             GREATEST(
                                      ifnull(
                                          datediff(
                                              (select amended_end_date from time_amendments where amendment_id = a.id)
                                              ,
                                              (select planned_end_date from contract_details where contract_id = a.contract_id)
                                          ),0),
                                      ifnull(
                                            datediff(
                                                (select amended_end_date from time_and_cost_amendments where amendment_id = a.id)
                                                ,
                                                (select planned_end_date from contract_details where contract_id = a.contract_id)
                                            ),0)
                              ) as days
                        from amendments as a
                        join (
                            SELECT
                            contract_id, max(id) as max_id
                            FROM amendments
                            where type_id in (' . $timeAmendment['id'] . ',' . $timeAndCostAmendment['id'] . ') and is_approved =true
                            group by contract_id
                            ) as max_value
                            on max_value.max_id = a.id
                        group by a.contract_id, a.id
                    ) as amendment_time
                        on amendment_time.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1  ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
        ');
        $data = isset($data[0]) ? $data[0] : $data;
        return $data;
    }

    public function contractManagerContractsTimeAndCostAmendmentsPercentageVsOriginTimeAndCost($params)
    {
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $timeAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-amendment');
        $timeAndCostAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-and-cost-amendment');
        $data = DB::select('
                    select 
                        round(
                          sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(if(amendment_cost.amount, amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        , 2)  as total_value_percentage,
                        round(
                          sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        , 2)  as cost_amendment_percentage,
                        round(
                            sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) * 100 /
                            (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        , 2) as total_time_percentage, 
                        round(
                            sum(ifnull(amendment_time.days, 0)) * 100 /
                            (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        , 2) as time_amendment_percentage 
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id 
                        and cdv.is_approved = true
                    left join contract_progresses as cp
                        on c.id = cp.contract_id
                    left join contract_progress_verifications as cpv
                        on cp.id = cpv.contract_progress_id
                        and cpv.is_approved = true
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                    ) as amendment_cost
                       on amendment_cost.contract_id = c.id
                    left join (
                        select 
                             a.contract_id,
                             GREATEST(
                                      ifnull(
                                          datediff(
                                              (select amended_end_date from time_amendments where amendment_id = a.id)
                                              , 
                                              (select planned_end_date from contract_details where contract_id = a.contract_id)
                                          ),0), 
                                      ifnull(
                                            datediff(
                                                (select amended_end_date from time_and_cost_amendments where amendment_id = a.id)
                                                , 
                                                (select planned_end_date from contract_details where contract_id = a.contract_id)
                                            ),0)
                              ) as days
                        from amendments as a 
                        join ( 
                            SELECT 
                            contract_id, max(id) as max_id 
                            FROM amendments 
                            where type_id in (' . $timeAmendment['id'] . ',' . $timeAndCostAmendment['id'] . ') and is_approved =true
                            group by contract_id
                            ) as max_value
                            on max_value.max_id = a.id
                        group by a.contract_id, a.id
                    ) as amendment_time
                        on amendment_time.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1  ' . $params['contractWhereCondition'] . '
                    ' . $params['search']['query_string'] . '
        ');
        if (isset($data[0])) {
            $data = $data[0];
        }
        return $data;
    }

    public function workInProgressContractsPaymentsValue($params)
    {
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $data = [];
        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select 
                        round(sum(ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4)  as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                        
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and  cpv.is_approved = true 
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1
                        ' . $params['contractWhereCondition'] . ' 
                         ' . $params['search']['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and  
                        cp.year in  ( ' . $value['year'] . ' ) and 
                        cp.month_id in(' . $value['months'] . ')
        ')[0];


            $data['re_plan_and_actual'][$key] = DB::select('
                    select 
                        cr.iteration_count as replan_number,
                        round(sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $params['search']['joins'] . '
                    where 1 = 1 ' . $params['contractWhereCondition'] . ' 
                         ' . $params['search']['query_string'] . ' and 
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in (' . $value['year'] . ') and 
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');
        }
        return $data;
    }


}
