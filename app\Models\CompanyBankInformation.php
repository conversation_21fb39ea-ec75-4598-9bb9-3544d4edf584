<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyBankInformation extends Model
{
    protected $guarded = ['id'];
    public static $fields = [
        'account_name',
        'account_number',
        'swift_code',
        'email',
        'phone_number',
        'is_approved',
        'is_confirmed',
        'bank_id',
        'company_id',
    ];

    public function company()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Company');
    }
}
