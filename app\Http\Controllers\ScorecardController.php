<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\ChallengesAndRemarks;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPP;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\DomesticContractExecutionLocation;
use NPA\ACPMS\Models\ForeignContractExecutionLocation;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class ScorecardController extends Controller
{

    public function contractGeneralInformation(Request $request)
    {
        $data = [];
        $contractId = $request->query('contract_id');
        $contract = Contract::select('id', 'currency_id', 'procurement_type_id')
            ->where('id', $contractId)
            ->with(
                ['contract_details' => function ($query) {
                    $query->select('id', 'contract_id', 'estimated_value', 'actual_value');
                    $query->whereHas('contract_detail_verification', function ($q) {
                        $q->where('is_approved', 1);
                    });
                }, 'contract_details.contract_detail_verification' => function ($query) {
                    $query->where('is_approved', 1);
                }, 'amendments' => function ($query) {
                    $query->select('id', 'contract_id');
                    $query->where('is_approved', 1);

                }, 'amendments.time_and_cost_amendment' => function ($query) {
                    $query->select('id', 'amendment_id', 'amendment_amount');

                }, 'amendments.cost_amendment' => function ($query) {
                    $query->select('id', 'amendment_id', 'amendment_amount');

                }, 'contract_status_logs' => function ($query) {
                    $query->select('id', 'contract_id', 'status_id');
                    $query->orderBy('id', 'desc')->first();
                }])->first();

        $data['currency_id'] = $contract['currency_id'];
        $data['procurement_type_id'] = $contract['procurement_type_id'];
        $data['value_difference'] = $contract['contract_details'] ? $contract['contract_details']['estimated_value'] - $contract['contract_details']['actual_value'] : null;
        $data['current_status'] = $contract['contract_status_logs'] !== [] && isset($contract['contract_status_logs'][0]) ? $contract['contract_status_logs'][0]['status_id'] : null;
        $data['value'] = $contract['contract_details'] ? $contract['contract_details']['actual_value'] : null;


        return response()->json($data);
    }


    public function contractPlanning(Request $request)
    {
        $contractId = $request->query('contract_id');
        $count = 0;
        $data = [];
        $contract = Contract::select('id')
            ->where('id', $contractId)->first();
        $contractApproval = $contract->contract_approval;
        if ($contractApproval) {
            $count += Attachment::where('foreign_key', $contractApproval['id'])
                ->where('table_name', 'contract_approvals')
                ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
        }
        $contractDetails = $contract->contract_details;
        if ($contractDetails) {
            $count += Attachment::where('foreign_key', $contractDetails['id'])
                ->where('table_name', 'contract_details')
                ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
        }
        $subContract = $contract->subcontract;
        if (isset($subContract[0])) {
            $count += Attachment::where('foreign_key', $subContract[0]['id'])
                ->where('table_name', 'subcontracts')
                ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
        }
        $contractPlanningAdvances = $contract->contract_planningAdvance;
        if ($contractPlanningAdvances) {
            $count += Attachment::where('foreign_key', $contractPlanningAdvances['id'])
                ->where('table_name', 'contract_planning_advances')
                ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
        }
        if ($contract) {
            $count += Attachment::where('foreign_key', $contract['id'])
                ->where('table_name', 'contracts')
                ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
        }
        $companyGeneralInfo = $contract->company_general_information;
        if ($companyGeneralInfo) {
            $company = $companyGeneralInfo->companies;
            if (isset($company[0])) {
                $count += Attachment::where('foreign_key', $company[0]['id'])
                    ->where('table_name', 'companies')
                    ->groupBy('field_name', 'table_name', 'foreign_key')->pluck('field_name')->count();
            }
        }
        // total of attachment in contract planning tab is 19
        $data['contract_planning_file_upload_percentage'] = (100 * $count) / 19;


        $data['contract_details'] = ContractDetails
            ::select('planned_start_date', 'planned_end_date')
            ->where('contract_id', $contractId)
            ->first();
        $data['location']['domestic'] = [];
        $data['location']['foreign'] = [];
        $data['location']['domestic'] = DomesticContractExecutionLocation::select('district_id', 'village')->where('contract_id', $contractId)->get();
        foreach ($data['location']['domestic'] as &$district) {

            $districtData = DropDowns::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/district', $district->district_id);
            unset($districtData['province']);
            $district->district = $districtData;
        }
        $data['location']['foreign'] = ForeignContractExecutionLocation::select('foreign_country_location', 'foreign_city_location')->where('contract_id', $contractId)->get();
        $data['re_plan'] = ContractRePlanningDateFAAPP::select('iteration_count')->where('contract_id', $contractId)->whereHas('contract_re_plan_verification', function ($query) {
            $query->where('is_approved', 1);
        })->orderBy('id', 'desc')->first();
        return response()->json($data);
    }


    public function contractProgress(Request $request)
    {
        $contractId = $request->query('contract_id');
        $data = [];
        $data['currency'] = Contract::select('currency_id')->where('id', $contractId)->first();
        $data['total_physical_progress'] = ContractProgressPayment::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->sum('physical_progress_percentage');
        $data['total_payments'] = ContractProgressPayment::where('contract_id', 1)
            ->where('is_approved', 1)
            ->sum('amount');
        $contractDetails = ContractDetails::select('actual_value')->where('contract_id', $contractId)->first();

        $data['current_contract_value'] = ($data['total_physical_progress'] / 100) * $contractDetails['actual_value'];
        unset ($data['total_payments']);


        $spi = DB::select('
            select ifnull(replan.index_value,plan.index_value) as index_value
            from contracts 
            join (
                select 
                    cpf.contract_id,
                    ifnull(sum(ifnull(cpp.physical_progress_percentage, 0)) / sum(ifnull(cpf.physical_progress_percentage, 0)), 0) as index_value
                from contract_planning_finance_affairs_and_physical_progresses as cpf
                left join contract_progress_payments as cpp
                    on cpf.contract_id = cpp.contract_id
                    and cpf.year = cpp.year
                    and cpf.month_id = cpp.month_id
                    and cpp.is_approved = true
                group by cpf.contract_id
            ) as plan
                on plan.contract_id = id
        
        
        
            left join(
                select 
                    crd.contract_id,
                    ifnull(sum(ifnull(cpp.physical_progress_percentage, 0)) / sum(ifnull(crppp.physical_progress_percentage, 0)), 0) as index_value
                from contract_re_planning_finance_affairs_and_physical_progresses as crppp
                join(
                        select inner_crd.contract_id, max(inner_crd.id) as max_id 
                        from contract_re_planning_date_f_a_a_p_ps as inner_crd
                        join contract_re_planning_date_f_a_a_p_p_verifications as crdv
                            on inner_crd.id = crdv.contract_re_planning_date_f_a_a_p_p_id
                            and crdv.is_approved = true
                        group by inner_crd.contract_id
                ) as crd
                    on crd.max_id = crppp.contract_re_planning_date_f_a_a_p_p_id
                left join contract_progress_payments as cpp
                    on crd.contract_id = cpp.contract_id
                    and crppp.year = cpp.year
                    and crppp.month_id = cpp.month_id
                    and cpp.is_approved = true
                group by crd.contract_id
            ) as replan
                on replan.contract_id = id
            where id = ?
        ', [$contractId]);

        $dpi = DB::select('
                select 
                    (ifnull(cpp_join.amount, 0)) /
                    if(
                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) * 
                        ((cpp_join.percentage) / 100)
                        ,
                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) * 
                        ((cpp_join.percentage) / 100)
                        , 1
                    )as index_value
                from contract_details as cd
                join contract_details_verifications cdv
                    on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                left join (
                            select 
                                (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                a.contract_id as contract_id
                            from amendments as a
                            left join cost_amendments as ca 
                                on ca.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            where a.is_approved = true
                            group by  a.contract_id 
                        
                ) as amendment
                    on cd.contract_id = amendment.contract_id
                left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id	  
                join (
                            select
                                cpp.contract_id,
                                sum(cpp.amount) as amount,
                                sum(cpp.physical_progress_percentage) as percentage
                                
                            from contract_progress_payments as cpp
                            where cpp.is_approved = true
                            group by cpp.contract_id
            
                ) as cpp_join
                    on cd.contract_id = cpp_join.contract_id
                where cd.contract_id = ?
        ', [$contractId]);

        $data['cost_performing_index'] = isset($dpi[0]) ? $dpi[0]->index_value : 0;
        $data['schedule_performing_index'] = isset($spi[0]) ? $spi[0]->index_value : 0;


        return response()->json($data);
    }


    public function contractAmendment(Request $request)
    {
        $data = [];
        $contractId = $request->query('contract_id');

        $contractDetails = ContractDetails::where('contract_id', $contractId)->first();
        $actualAmount = $contractDetails['actual_value'];
        $amendments = Amendment::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->orderBy('id', 'asc')
            ->get();

        $timeAndCostAmendmentId = $this->getAmendmentTypeId('time-and-cost-amendment');
        $costAmendmentId = $this->getAmendmentTypeId('cost-amendment');
        $timeAmendmentId = $this->getAmendmentTypeId('time-amendment');
        $amendedAmount = 0;
        foreach ($amendments as $amendment) {
            if (in_array($amendment['type_id'], [$timeAndCostAmendmentId, $costAmendmentId])) {
                $amendment['type_id'] === $costAmendmentId ?
                    $amendedAmount += CostAmendment::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amendment_amount'] :

                    $amendedAmount += TimeAndCostAmendment::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amendment_amount'];
            }
        }
        $data['cost_amendment_percentage'] = $actualAmount ? (100 * $amendedAmount) / $actualAmount : null;
        $data['remaining_cost_amendment_percentage'] = 25 - $data['cost_amendment_percentage'];
        $data['amendment_count'] = Amendment::where('contract_id', $contractId)->where('is_approved', 1)->count();
        $totalContractDateInMilliseconds = null;
        if ($contractDetails['planned_end_date'] && $contractDetails['planned_start_date']) {
            $totalContractDateInMilliseconds = Carbon::createFromFormat('Y-m-d', $contractDetails['planned_end_date'])->timestamp -
                Carbon::createFromFormat('Y-m-d', $contractDetails['planned_start_date'])->timestamp;
        }
        $amendedEndDate = null;
        foreach ($amendments as $amendment) {
            if (in_array($amendment['type_id'], [$timeAndCostAmendmentId, $timeAmendmentId])) {
                $amendment['type_id'] === $costAmendmentId ?
                    $amendedEndDate = TimeAmendment::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'] :

                    $amendedEndDate = TimeAndCostAmendment::where('amendment_id', $amendment['id'])
                        ->orderBy('id', 'desc')
                        ->first()['amended_end_date'];
            }
        }
        $totalContractAmendedDateInMilliseconds = 0;
        if ($amendedEndDate && $contractDetails['planned_end_date']) {
            $totalContractAmendedDateInMilliseconds = Carbon::createFromFormat('Y-m-d', $amendedEndDate)->timestamp -
                Carbon::createFromFormat('Y-m-d', $contractDetails['planned_end_date'])->timestamp;
            if ($totalContractAmendedDateInMilliseconds < 0) {
                $totalContractAmendedDateInMilliseconds = 0;
            }
        }
        $data['time_amended_percentage'] = $totalContractDateInMilliseconds ? (100 * $totalContractAmendedDateInMilliseconds) / $totalContractDateInMilliseconds : null;


        return response()->json($data);

    }

    public function getAllAmendmentType()
    {
        $path = 'api/dropDown/amendmentType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function getAmendmentTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->getAllAmendmentType(), $slug)['id'];
    }


    public function contractChallengesAndRemarks(Request $request)
    {
        $contractId = $request->query('contract_id');
        $openStatusId = null;
        $closeStatusId = null;
        foreach ($this->getAllChallengesAndRemarksStatus() as $status) {
            $status['slug'] === 'open' ?
                $openStatusId = $status['id'] :
                $closeStatusId = $status['id'];

        }
        $resolvedResolutionId = null;
        foreach ($this->getAllChallengesAndRemarksResolution() as $resolution) {
            if ($resolution['slug'] === 'solved') {
                $resolvedResolutionId = $resolution['id'];
            }

        }
        $allChallengesCount = ChallengesAndRemarks::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->count();

        $data['open_challenges_count'] = ChallengesAndRemarks::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->where('status_id', $openStatusId)
            ->count();
        $data['closed_challenges_count'] = ChallengesAndRemarks::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->where('status_id', $closeStatusId)
            ->count();
        $closeChallengesResolvedResolutionCount = ChallengesAndRemarks::where('contract_id', $contractId)
            ->where('is_approved', 1)
            ->where('status_id', $closeStatusId)
            ->where('resolution_id', $resolvedResolutionId)
            ->count();
        $data['closed_challenges_resolved_resolution_percentage'] = $allChallengesCount ? ($closeChallengesResolvedResolutionCount * 100) / $allChallengesCount : null;
        $data['resolvable_challenges_percentage'] = $allChallengesCount ? ($data['open_challenges_count'] * 100) / $allChallengesCount : null;
        return response()->json($data);
    }

    public function getAllChallengesAndRemarksStatus()
    {
        $path = 'api/dropDown/challengesRemarksStatus?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getAllChallengesAndRemarksResolution()
    {
        $path = 'api/dropDown/challengesRemarksResolution?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function systemUserGuide()
    {
        $data = [];
        $data['current_version'] = '2.0.0';
        $data['section_count'] = '7';
        $data['sub_section_count'] = '20';
        $data['capacity'] = 'نامحدود';


        return response()->json($data);
    }


}
