<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateParsCheckDbsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pars_check_dbs', function (Blueprint $table) {
            $table->boolean('is_action_plan_uploaded')->nullable()->after('is_main_contract_have_subcontracts');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pars_check_dbs', function (Blueprint $table) {
            $table->dropColumn('is_action_plan_uploaded');
        });
    }
}
