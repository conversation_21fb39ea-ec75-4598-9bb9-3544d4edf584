<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionReturned extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'returned_date',
        'contract_retention_applicable_id'
    ];

    public function contract_retention_applicable()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetentionApplicable');
    }

    public function contract_retention_returned_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractRetentionReturnedLog');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }
}
