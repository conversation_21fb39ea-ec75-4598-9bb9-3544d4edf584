<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateParsRemarks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pars_remarks', function (Blueprint $table) {
            $table->dropColumn('is_contract_started_based_on_conditions');
            $table->dropColumn('is_p_p_p_based_on_agreement_and_plan');
            $table->dropColumn('is_plan_payment_document_uploaded');
            $table->dropColumn('is_p_payment_based_on_schedule_and_docs');
            $table->dropColumn('is_advanced_payment_legally_funded');
            $table->dropColumn('is_p_e_got_action_on_suggestions');
            $table->dropColumn('advanced_payments');
            $table->text('implemented_suggestions')->after('new_suggestion')->nullable();
            $table->text('not_implemented_suggestions')->after('implemented_suggestions')->nullable();
            $table->text('performance_security_percentage_change_reasons')->after('not_implemented_suggestions')->nullable();
            $table->date('advance_payment_guaranty_expiration_date')->after('performance_security_percentage_change_reasons')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pars_remarks', function (Blueprint $table) {
            $table->dropColumn('performance_security_percentage_change_reasons');
            $table->dropColumn('implemented_suggestions');
            $table->dropColumn('not_implemented_suggestions');
            $table->dropColumn('advance_payment_guaranty_expiration_date');
            $table->text('is_contract_started_based_on_conditions')->after('new_suggestion')->nullable();
            $table->text('is_p_p_p_based_on_agreement_and_plan')->after('is_contract_started_based_on_conditions')->nullable();
            $table->text('is_plan_payment_document_uploaded')->after('is_p_p_p_based_on_agreement_and_plan')->nullable();
            $table->text('is_p_payment_based_on_schedule_and_docs')->after('is_plan_payment_document_uploaded')->nullable();
            $table->text('is_advanced_payment_legally_funded')->after('is_p_payment_based_on_schedule_and_docs')->nullable();
            $table->text('is_p_e_got_action_on_suggestions')->after('is_advanced_payment_legally_funded')->nullable();
            $table->text('advanced_payments')->after('is_p_e_got_action_on_suggestions')->nullable();
        });
    }
}
