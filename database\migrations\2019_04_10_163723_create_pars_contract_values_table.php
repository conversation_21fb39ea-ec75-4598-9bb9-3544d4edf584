<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractValuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_values', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->double('estimated_value')->nullable();
            $table->double('actual_value')->nullable();
            $table->double('value_difference')->nullable();
            $table->double('percentage_difference')->nullable();
            $table->double('provisional_sum_and_contingency_value')->nullable();
            $table->boolean('is_provisional_sum_and_contingency_applicable')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_values');
    }
}
