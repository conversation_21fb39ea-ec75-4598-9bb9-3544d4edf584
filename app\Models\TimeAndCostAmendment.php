<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class TimeAndCostAmendment extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'amendment_id',
        'is_above_threshold',
        'is_above_threshold_by_time',
        'current_cost_threshold_value',
        'current_time_threshold_value',
        'amendment_amount',
        'replanning_start_date',
        'amended_end_date',
        'amended_performance_security_end_date',
        'amended_performance_security_amount',
        'amendment_reasons'
    ];
}
