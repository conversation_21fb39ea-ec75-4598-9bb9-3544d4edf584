<?php

namespace NPA\ACPMS\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractSpecialists;
use Throwable;
use Validator;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param $contractId
     * @return \Illuminate\Http\Response
     */


    public function getContractManager($contractId)
    {
        try {
            $procurementEntityId = Contract::find($contractId)->procurement_entity_id;
            $users = Helper::getContractManagers($procurementEntityId);

            return response()->json($users, 200);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function getSpecialist($contractId)
    {
        try {
            $sectorId = Contract::find($contractId)->sector_id;
            $users = Helper::getSpecialists($sectorId);

            return response()->json($users, 200);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }
    }


    public function assignContractManager($contractId, $assignContractManagerId)
    {
        try {
            $temp = Contract::find($contractId);
            Helper::assignContractToUser($contractId, $assignContractManagerId);
            if ($temp['contract_manager_id'] != $assignContractManagerId) {
                $rowsCount = DB::table('contracts')
                    ->where('id', $contractId)
                    ->update(['contract_manager_id' => $assignContractManagerId]);
                if ($rowsCount < 1) {
                    throw Error::exceptionNotFound();
                }
            }
            return response()->json([], 201);
        } catch (Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public function assignSpecialist(Request $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->all();
            $contractSpecialists = ContractSpecialists::where('contract_id', $data['contract_id'])->get();
            ContractSpecialists::where('contract_id', $data['contract_id'])->delete();
            if ($data['specialists'] === '[]') {
                Helper::assignOrRemoveContractAccessToUser([
                    'remove' => json_encode($contractSpecialists->toArray()),
                    'assign' => json_encode([])
                ]);
                DB::commit();
                return response()->json([], 204);
            }
            $specialistData = [];
            foreach ($data['specialists'] as $specialist) {
                array_push($specialistData, [
                    'specialist_id' => $specialist['id'],
                    'contract_id' => $data['contract_id']
                ]);
            }
            ContractSpecialists::insert($specialistData);

            Helper::assignOrRemoveContractAccessToUser([
                'remove' => json_encode($contractSpecialists->toArray()),
                'assign' => json_encode($specialistData)
            ]);
            DB::commit();
            return response()->json([], 201);
        } catch (Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    public function authenticate(Request $request)
    {

        $singleUseToken = $request->query('single_use_token');
        if (!$singleUseToken) {
            $isInitialization = $request->query('is_initialization');
            $session = $request->header('authorization');
            if ($isInitialization && $session) {
                $headers = ['Authorization' => $session];
                $tokenRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/acpms/initiates?key=' . urlencode(config('custom.APP_KEY')), $headers);
                $token = $tokenRequest->headers['authorization'];
                $decodedSession = null;
                if ($token) {
                    $decodedSession = json_decode($session);
                    $decodedSession->token = $token;
                }
                if ($tokenRequest->status_code >= 300 || !$decodedSession) {
                    return response()->json(json_decode($tokenRequest->body), $tokenRequest->status_code);
                }

                return response()->json(json_decode($tokenRequest->body, true), 200, [
                    'Authorization' => json_encode($decodedSession)
                ]);
            } else {
                return response()->json('single_use_token not provided', 400);
            }
        }


        $tokenRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/acpms/token?singleUseToken=' .
            $singleUseToken . '&key=' . urlencode(config('custom.APP_KEY')), [], ['timeout' => 120]);

        if ($tokenRequest->status_code >= 300) {
            return response()->json(json_decode($tokenRequest->body), $tokenRequest->status_code);
        }
        return response()->json(json_decode($tokenRequest->body, true), 200);

    }

    public function logout(Request $request)
    {
        $session = $request->header('authorization');
        $headers = ['Authorization' => $session];
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') . 'api/revoke-session?key=' . urlencode(config('custom.APP_KEY')), $headers);
        if ($request->status_code >= 300) {
            return response()->json(json_decode($request->body), $request->status_code);
        }
        return response()->json([], 200);

    }

    public function getSystems(Request $request)
    {
        $session = $request->header('authorization');
        $headers = ['Authorization' => $session];
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_USM_BASE_URL') .
            'api/systems/list?key=' . urlencode(config('custom.APP_KEY')), $headers);
        if ($request->status_code >= 300) {
            return response()->json(json_decode($request->body), $request->status_code);
        }
        return response()->json(json_decode($request->body, true), 200);
    }

}
