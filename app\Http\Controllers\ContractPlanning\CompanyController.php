<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Company;

class CompanyController extends Controller
{
    private $files = [
        'joint_venture_licence',
        'licence_document'

    ];

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);
        $data['joint_venture_company_role_id'] = '';
        if(isset($data['joint_venture_company_role']['id']) && $data['joint_venture_company_role'] && $data['joint_venture_company_role']['id']){
            $data['joint_venture_company_role_id'] = $data['joint_venture_company_role']['id'];

        }
        try {
            DB::beginTransaction();
            $company = Company::create(array_only($data, Company::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $company, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $company['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param $companyGeneralInformationId
     * @return \Illuminate\Http\Response
     */
    public function show($companyGeneralInformationId)
    {
        $companies = Company::where('company_general_information_id', $companyGeneralInformationId)->get();

        if (!isset($companies) && count($companies) < 1) {
            return response()->json([], 404);
        }
        $queryString = '?search[license_number][match]=in&';
        foreach ($companies as $company) {
            $queryString = $queryString . 'search[license_number][value][]=' . ($company['license_number'] ? $company['license_number'] : '') . '&';
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
        $remoteRequestData = json_decode($remoteRequest->body, true);
        foreach ($companies as $company) {
            foreach ($remoteRequestData as $element) {
                if ($company['license_number'] == $element['license_number']) {
                    $company['full_name'] = $element['full_name'];
                    // $company['name_pa'] = $element['name_pa'];
                    // $company['name_en'] = $element['name_en'];
                    $company['license_number'] = $element['license_number'];
                    $company['tin'] = $element['tin'];
                    $company['v_pin'] = $element['v_pin'];
                }
            }

            $attachments = $company->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index) {
                        array_push($att, $v);
                    }
                }
                $company[$attachments[$key]['field_name']] = $att;
            }


        }

        if ($remoteRequest->status_code > 400) {
            return Error::composeResponse(new Exception($remoteRequest->body));
        } else {
            return response()->json($companies, 200);
        }
    }

    public static function dropDown($companyGeneralInformationId)
    {
        $companies = Company::where('company_general_information_id', $companyGeneralInformationId)->select('id', 'license_number')->get();
        $queryString = '?search[license_number][match]=in&';
        foreach ($companies as $company) {
            $queryString = $queryString . 'search[license_number][value][]=' . $company['license_number'] . '&';
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
        $remoteRequestData = json_decode($remoteRequest->body, true);
        foreach ($companies as $company) {
            foreach ($remoteRequestData as $element) {
                if ($company['license_number'] == $element['license_number']) {
                    $company['full_name'] = $element['full_name'];
                    // $company['name_pa'] = $element['name_pa'];
                    // $company['name_en'] = $element['name_en'];
                    unset($company['license_number']);
                }
            }
        }
        if ($remoteRequest->status_code > 400) {
            return Error::composeResponse(new Exception($remoteRequest->body));
        } else {
            return response()->json($companies, 200);
        }
    }


    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param Company $company
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, Company $company)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);

        try {
            DB::beginTransaction();
            if (!array_key_exists('parent_id', $data)) {
                $data['joint_venture_company_role_id'] = '';
                if(isset($data['joint_venture_company_role']['id']) && $data['joint_venture_company_role'] && $data['joint_venture_company_role']['id']){
                    $data['joint_venture_company_role_id'] = $data['joint_venture_company_role']['id'];

                 }
                // $data['joint_venture_company_role_id'] = $data['joint_venture_company_role']['id'];
                $company->update(array_only($data, Company::$fields));
                foreach ($files as $fieldName => $fileContent) {
                    if ($fileContent !== null) {
                        AttachmentController::saveFile($request, $company, $fieldName, $fileContent);
                    }
                }
            } else {
                unset($data['parent_id']);
                $company->update(array_only($data, Company::$fields));
            }

            DB::commit();
            return response()->json([], 204, [
                'location' => $company['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t, Error::extractCustomErrorCode($t));
        }

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Company $company
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Company $company)
    {
        try {
            $company->delete();
            return response()->json([], 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function searchCompany(Request $request)
    {
        $query = $request->query();
        $queryString = '';

        if (array_has($query, ['full_name']) && $query['full_name'] !== 'null' && $query['full_name']) {
            $queryString .= 'search[full_name][match]=partial&';
            $queryString .= 'search[full_name][value]=' . $query['full_name'] . '&';
        }

        if (array_has($query, ['license_number']) && $query['license_number'] !== 'null' && $query['license_number']) {
            $queryString .= 'search[license_number][match]=full&';
            $queryString .= 'search[license_number][value]=' . $query['license_number'] . '&';
        }

        if (array_has($query, ['tin']) && $query['tin'] !== 'null' && $query['tin']) {
            $queryString .= 'search[tin][match]=full&';
            $queryString .= 'search[tin][value]=' . $query['tin'] . '&';
        }

        if (array_has($query, ['v_pin']) && $query['v_pin'] !== 'null' && $query['v_pin']) {
            $queryString .= 'search[v_pin][match]=full&';
            $queryString .= 'search[v_pin][value]=' . $query['v_pin'] . '&';
        }

        if (strlen($queryString) > 0) {
            $queryString = '?' . substr($queryString, 0, strlen($queryString) - 1);
        } else {
            return Error::responseInsufficientParameters(new Exception('Insufficient parameters'));
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString);
        if ($remoteRequest->status_code > 400) {
            return Error::composeResponse(new Exception($remoteRequest->body));
        } else {
            return response()->json(json_decode($remoteRequest->body, true));
        }
    }

}
