<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CheckIsAuthorizedToChangeThresholdAndNpaidentificationNumber extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec("
            create trigger `is_authorized_change_threshold_npaidentification_number`
            before update on `contracts`
            for each row
            exit_label:begin
                if @is_sa_editing then
                    leave exit_label;
                end if;
                if new.is_above_threshold != old.is_above_threshold or new.contract_number != old.contract_number 
                then
                     signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0006|||not-authorized-to-change-threshold-and-npa_identification_number';
                end if;
            end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getPdo()->exec('drop trigger if exists `is_authorized_change_threshold_npaidentification_number`');
    }
}
