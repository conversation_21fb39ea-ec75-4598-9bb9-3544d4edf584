<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsVendorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_vendors', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('pars_general_informations_id');
            $table->foreign('pars_general_informations_id')
                ->references('id')
                ->on('pars_general_informations')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('vendor_name')->nullable();
            $table->boolean('is_leader')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_vendors');
    }
}
