<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixingDecimalPointInAmounts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('alter table contract_re_planning_finance_affairs_and_physical_progresses modify amount double not null');

        DB::statement('alter table contract_planning_advances modify amount double not null');

        DB::statement('alter table contract_planning_finance_affairs_and_physical_progresses modify amount double not null');

        DB::statement('alter table contract_progress_payments modify amount double not null');

        DB::statement('alter table contract_progress_payments_logs modify amount double not null');

        DB::statement('alter table contract_progress_payment_after_due_dates modify amount double not null');

        DB::statement('alter table contract_performance_securities modify amount double not null');

        DB::statement('alter table contract_progress_defect_liabilities modify amount double not null');

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
