<?php

namespace NPA\ACPMS\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\MongoService;
use Sinergi\BrowserDetector\Browser;

class Logger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    private $startTime;
    private $endTime;

    public function handle($request, Closure $next)
    {
        $this->startTime = microtime(true);
        return $next($request);
    }

    public function terminate($request, $response)
    {
        $this->endTime = microtime(true);
        // $this->log($request, $response);

    }

    protected function log($request, $response)
    {
        $collection = MongoService::getInstance()->log->apiLogs;
        $url = $request->fullUrl();
        $method = $request->getMethod();
        $ipAddress = $this->getOriginalClientIp($request);
        $request_body = $request->post();
        $request_query_string = $request->query();
        $response_content = json_decode($response->getContent(), true);
        $response_status = $response->getStatusCode();
        $username = $request->username;
        $email = $request->email;
        $role = $request->role;
        $timestamp = Carbon::now()->toDateTimeString();
        $browser = new Browser();
        $browser_details = $browser->getName() . $browser->getVersion();
        if (isset($response_content['$oid'])) {
            $response_content['mongoId'] = $response_content['$oid'];
            unset($response_content['$oid']);
        }
        $sector_id = '';
        $procurement_entity_id = '';
        $npa_identification_number = '';
        if (strpos($url, 'project') !== false) {
            $npa_id_search = explode('/', substr($url, strpos($url, 'project') + strlen('project/')));
            if (count($npa_id_search) === 1) {
                $npa_identification_number = $npa_id_search[0];
                while (strpos($npa_identification_number, '%') !== false) {
                    $npa_identification_number = urldecode($npa_identification_number);
                }

                $contract = DB::select('
                            select
                                sector_id, procurement_entity_id
                            from contracts
                            where npa_identification_number = \'' . $npa_identification_number . '\'
                        ');
                if ($contract && $contract[0]) {
                    $sector_id = $contract[0]->sector_id;
                    $procurement_entity_id = $contract[0]->procurement_entity_id;
                }
            }
        }

        $response_content = $this->find_array_key($response_content);
        $data = [
            'url' => $url,
            'ip' => $ipAddress,
            'method' => $method,
            'request_body' => $request_body,
            'request_query_string' => $request_query_string,
            'response_content' => $response_content,
            'response_status' => $response_status,
            'username' => $username,
            'email' => $email,
            'role' => $role,
            'sector_id' => $sector_id,
            'procurement_entity_id' => $procurement_entity_id,
            'npa_identification_number' => $npa_identification_number,
            'browser_details' => $browser_details,
            'timestamp' => $timestamp
        ];
        $collection->insertOne($data);
    }

    function find_array_key($response)
    {
        $response_contents = json_decode(json_encode($response), true);
        if (isset($response_contents) && is_array($response_contents)) {
            foreach ($response_contents as &$response_content) {
                if (isset($response_content['_id']['$oid'])) {
                    $oid = $response_content['_id']['$oid'];
                    unset($response_content['_id']);
                    $response_content['_id'] = [];
                    $response_content['_id']['mongoId'] = $oid;
                }
                if (isset($response_content) && is_array($response_content)) {
                    foreach ($response_content as &$c) {
                        if (isset($c['_id']['$oid'])) {
                            $oid = $c['_id']['$oid'];
                            unset($c['_id']);
                            $c['_id'] = [];
                            $c['_id']['mongoId'] = $oid;
                        }
                    }
                }
            }
            return $response_contents;
        }
    }

    function getOriginalClientIp($request): string
    {
        $request = $request ?? request();
        $xForwardedFor = $request->header('x-forwarded-for');
        if (empty($xForwardedFor)) {
            $ip = $request->ip();
        } else {
            $ips = is_array($xForwardedFor) ? $xForwardedFor : explode(', ', $xForwardedFor);
            $ip = $ips[0];
        }
        return $ip;
    }
}
