<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DropIsAboveThresholdFormTimeAmendment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('time_amendments', 'is_above_threshold')) {
            Schema::table('time_amendments', function (Blueprint $table) {
                $table->dropColumn('is_above_threshold');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->char('is_above_threshold')
                ->after('amendment_id');
        });
    }
}
