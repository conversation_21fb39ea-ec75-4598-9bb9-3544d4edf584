<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsContractProgressStopsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_contract_progress_stops', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->date('progress_stop_start_date')->nullable();
            $table->date('progress_stop_end_date')->nullable();
            $table->integer('progress_stop_duration')->nullable();
            $table->text('progress_stop_reasons')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_contract_progress_stops');
    }
}
