<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractRetention;
use NPA\ACPMS\Models\ContractRetentionApplicableLog;
use NPA\ACPMS\Models\ContractRetentionLog;
use NPA\ACPMS\Models\ContractStatusCancelledLog;

class ContractRetentionLogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $contractRetention = ContractRetention::where('contract_retention_id', $id)->first();
            $contractRetentionLogs['contract_retention_logs'] = ContractRetentionLog::where('contract_retention_id', $id)->get();
            $contractRetentionLogs['contract_retention_applicable_logs'] = ContractRetentionApplicableLog::where('contract_retention_id', $id)
                ->get();
            return response()->json($contractRetentionLogs);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
