<?php

namespace NPA\ACPMS\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Filter;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\UserAwardAuthority;
use NPA\ACPMS\Role;

class AwardAuthorityChartController extends Controller
{
    public function index(Request $request)
    {
        $roleName = Role::find(auth('api')->user()->role_id)['name'];
        $userAwardAuthority = UserAwardAuthority::select('procurement_entity_id')
            ->where('user_id', auth('api')->user()->id)->first();
        $procurementEntityId = $userAwardAuthority['procurement_entity_id'];
        $procurementEntityCondition = ' ';
        if ($roleName === 'award-authority') {
            $procurementEntityCondition = ' and c.procurement_entity_id = ' . $procurementEntityId . ' ';
        } elseif ($roleName !== 'cpmd-manager' && $roleName !== 'cpmd-system-development') {
            return response()->json([], 404);
        }
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');

        $queryString = Filter::prepareQueryStrings($request->query());
        $data['widgets'] = $this->widgets($procurementEntityCondition, $queryString, $contractStatuses);
        $data['published_and_unpublished_contracts_chart'] = $this->publishedAndUnpublishedContractsChart($procurementEntityCondition, $queryString);
        $data['published_and_unpublished_procurement_type_based_chart'] = $this->publishedAndUnpublishedProcurementTypeBasedChart($procurementEntityCondition, $queryString, $contractStatuses);
        $data['contracts_documents_upload_consultancy_services'] = $this->documentsUploadConsultancyServices($procurementEntityCondition, $queryString, $procurementTypes);
        $data['contracts_count_based_on_procurement_method'] = $this->contractsCountBasedOnProcurementMethod($procurementEntityCondition, $queryString);
        $data['number_of_contracts_based_on_challenges'] = $this->numberOfContractsBasedOnChallenges($procurementEntityCondition, $queryString);
        $data['payments_percentage_chart'] = $this->paymentPercentageChart($procurementEntityCondition, $queryString);
        $data['published_contracts_respect_to_threshold_chart'] = $this->publishedContractsRespectToThresholdChart($procurementEntityCondition, $queryString, $contractStatuses);
        $data['published_contracts_respect_to_threshold_by_procurement_type'] = $this->publishedContractsRespectToThresholdByProcurementType($procurementEntityCondition, $queryString);
        $data['contracts_count_based_on_contract_manager_chart'] = $this->contractsCountBasedOnContractManagerChart($procurementEntityCondition, $queryString);
        $data['amendment_percentage_in_contracts'] = $this->amendmentPercentageInContracts($procurementEntityCondition, $queryString, $amendmentTypes);
        $data['contracts_documents_upload_chart'] = $this->contractsDocumentsUpload($procurementEntityCondition, $queryString, $procurementTypes);
        $data['contracts_count_and_value_based_on_procurement_type_chart'] = $this->contractsCountAndValueBasedOnProcurementTypeChart($procurementEntityCondition, $queryString);
        $data['contracts_value_based_on_donor_chart'] = $this->contractsValueBasedOnDonorChart($procurementEntityCondition, $queryString);
        $data['contracts_number_based_on_donor_chart'] = $this->contractsNumberBasedOnDonorChart($procurementEntityCondition, $queryString);
        $data['contracts_count_based_on_selection_method_in_consultancy'] = $this->contractsCountBasedOnSelectionMethodInConsultancy($procurementEntityCondition, $queryString, $procurementTypes);
        $data['work_in_progress_contract_payments_volume_chart'] = $this->workInProgressContractPaymentsVolumeChart($procurementEntityCondition, $queryString, $contractStatuses);
        $data['work_in_progress_contract_physical_progress_volume_chart'] = $this->workInProgressContractPhysicalProgressVolumeChart($procurementEntityCondition, $queryString, $contractStatuses);
        $data['completed_contracts_payment_volume_chart'] = $this->completedContractsPaymentVolumeChart($procurementEntityCondition, $queryString, $contractStatuses);
        $data['type_of_amendments_in_contracts_chart'] = $this->typeOfAmendmentsInContractsChart($procurementEntityCondition, $queryString);
        $data['province_contracts_based_on_value'] = $this->provinceContractsBasedOnValue($procurementEntityCondition, $queryString);
        $data['contracts_count_based_on_status_chart'] = $this->contractsCountBasedOnStatusChart($procurementEntityCondition, $queryString);
        $data['contracts_value_based_on_status_chart'] = $this->contractsValueBasedOnStatusChart($procurementEntityCondition, $queryString);
        $data['contractChallengesContractAmount'] = $this->contractChallengesContractAmount($procurementEntityCondition, $queryString);

        return response()->json($data);
    }

    private function widgets($procurementEntityCondition, $queryString)
    {
        $data = DB::select('select
                                (
                                    select count(c.id) as inserted_contracts
                                        from contracts as c  
                                        ' . $queryString['joins'] . '
                                        where 1 = 1
                                        ' . $procurementEntityCondition . '
                                        ' . $queryString['query_string'] . '
                                ) as inserted_contracts
                                ,(
                                    select 
                                        count(c.id) as published_contracts 
                                        from contracts as c 
                                        ' . $queryString['joins'] . '
                                        where c.is_published = true
                                        ' . $procurementEntityCondition . '
                                        ' . $queryString['query_string'] . '
                                ) as published_contracts
                                ,(
                                    select
                                        count(distinct c.id) - count(case when cgiv.is_approved = true then 1 end) as not_inserted_contracts 
                                    from contracts as c
                                    left join contract_general_information_verifications as cgiv
                                      on c.id = cgiv.contract_id
                                    ' . $queryString['joins'] . '
                                    where 1 = 1
                                    ' . $procurementEntityCondition . '
                                    ' . $queryString['query_string'] . '
                                ) as not_inserted_contracts
                                ,(
                                  select 
                                    sum(
                                        (ifnull(cd.actual_value, 0) + 
                                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                                        ifnull(amendment.amount, 0))
                                        * if(c.exchange_rate, c.exchange_rate, 1)
                                    ) 
                                  from contracts as c
                                  left join (
                                     select 
                                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                            a.contract_id as contract_id
                                     from amendments as a
                                     left join cost_amendments as ca 
                                         on ca.amendment_id = a.id
                                     left join time_and_cost_amendments as tca
                                         on tca.amendment_id = a.id
                                     where a.is_approved = true
                                     group by  a.contract_id 
                                  ) as amendment
                                        on c.id = amendment.contract_id
                                  join contract_details as cd
                                        on cd.contract_id = c.id
                                  join contract_details_verifications as cdv
                                        on cdv.contract_detail_id = cd.id
                                        and cdv.is_approved = true
                                  left join provisional_sum_and_contingencies as psc
                                      on psc.contract_detail_id = cd.id
                                            
                                  ' . $queryString['joins'] . '
                                  where 1 = 1 ' . $procurementEntityCondition . '
                                  ' . $queryString['query_string'] . '
                                ) as signed_contracts_cost_with_amendments
                                ,(
                                select 
                                    sum(
                                            ifnull(cpp.amount, 0) * 
                                            if(c.exchange_rate, c.exchange_rate, 1)
                                        ) as paid_payments 
                                from contracts as c 
                                join contract_progress_payments as cpp
                                    on c.id = cpp.contract_id 
                                    and cpp.is_approved = true
                                ' . $queryString['joins'] . '
                                where 1 = 1  ' . $procurementEntityCondition . '
                                ' . $queryString['query_string'] . '
                                ) as paid_payments
                                ,(
                                select
                                    sum(
                                        (ifnull(cpp.total_physical_progress, 0) / 100) * 
                                        (ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)
                                        * if(c.exchange_rate, c.exchange_rate, 1) ) )  as actual_progress_value
                                        
                                from contracts as c
                                left join(
                                    select 
                                        inner_cpp.contract_id,
                                        sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                                    from contract_progress_payments as inner_cpp
                                    where inner_cpp.is_approved = true
                                    group by inner_cpp.contract_id 
                                ) as cpp
                                    on cpp.contract_id = c.id
                                left join (
                                     select 
                                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                            a.contract_id as contract_id
                                     from amendments as a
                                     left join cost_amendments as ca 
                                         on ca.amendment_id = a.id
                                     left join time_and_cost_amendments as tca
                                         on tca.amendment_id = a.id
                                     where a.is_approved = true
                                     group by  a.contract_id 
                                  ) as amendment
                                        on c.id = amendment.contract_id
                                join contract_details as cd
                                    on cd.contract_id = c.id
                                join contract_details_verifications as cdv
                                    on cdv.contract_detail_id = cd.id
                                    and cdv.is_approved = true
                                    
                                left join provisional_sum_and_contingencies as psc
                                  on psc.contract_detail_id = cd.id
                                ' . $queryString['joins'] . '
                                where 1 = 1 ' . $procurementEntityCondition . '
                                    ' . $queryString['query_string'] . ' 
                                ) as physical_progress_value
                                ,(
                                   select 
                                    count(c.id) as above_threshold_contracts_count
                                    from contracts as c 
                                    ' . $queryString['joins'] . '
                                    where c.is_above_threshold = true
                                    ' . $procurementEntityCondition . '
                                    ' . $queryString['query_string'] . '
                                ) as above_threshold_contracts_count
                                ,(
                                    select 
                                    count(c.id) as below_threshold_contracts_count
                                    from contracts as c 
                                    ' . $queryString['joins'] . '
                                    where c.is_above_threshold = false
                                    ' . $procurementEntityCondition . '
                                    ' . $queryString['query_string'] . '
                                ) as below_threshold_contracts_count
                            ');
        return $data[0];
    }

    private function publishedAndUnpublishedContractsChart($procurementEntityCondition, $queryString)
    {   // Has transferred to AllCharts

        $data = DB::select('
                select 
                    count(case when c.is_published = true then 1 end) as published_contracts,
                    count(case when c.is_published = false then 1 end) as unpublished_contracts
                from contracts as c
                ' . $queryString['joins'] . '
                where  1 = 1   ' . $procurementEntityCondition . '
                 ' . $queryString['query_string'] . '; 
        ');
        isset($data[0]) ? $data = $data[0] : $data = null;

        return $data;
    }

    private function publishedAndUnpublishedProcurementTypeBasedChart($procurementEntityCondition, $queryString, $contractStatuses)
    {
        // Has Transferred to AllCharts
        $contractStatusNotSigned = DropDowns::getElementTypeBySlug($contractStatuses, 'not-signed');
        $data['procurement_type'] = DB::select('
            select 
                c.procurement_type_id as id,
                count(case when c.is_published = true then 1 end) as published_contracts_count,
                count(c.id) contracts_count
            from contracts as c
            join (
                select
                    csl.id, csl.contract_id, csl.status_id
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                    on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = c.id
            ' . $queryString['joins'] . '
            where cs.status_id != ' . $contractStatusNotSigned['id'] . ' 
            ' . $procurementEntityCondition . '
            ' . $queryString['query_string'] . '
            group by c.procurement_type_id;
        ');
        return $data;
    }

    private function documentsUploadConsultancyServices($procurementEntityCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');

        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyServices['id'] . '   ' . $procurementEntityCondition . '
                 ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                        ' . $queryString['joins'] . '
                    where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                     ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'reference_terms\'
                 ,\'financial_tables\', \'action_plan\', \'negotiation_meeting_minutes\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'key_staffs\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name  = \'contract_closeout_report\'
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                            ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                         ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                            ' . $queryString['joins'] . '
                            where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id = ' . $consultancyServices['id'] . '
                             ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        ');
        return $data;
    }

    private function contractsCountBasedOnProcurementMethod($procurementEntityCondition, $queryString)
    {

        $data = DB::select('
            select
              count(c.id) as contrats_count,
              c.procurement_method_id
            from contracts as c
            ' . $queryString['joins'] . '
            where  1 = 1 ' . $procurementEntityCondition . '
            ' . $queryString['query_string'] . '
            group by c.procurement_method_id
            ');
        return $data;
    }

    private function numberOfContractsBasedOnChallenges($procurementEntityCondition, $queryString)
    {
        $data = DB::select('
            select 
                total_contracts_count, 
                challenges_contracts_count,
                (total_contracts_count-challenges_contracts_count) as not_challenge_contracts_count
            from 
            (
                select 
                    count(distinct(c.id)) as total_contracts_count, 
                    count(distinct (cr.contract_id)) as challenges_contracts_count
                from contracts as c
                left Join challenges_and_remarks as cr 
                    on c.id = cr.contract_id and cr.is_approved = true
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $procurementEntityCondition . '
                 ' . $queryString['query_string'] . '
            ) as t
                ');
        return $data[0];
    }

    private function paymentPercentageChart($procurementEntityCondition, $queryString)
    {
        $data['plan_and_actual'] = DB::select('
                    select distinct
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 / 
                          sum(ifnull(cp.amount, 1) * if(c.exchange_rate, c.exchange_rate, 1))
                        ) as percentage
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    left join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id  
                        and cpv.is_approved = true
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1  ' . $procurementEntityCondition . '
                    ' . $queryString['query_string'] . '
            ')[0];
        $data['re_plan_and_actual'] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        (
                          sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) ) * 100 / 
                          if(
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                              sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)),
                              1)
                        ) as percentage
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where  1 = 1  ' . $procurementEntityCondition . '
                    ' . $queryString['query_string'] . '
                    group by cr.iteration_count;   
            ');

        return $data;
    }

    private function publishedContractsRespectToThresholdChart($procurementEntityCondition, $queryString, $contractStatuses)
    {
        $contractStatusNotSigned = DropDowns::getElementTypeBySlug($contractStatuses, 'not-signed');
        $data = DB::select('
                                select 
                                    is_above_threshold as threshold,
                                    count(case when c.is_published = true then 1 end) as published_contracts_count,
                                    count(c.id) as contracts_count
                                from contracts as c 
                                join (
                                       select
                                          csl.id, csl.contract_id, csl.status_id
                                       from contract_status_logs as csl
                                       join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                           on csl.id = max_value.max_id
                                       ) as cs
                                           on cs.contract_id = c.id
                                ' . $queryString['joins'] . '
                                where cs.status_id != ' . $contractStatusNotSigned['id'] . ' 
                                ' . $procurementEntityCondition . '
                                ' . $queryString['query_string'] . '
                                group by is_above_threshold
                    ');

        return $data;
    }

    private function publishedContractsRespectToThresholdByProcurementType($procurementEntityCondition, $queryString)
    {
        //Has Transferred to AllCharts
        $data = DB::select('
                                select 
                                    c.procurement_type_id, 
                                    count(case when c.is_above_threshold = true then 1 end) as above_threshold_contracts_count,
                                    count(case when c.is_above_threshold = false then 1 end) as below_threshold_contracts_count
                                from contracts as c 
                                ' . $queryString['joins'] . '
                                where 1 = 1 
                                ' . $procurementEntityCondition . '
                                ' . $queryString['query_string'] . '
                                group by c.procurement_type_id
                    ');
        return $data;
    }

    private function contractsCountBasedOnContractManagerChart($procurementEntityCondition, $queryString)
    {

        // Has Transferred to AllCharts
        $data = DB::select('
                    select
                        u.full_name as full_name,
                        count(u.id) as contracts_count
                    from users as u
                    join contracts as c
                      on c.contract_manager_id = u.id
                    join user_contract_managers as ucm
                      on ucm.user_id = u.id
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $procurementEntityCondition . '
                    ' . $queryString['query_string'] . '
                    group by u.id, u.full_name;
               ');
        return $data;
    }

    private function amendmentPercentageInContracts($procurementEntityCondition, $queryString, $amendmentTypes)
    {
        $timeAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-amendment');
        $timeAndCostAmendment = DropDowns::getElementTypeBySlug($amendmentTypes, 'time-and-cost-amendment');
        $data = DB::select('
                    select 
                        (
                          sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        )  as total_value_percentage,
                        (
                          sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1)) * 100 /
                          (sum(ifnull(cd.actual_value, 0) * if(c.exchange_rate, c.exchange_rate, 1)) + sum(ifnull(amendment_cost.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1))) 
                        )  as cost_amendment_percentage,
                        (
                          sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        ) as total_time_percentage, 
                        (
                          sum(ifnull(amendment_time.days, 0)) * 100 /
                          (sum(ifnull(datediff(cd.planned_end_date,ifnull(cp.actual_start_date, cd.planned_start_date)),0)) + sum(ifnull(amendment_time.days, 0)))  
                        ) as time_amendment_percentage 
                        
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id 
                        and cdv.is_approved = true
                    join contract_progresses as cp
                        on c.id = cp.contract_id
                    join contract_progress_verifications as cpv
                        on cp.id = cpv.contract_progress_id
                        and cpv.is_approved = true
                     left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment_cost
                       on amendment_cost.contract_id = c.id
                       
                    join (
                        select 
                             a.contract_id,
                             GREATEST(
                                      ifnull(
                                          datediff(
                                              (select amended_end_date from time_amendments where amendment_id = a.id)
                                              , 
                                              (select planned_end_date from contract_details where contract_id = a.contract_id)
                                          ),0), 
                                      ifnull(
                                            datediff(
                                                (select amended_end_date from time_and_cost_amendments where amendment_id = a.id)
                                                , 
                                                (select planned_end_date from contract_details where contract_id = a.contract_id)
                                            ),0)
                              ) as days
                        from amendments as a 
                        join ( 
                            SELECT 
                            contract_id, max(id) as max_id 
                            FROM amendments 
                            where type_id in (' . $timeAmendment['id'] . ',' . $timeAndCostAmendment['id'] . ') and is_approved =true
                            group by contract_id
                            ) as max_value
                            on max_value.max_id = a.id
                        group by a.contract_id, a.id
                    ) as amendment_time
                        on amendment_time.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1  ' . $procurementEntityCondition . '
                    ' . $queryString['query_string'] . '
        ');
        $data = isset($data[0]) ? $data[0] : $data;
        return $data;
    }

    private function contractsDocumentsUpload($procurementEntityCondition, $queryString, $procurementTypes)
    {
        $consultancyServices = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data['total'] = DB::select('
                select 
                    count(c.id ) as contract_count
                from contracts as c
                ' . $queryString['joins'] . '
                where c.procurement_type_id != ' . $consultancyServices['id'] . '  ' . $procurementEntityCondition . ' 
                ' . $queryString['query_string'] . '
        ')[0];
        $data['uploaded'] = DB::select('
        
            select 
                att.field_name as document_name, 
                count(distinct att.foreign_key) as count
            from attachments as att
            where (att.foreign_key in (
                    select 
                        cd.id as contract_details_id	
                    from contracts as c
                    join contract_details as cd
                        on cd.contract_id  = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                     ' . $queryString['joins'] . '
                    where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                    ' . $queryString['query_string'] . '
                                    )
                and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                 ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                 ,\'contract_performance_guarantee\')
                 ) or (att.foreign_key in (
                        select 
                            cpa.id as cpa_id				
                        from contracts as c
                        join contract_progress_advances as cpa
                            on cpa.contract_id = c.id
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                    )	
                and att.field_name = \'advance_payment_guarantee\'
                        )
                or (att.foreign_key in (
                        select 
                            cs.id as cs_id
                        from contracts as c
                        join contract_statuses as cs 
                            on cs.contract_id = c.id
                        join contract_status_verifications as csv
                            on csv.contract_status_id = cs.id
                            and csv.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1  ' . $procurementEntityCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                
                                    )
                and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                    )
                
                or (att.foreign_key in (
                        select 
                            comp.id as comp_id
                        from contracts as c
                        join company_general_informations as cgi
                            on cgi.contract_id = c.id
                            and cgi.is_approved = true
                        join companies as comp
                            on comp.company_general_information_id = cgi.id
                            and cgi.is_approved = true
                         ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                        ' . $queryString['query_string'] . '
                                )
                and att.field_name = \'joint_venture_licence\'
                    )
                or (att.foreign_key in (
                            select 
                                c.id as c_id
                            from contracts as c
                             ' . $queryString['joins'] . '
                            where  1 = 1   ' . $procurementEntityCondition . ' and c.procurement_type_id != ' . $consultancyServices['id'] . '
                            ' . $queryString['query_string'] . '
                )
                and att.field_name = \'planned_payments_schedule\'
                    )
            group by att.field_name
        
        
        ');

        return $data;
    }

    private function contractsCountAndValueBasedOnProcurementTypeChart($procurementEntityCondition, $queryString)
    {
// Has Transferred to AllCharts
        $data = DB::select('
                    select
                      count(c.id) as contracts_count,
                      round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            )/ 1000000,4) as contract_total_value,
                      c.procurement_type_id as type_id
                    from contracts as c
                    join contract_details as cd
                      on c.id = cd.contract_id
                    join contract_details_verifications as cdv
                      on cd.id = cdv.contract_detail_id
                    left join provisional_sum_and_contingencies as psc
                      on psc.contract_detail_id = cd.id
                    left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.is_approved = true
                                group by  a.contract_id 
                        
                      ) as amendment
                        on c.id = amendment.contract_id
                      ' . $queryString['joins'] . '
                    where  1 = 1   ' . $procurementEntityCondition . '
                    and cdv.is_approved = true
                    ' . $queryString['query_string'] . '
                    group by c.procurement_type_id');


        return $data;
    }

    private function typeOfAmendmentsInContractsChart($procurementEntityCondition, $queryString)
    {
        $data = DB::select('
        select 
            c.procurement_type_id as procurement_type_id,
            a.type_id as amendment_type,
            count(a.id) as amendment_count
        from contracts as c
        join amendments as a 
            on a.contract_id = c.id
            and a.is_approved = true 
        ' . $queryString['joins'] . '
        where  1 = 1   ' . $procurementEntityCondition . '
         ' . $queryString['query_string'] . '
        group by c.procurement_type_id, a.type_id;
       ');

        return $data;
    }

    private function contractsValueBasedOnDonorChart($procurementEntityCondition, $queryString)
    {
        $data = DB::select('select
                          round(sum(
                              (ifnull(cd.actual_value, 0) + 
                              ifnull(amendment.amount, 0) + 
                              ifnull(psc.provisional_sum_and_contingency, 0)) * 
                              if(c.exchange_rate, c.exchange_rate, 1)
                            )/ 1000000,4) as total_contracts_value,cdo.donor_id as donor_id
                          from contracts as c
                          join contract_donors as cdo
                            on c.id = cdo.contract_id
                          join contract_details as cd
                              on c.id = cd.contract_id
                          join contract_details_verifications cdv
                              on cd.id = cdv.contract_detail_id
                              and cdv.is_approved = true
                          left join provisional_sum_and_contingencies as psc
                              on psc.contract_detail_id = cd.id
                          left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                group by  a.contract_id 
                        
                            ) as amendment
                                on c.id = amendment.contract_id
                          ' . $queryString['joins'] . '
                          where 1 = 1 ' . $procurementEntityCondition . '
                          ' . $queryString['query_string'] . '
                          group by cdo.donor_id;
                        ');

        return $data;
    }

    private function contractsNumberBasedOnDonorChart($procurementEntityCondition, $queryString)
    {
        $data = DB::select('
                        select
                          count(c.id) as number_of_contracts,
                          cd.donor_id
                        from contracts as c
                        join contract_donors as cd
                          on c.id = cd.contract_id
                          ' . $queryString['joins'] . '
                        where  1 = 1   ' . $procurementEntityCondition . '
                        ' . $queryString['query_string'] . '
                        group by cd.donor_id
                                ');
        return $data;
    }

    private function contractsCountBasedOnSelectionMethodInConsultancy($procurementEntityCondition, $queryString, $procurementTypes)
    {
        $consultancyStatus = DropDowns::getElementTypeBySlug($procurementTypes, 'consultancy');
        $data = DB::select('
                select 
                    cc.selection_method_id,
                    count(cc.id) as count
                from contracts as c 
                join consultancy_contracts_selection_methods as cc
                     on cc.contract_id = c.id 
                ' . $queryString['joins'] . '
                where  c.procurement_type_id = ' . $consultancyStatus['id'] . '   
                ' . $procurementEntityCondition . '
                ' . $queryString['query_string'] . '
                 group by cc.selection_method_id
        ');

        return $data;
    }

    private function workInProgressContractPaymentsVolumeChart($procurementEntityCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];

        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select 
                        round(sum(ifnull(cp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4)  as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                        
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and 
                            cpp.month_id = cp.month_id and 
                            cpp.year = cp.year and 
                            cpp.is_approved = true	
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and  cpv.is_approved = true 
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id
                    join contracts as c
                        on cp.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1
                        ' . $procurementEntityCondition . ' 
                         ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and  
                        cp.year in  ( ' . $value['year'] . ' ) and 
                        cp.month_id in(' . $value['months'] . ')
        ')[0];


            $data['re_plan_and_actual'][$key] = DB::select('
                    select 
                        cr.iteration_count as replan_number,
                        round(sum(ifnull(crp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as planning_amount,
                        round(sum(ifnull(cpp.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) / 1000000), 4) as actual_amount
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and 
                            cpp.month_id = crp.month_id and 
                            cpp.year = crp.year and 
                            cpp.is_approved = true
                    join (
                        select 
                            csl.id, csl.contract_id, csl.status_id 
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $procurementEntityCondition . ' 
                         ' . $queryString['query_string'] . ' and 
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in (' . $value['year'] . ') and 
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;
        
        ');

        }

        return $data;

    }

    private function workInProgressContractPhysicalProgressVolumeChart($procurementEntityCondition, $queryString, $contractStatuses)
    {
        $workInProgressStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'work-in-process');

        $contract_status_id = $workInProgressStatus['id'];
        $current_date = Carbon::now();
        $current_jalali_date = jDateTime::toJalali($current_date->year, $current_date->month, $current_date->day);
        $firstQuarter = '';
        $otherQuarterYear = '';
        for ($i = 1393; $i <= $current_jalali_date[0]; $i++) {
            $firstQuarter .= $i;
            $otherQuarterYear .= $i;
            if ($i !== $current_jalali_date[0]) {
                $firstQuarter .= ', ';
                $otherQuarterYear .= ', ';
            }
        }

        if ($current_jalali_date[1] > 9) {
            $tempYear = $current_jalali_date[0] + 1;
            $otherQuarterYear .= ', ' . $tempYear . '';
        }

        $year = [
            'first_quarter' => ['year' => $firstQuarter, 'months' => '10, 11, 12'],
            'second_quarter' => ['year' => $otherQuarterYear, 'months' => '1, 2, 3'],
            'third_quarter' => ['year' => $otherQuarterYear, 'months' => '4, 5, 6'],
            'forth_quarter' => ['year' => $otherQuarterYear, 'months' => '7, 8, 9'],
        ];
        $tempPlan = 0;
        $tempActual = 0;
        foreach ($year as $key => $value) {
            $data['plan_and_actual'][$key] = DB::select('
                    select
                        round(
                        sum((cp.physical_progress_percentage  / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000, 4) + ' . $tempPlan . ' as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull((
                                select
                                    sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                                from amendments as a
                                left join cost_amendments as ca
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.contract_id = c.id
                            ), 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) / 1000000 , 4) + ' . $tempActual . ' as actual_progress_value
                    from contract_planning_finance_affairs_and_physical_progresses as cp
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cp.contract_id and
                            cpp.month_id = cp.month_id and
                            cpp.year = cp.year and
                            cpp.is_approved = true
                    join contract_planning_f_a_a_p_p_verifications as cpv
                        on cpv.contract_id = cp.contract_id
                        and cpv.is_approved = true


                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cp.contract_id


                    join contracts as c
                        on cp.contract_id = c.id
                    join contract_details as cd
	                    on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $procurementEntityCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        cp.year in  ( ' . $value['year'] . ' ) and
                        cp.month_id in(' . $value['months'] . ')
        ')[0];
            $tempPlan = $data['plan_and_actual'][$key]->planning_progress_value ? $data['plan_and_actual'][$key]->planning_progress_value : 0;
            $tempActual = $data['plan_and_actual'][$key]->actual_progress_value ? $data['plan_and_actual'][$key]->actual_progress_value : 0;


            $data['re_plan_and_actual'][$key] = DB::select('
                    select distinct
                        cr.iteration_count as replan_number,
                        round(sum((crp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as planning_progress_value,

                        round(sum((cpp.physical_progress_percentage / 100) * (cd.actual_value + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) 
                        * if(c.exchange_rate, c.exchange_rate, 1)) / 1000000 , 4)  as actual_progress_value
                    from contract_re_planning_date_f_a_a_p_ps as cr
                    join contract_re_planning_date_f_a_a_p_p_verifications as crv
                        on cr.id = crv.contract_re_planning_date_f_a_a_p_p_id
                        and crv.is_approved = true
                    join contract_re_planning_finance_affairs_and_physical_progresses as crp
                        on crp.contract_re_planning_date_f_a_a_p_p_id = cr.id
                    left join contract_progress_payments as cpp
                        on  cpp.contract_id = cr.contract_id and
                            cpp.month_id = crp.month_id and
                            cpp.year = crp.year and
                            cpp.is_approved = true
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = cr.contract_id
                    join contracts as c
                        on cr.contract_id = c.id
                    left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
                    ) as amendment
                        on c.id = amendment.contract_id
                    join contract_details as cd
                        on cd.contract_id = c.id
                    join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                    left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                        ' . $queryString['joins'] . '
                    where 1 = 1 ' . $procurementEntityCondition . '
                        ' . $queryString['query_string'] . ' and
                        cs.status_id = ' . $contract_status_id . ' and
                        crp.year in  ( ' . $value['year'] . ' ) and
                        crp.month_id in(' . $value['months'] . ')
                    group by cr.iteration_count;

        ');

        }

        return $data;

    }

    private function completedContractsPaymentVolumeChart($procurementEntityCondition, $queryString, $contractStatuses)
    {
        $contractCompletedStatus = DropDowns::getElementTypeBySlug($contractStatuses, 'contract-completion-defect-liability-period');
        $data = DB::select('
            select 
                round(
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contract_total_value,
                    
                round(
                  sum(
                          ifnull(cpp.amount,0) * 
                          if(c.exchange_rate, c.exchange_rate, 1)
                      ) / 1000000, 4) as paid_amounts,
                c.procurement_type_id as type_id
            from contracts as c
            join contract_details as cd
                on c.id = cd.contract_id
            join contract_details_verifications as cdv
                on cd.id = cdv.contract_detail_id and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
            join (
                    select
                        csl.id, csl.contract_id, csl.status_id
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cd.contract_id
            left join (
                select 
                    cpp_inner.contract_id ,
                    sum(cpp_inner.amount) as amount
                from  contract_progress_payments as cpp_inner
                where cpp_inner.is_approved = true
                group by cpp_inner.contract_id 
              ) as cpp
                on c.id = cpp.contract_id 
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        group by  a.contract_id 
                
               ) as amendment
                   on c.id = amendment.contract_id
            ' . $queryString['joins'] . '
              where cs.status_id = ' . $contractCompletedStatus['id'] . '
            ' . $procurementEntityCondition . '
               ' . $queryString['query_string'] . '
            group by c.procurement_type_id 
             ');
        return $data;

    }

    private function provinceContractsBasedOnValue($procurementEntityCondition, $queryString)
    {
        $data['no_shared_contracts'] = DB::select('
                select 
                    p.id as province_id, 
                    cs.status_id as contract_status_id, 
                    count(distinct(c.id)) as contracts_count,
                  sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(c.exchange_rate, c.exchange_rate, 1) 
                    ) as total_contract_value
                from contracts as c 
                join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                            on csl.id = max_value.max_id
                        ) as cs
                            on cs.contract_id = c.id
                left join contract_details as cd 
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on c.id = amendment.contract_id
                ' . $queryString['joins'] . '
                where 1=1 ' . $queryString['query_string'] . '
                ' . $procurementEntityCondition . '
                and c.id not in
                    (
                    select 
                        c.id as contract_id
                    from contracts as c 
                    join domestic_contract_execution_locations as dc 
                        on dc.contract_id = c.id 
                    join temp_districts as d
                        on d.id = dc.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    group by c.id
                    having count(distinct p.id) > 1
                    )
                group by p.id, cs.status_id;
        ');

        $data['shared_contracts'] = DB::select('
            select province_id, 
            sum(shared_contracts_count) as shared_contracts_count, 
            sum(total_contract_shared_value) as total_contract_shared_value 
            from (
                select 
                    provinces_contracts.province_id, 
                    sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) *
                        if(join_c.exchange_rate, join_c.exchange_rate, 1) 
                    ) as total_contract_shared_value,
                    count(provinces_contracts.province_id) as shared_contracts_count
                from 
                    (
                        select distinct
                            p.id as province_id,
                            c.id as contract_id
                        from contracts as c
                        join domestic_contract_execution_locations as dcel
                            on dcel.contract_id = c.id
                        join temp_districts as d 
                            on d.id = dcel.district_id
                        join temp_provinces as p
                            on p.id = d.temp_province_id
                        
                    ) as provinces_contracts
                left join contract_details as cd
                    on cd.contract_id = provinces_contracts.contract_id
                left join contract_details_verifications as cdv
                        on cdv.contract_detail_id = cd.id
                        and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                ) as amendment
                    on provinces_contracts.contract_id = amendment.contract_id
                join contracts as join_c
                    on join_c.id = provinces_contracts.contract_id 
                where provinces_contracts.contract_id not in (
                    select 
                        c.id as contract_id
                    from contracts as c
                    join domestic_contract_execution_locations as dcel
                        on dcel.contract_id = c.id
                    join temp_districts as d 
                        on d.id = dcel.district_id
                    join temp_provinces as p
                        on p.id = d.temp_province_id
                    ' . $queryString['joins'] . '
                    where 1 = 1 ' . $queryString['query_string'] . '
                    ' . $procurementEntityCondition . '
                    group by c.id
                    having count(distinct p.id) = 1
                )
                group by provinces_contracts.province_id, provinces_contracts.contract_id) as temp_table
            group by province_id;
        ');

        return $data;
    }

    private function contractsCountBasedOnStatusChart($procurementEntityCondition, $queryString)
    {
        $data['contracts_count'] = DB::select('
            select 
                count(distinct c.id) as contract_count
            from contracts as c 
            join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as csl_max
                    on csl_max.contract_id = c.id
            ' . $queryString['joins'] . '
            where  1 = 1  ' . $procurementEntityCondition . '
            ' . $queryString['query_string'] . ';
	    ')[0];
        $data['status_based_contracts_count'] = DB::select('
            select 
                cs.status_id as status_id,
                count( distinct cs.id) as status_count
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c 
                on c.id = cs.contract_id
            ' . $queryString['joins'] . '
            where 1 = 1  ' . $procurementEntityCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
 
            
        ');

        return $data;
    }

    private function contractsValueBasedOnStatusChart($procurementEntityCondition, $queryString)
    {
        $data['contracts_total_value'] = DB::select('
            select round(sum(temp.contract_value)/ 1000000, 4) as contracts_total_value
                from	
                (select 
                    sum(
                                        (ifnull(cd.actual_value, 0) + 
                                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                                        ifnull(amendment.amount, 0))
                                        * if(c.exchange_rate, c.exchange_rate, 1)
                                    )  as contract_value
                from contracts as c
                left join (
                                     select 
                                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                            a.contract_id as contract_id
                                     from amendments as a
                                     left join cost_amendments as ca 
                                         on ca.amendment_id = a.id
                                     left join time_and_cost_amendments as tca
                                         on tca.amendment_id = a.id
                                     where a.is_approved = true
                                     group by  a.contract_id 
                                  ) as amendment
                                        on c.id = amendment.contract_id 
                join (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
                  on cs.contract_id = c.id
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id 
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
                ' . $queryString['joins'] . '
                where 1 = 1  ' . $procurementEntityCondition . '
                ' . $queryString['query_string'] . '
                group by c.id, c.exchange_rate) as temp;
	    ')[0];

        $data['status_based_contracts_value'] = DB::select('
            select 
                cs.status_id as status_id,
                round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0))
                        * if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000, 4)  as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                     from amendments as a
                     left join cost_amendments as ca 
                         on ca.amendment_id = a.id
                     left join time_and_cost_amendments as tca
                         on tca.amendment_id = a.id
                     where a.is_approved = true
                     group by  a.contract_id 
            ) as amendment
                on c.id = amendment.contract_id 
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            ' . $queryString['joins'] . '
            where 1 = 1 ' . $procurementEntityCondition . '
            ' . $queryString['query_string'] . '
            group by cs.status_id;
        ');

        return $data;
    }

    private function contractChallengesContractAmount($procurementEntityCondition, $queryString)
    {
        $data = DB::select('
                  select 
                    car_distinct.category_id,
                    count(car_distinct.contract_id) contracts_count,
                    round(sum(
                        (ifnull(cd.actual_value, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0) + 
                        ifnull(amendment.amount, 0)) * 
                        if(c.exchange_rate, c.exchange_rate, 1)
                    ) / 1000000 , 4) as contracts_amount
                from contracts as c
                left join contract_details as cd
                    on cd.contract_id = c.id
                left join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join (
                     select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                
                   ) as amendment
                          on c.id = amendment.contract_id
               left join provisional_sum_and_contingencies as psc
                	  on psc.contract_detail_id = cd.id
               join (
                    select distinct 
                      inner_car.category_id,
                      inner_car.contract_id
                    from challenges_and_remarks as inner_car
                    where inner_car.is_approved = true
                    group by inner_car.category_id, inner_car.contract_id, inner_car.id
                 )as car_distinct 
                    on car_distinct.contract_id = c.id
                 ' . $queryString['joins'] . '
               where 1 = 1    ' . $procurementEntityCondition . '
               ' . $queryString['query_string'] . ' 
               group by car_distinct.category_id
                  
                  
                  ');
        return $data;
    }

    public function procurement_type()
    {
        $path = 'api/dropDown/procurementType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public function getProcurementTypeId($slug)
    {
        return $this->getElementTypeBySlug($this->procurement_type(), $slug)['id'];
    }


}
