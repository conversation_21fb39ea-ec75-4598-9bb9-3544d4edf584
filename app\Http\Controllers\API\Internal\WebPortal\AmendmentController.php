<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Illuminate\Http\Request;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\Contract;

class AmendmentController extends Controller
{
    public function index(Request $request)
    {
        $data = [];
        $exchangeRate = Contract::find($request->query('contract_id'))['exchange_rate'];
        if (!$exchangeRate) {
            $exchangeRate = 1;
        }
        $amendments = Amendment::select('type_id', 'id')
            ->where('is_published', 1)
            ->where('contract_id', $request->query('contract_id'))
            ->with(
                ['time_amendment' => function ($query) {
                    $query->select('amendment_reasons', 'replanning_start_date', 'amended_end_date', 'amendment_id');
                }, 'time_and_cost_amendment' => function ($query) {
                    $query->select('amendment_reasons', 'replanning_start_date', 'amendment_amount', 'amended_end_date', 'amendment_id');
                }, 'cost_amendment' => function ($query) {
                    $query->select('amendment_reasons', 'replanning_start_date', 'amendment_amount', 'amended_end_date', 'amendment_id');
                }, 'amendment_in_contract_terms' => function ($query) {
                    $query->select('amendment_reasons', 'replanning_start_date', 'terms_in_contract_amendment', 'amended_end_date', 'amendment_id');
                }]
            )->orderBy('id', 'asc')->get();

        foreach ($amendments as &$amendment) {
            $temp['type_id'] = $amendment['type_id'];
            $temp['id'] = $amendment['id'];
            unset($amendment['id']);
            unset($amendment['type_id']);
            $details = $this->getInsertedAmendment($amendment->toArray());
            $temp['amendment_reasons'] = $details['amendment_reasons'];
            $temp['amendment_date'] = $details['replanning_start_date'];
            $temp['amendment_amount'] = isset($details['amendment_amount']) ? $details['amendment_amount'] * $exchangeRate : 0;
            $temp['terms_in_contract_amendment'] = isset($details['terms_in_contract_amendment']) ? $details['terms_in_contract_amendment'] : null;
            $temp['amendment_period']['start_date'] = $details['replanning_start_date'] ? $details['replanning_start_date'] : null;
            $temp['amendment_period']['end_date'] = $details['amended_end_date'] ? $details['amended_end_date'] : null;

            array_push($data, $temp);
        }
        return response()->json($data, 200);
    }

    private function getInsertedAmendment($array)
    {
        foreach ($array as $element) {
            if ($element !== null) {
                return $element;
            }
        }

    }
}
