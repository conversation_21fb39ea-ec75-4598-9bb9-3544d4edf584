<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCurrencyParsGeneralInformations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pars_general_informations', function (Blueprint $table) {
            $table->string('currency_name')->after('contract_number')->nullable();
        });
        Schema::table('pars_remarks', function (Blueprint $table) {
            $table->boolean('is_performance_security_applicable')->after('advance_payment_guaranty_expiration_date')->nullable();
            $table->boolean('is_performance_security_legally')->after('is_performance_security_applicable')->nullable();
            $table->boolean('is_advance_payment_applicable')->after('is_performance_security_legally')->nullable();
            $table->boolean('is_advance_payment_legally')->after('is_advance_payment_applicable')->nullable();
            $table->string('contract_progress_remark')->after('is_advance_payment_legally')->nullable();
            $table->text('implemented_suggestions_vendor')->after('contract_progress_remark')->nullable();
            $table->text('not_implemented_suggestions_vendor')->after('implemented_suggestions_vendor')->nullable();
            $table->text('old_suggestions_vendor')->after('not_implemented_suggestions_vendor')->nullable();
            $table->text('new_suggestions_vendor')->after('old_suggestions_vendor')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pars_general_informations', function (Blueprint $table) {
            $table->dropColumn('currency_name');
        });
        Schema::table('pars_remarks', function (Blueprint $table) {
            $table->dropColumn('is_performance_security_applicable');
            $table->dropColumn('is_performance_security_legally');
            $table->dropColumn('is_advance_payment_applicable');
            $table->dropColumn('is_advance_payment_legally');
            $table->dropColumn('contract_progress_remark');
            $table->dropColumn('implemented_suggestions_vendor');
            $table->dropColumn('not_implemented_suggestions_vendor');
            $table->dropColumn('old_suggestions_vendor');
            $table->dropColumn('new_suggestions_vendor');
        });
    }
}
