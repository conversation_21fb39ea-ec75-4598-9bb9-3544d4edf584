<?php

namespace NPA\ACPMS\Http\Controllers\ContractAmendments;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Morilog\Jalali\jDateTime;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\AmendmentInContractTerms;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractPerformanceSecurity;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class AmendmentController extends Controller
{

    private $files = [
        'amendment_approval_form',
        'request_approval_amendment',
        'contract_amendment_agreement',
        'performance_guarantee_amendment',
        'npc_decision_form',
    ];

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $amendmentInContractConditionsStatus = $this->getElementTypeBySlug($this->getAllAmendmentType(), 'amendment-in-contract-conditions');
            $contractId = $request->query('contract_id');
            $amendments = Amendment::where('contract_id', $contractId)
                ->orderBy('id', 'asc')
                ->get();

            $confirmedStatus = true;
            $allData = [];
            $numberOfUnconfirmedAmendments = Amendment::where('contract_id', $contractId)
                ->whereNull('is_confirmed')
                ->orWhere('is_confirmed', 0)
                ->count();
            foreach ($amendments as &$amendment) {
                $attachments = $amendment->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
                foreach ($attachments as $key => $value) {
                    $index = $attachments[$key]['field_name'];
                    $att = [];
                    foreach ($attachments as $k => $v) {
                        if ($v['field_name'] == $index && $v['table_name'] == 'amendments') {
                            array_push($att, $v);
                        }
                    }
                    $amendment[$attachments[$key]['field_name']] = $att;
                }

                $previousAmendment = Amendment::where('contract_id', $contractId)
                    ->where('id', '<', $amendment['id'])
                    ->orderBy('id', 'desc')->first();

                if ($previousAmendment && $previousAmendment['is_confirmed'] && $previousAmendment['re_plan_id']) {
                    $isRePlanConfirmed = ContractRePlanningDateFAAPPVerification::
                    where('contract_re_planning_date_f_a_a_p_p_id', $previousAmendment['re_plan_id'])
                        ->where('is_confirmed', 1)->first();
                } else {
                    $isRePlanConfirmed = false;
                }
                if (
                    (
                        // If this is the first amendment we are taking

                        !$amendment['is_confirmed'] && $confirmedStatus && (
                            count($amendments) === 1 ||
                            $numberOfUnconfirmedAmendments === count($amendments)
                        )
                    ) ||
                    (
                        !$amendment['is_confirmed'] &&
                        $confirmedStatus &&
                        $isRePlanConfirmed
                    )
                    ||
                    (
                        $amendment['is_confirmed'] &&
                        // Check if previous amendment exists before accessing its keys
                        $previousAmendment !== null && 
                        $previousAmendment['is_confirmed'] && 
                        // Also check if the status variable/key exists
                        isset($amendmentInContractConditionsStatus['id']) && 
                        $previousAmendment['type_id'] === $amendmentInContractConditionsStatus['id']
                    )
                ) {
                    $amendment['confirmed_status'] = true;
                    $confirmedStatus = false;
                }
                $amendment['confirmed_status'] = true;
                array_push($allData, array_merge($amendment->toArray(), $this->getAmendmentTypeData($amendment)->toArray()));

            }
            return response()->json($allData);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['type_id'] = $data['type']['id'];
        $files = array_only($data, $this->files);

        try {
            DB::beginTransaction();

            if ($data['npc_decision_number']) {
                $data['peshnehad_decision_number'] = $data['npc_decision_number'];
            } else if ($data['peshnehad_number']) {
                $data['peshnehad_decision_number'] = $data['peshnehad_number'];
            }

            $amendment = Amendment::create(array_only($data, Amendment::$fields));

            if ($data['type']['slug'] == 'time-amendment') {
                $data['amendment_id'] = $amendment['id'];
                TimeAmendment::create(array_only($data, TimeAmendment::$fields));
            }

            if ($data['type']['slug'] == 'cost-amendment') {
                $data['amendment_id'] = $amendment['id'];
                $data['amended_end_date'] = $data['get_amended_end_date'];
                CostAmendment::create(array_only($data, CostAmendment::$fields));
            }

            if ($data['type']['slug'] == 'time-and-cost-amendment') {
                $data['amendment_id'] = $amendment['id'];
                TimeAndCostAmendment::create(array_only($data, TimeAndCostAmendment::$fields));
            }

            if ($data['type']['slug'] == 'amendment-in-contract-conditions') {
                $data['amendment_id'] = $amendment['id'];
                $data['amended_end_date'] = $data['get_amended_end_date'];
                AmendmentInContractTerms::create(array_only($data, AmendmentInContractTerms::$fields));
            }

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $amendment, $fieldName, $fileContent);
                }
            }

            DB::commit();

            return response()->json([], 201,
                [
                    'location' => $amendment['id']
                ]
            );
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\Amendment $amendment
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $contractId)
    {
        if ($request['amendment_id']) {
            $amendmentId = $request->query('amendment_id');
            $amendment = Amendment::where('id', $amendmentId)->first();
            $attachments = $amendment->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index && $v['table_name'] == 'amendments') {
                        array_push($att, $v);
                    }
                }
                $data[$attachments[$key]['field_name']] = $att;
            }

        }
        $actualPayment =
            ContractProgressPayment
                ::select('month_id', 'year')
                ->where('contract_id', $contractId)
                ->where('is_confirmed', true)
                ->orderBy('id', 'desc')->first();
        if ($actualPayment) {
            $amendmentStartDate = implode('-', jDateTime::toGregorian($actualPayment->year, $actualPayment->month_id + 1, 1));
            $data['replanning_start_date'] = $amendmentStartDate;
        }

        $contractDetails = ContractDetails::where('contract_id', $contractId)->first();
        if ($contractDetails) {
            $performanceSecurity = ContractPerformanceSecurity::select('start_date', 'end_date')->where('contract_detail_id', $contractDetails->id)->first();
            if ($performanceSecurity) {
                $data['performance_security_start_date'] = $performanceSecurity['start_date'];
                $data['performance_security_end_date'] = $performanceSecurity['end_date'];
            }

        }

        $amendment = Amendment::where('contract_id', $contractId)->orderBy('id', 'desc')->first();
        if ($amendment) {
            $timeAmendment = TimeAmendment::select('amended_end_date')->where('amendment_id', $amendment['id'])->orderBy('id', 'desc')->first();
            $timeAndCostAmendment = TimeAndCostAmendment::select('amended_end_date')->where('amendment_id', $amendment['id'])->orderBy('id', 'desc')->first();
            $costAmendment = CostAmendment::select('amended_end_date')->where('amendment_id', $amendment['id'])->orderBy('id', 'desc')->first();
            $amendmentInContractTerms = AmendmentInContractTerms::select('amended_end_date')->where('amendment_id', $amendment['id'])->orderBy('id', 'desc')->first();

            if ($timeAmendment) {
                $amendedEndDate = $timeAmendment['amended_end_date'];
            } elseif ($timeAndCostAmendment) {
                $amendedEndDate = $timeAndCostAmendment['amended_end_date'];
            } elseif ($costAmendment) {
                $amendedEndDate = $costAmendment['amended_end_date'];
            } elseif ($amendmentInContractTerms) {
                $amendedEndDate = $amendmentInContractTerms['amended_end_date'];
            }
            $data['get_amended_end_date'] = $amendedEndDate;
        } else {
            $amendedEndDate = $contractDetails['planned_end_date'];
            $data['get_amended_end_date'] = $amendedEndDate;
        }

        $is_performance_security_applicable = ContractDetails::select('is_performance_security_applicable')
            ->where('contract_id', $contractId)->get()->toArray();
        $data['is_performance_security_applicable'] = $is_performance_security_applicable ? $is_performance_security_applicable[0]['is_performance_security_applicable'] : '';

        $contract = Contract::select('is_above_threshold')->where('id', $contractId)->first();
        $data['is_contract_above_threshold'] = $contract['is_above_threshold'];


        return response()->json($data, 200);

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\Models\Amendment $amendment
     * @return \Illuminate\Http\Response
     */
    public function edit(Amendment $amendment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\Amendment $amendment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $amendmentId)
    {
        try {
            $currentDate = Carbon::now()->toDateTimeString();

            $data = $request->all();
            DB::beginTransaction();
            if (!array_key_exists('parent_id', $data)) {
                $data['type_id'] = $data['type']['id'];
                $files = array_only($data, $this->files);
                $amendment = Amendment::where('id', $amendmentId)->first();


                if ($data['is_above_threshold'] && ($data['type']['slug'] == 'cost-amendment' || $data['type']['slug'] == 'time-and-cost-amendment')) {
                    $data['peshnehad_decision_number'] = $data['npc_decision_number'];
                    Amendment::where('id', $amendmentId)->update(array_only($data, Amendment::$fields));
                } else {
                    $data['peshnehad_decision_number'] = $data['peshnehad_number'];
                    Amendment::where('id', $amendmentId)->update(array_only($data, Amendment::$fields));
                }

                if ($data['type']['slug'] == 'time-amendment') {
                    TimeAmendment::where('amendment_id', $amendmentId)->update(array_only($data, TimeAmendment::$fields));
                }

                if ($data['type']['slug'] == 'cost-amendment') {
                    if ($data['get_amended_end_date']) {
                        $data['amended_end_date'] = $data['get_amended_end_date'];
                    }
                    CostAmendment::where('amendment_id', $amendmentId)->update(array_only($data, CostAmendment::$fields));
                }

                if ($data['type']['slug'] == 'time-and-cost-amendment') {
                    TimeAndCostAmendment::where('amendment_id', $amendmentId)->update(array_only($data, TimeAndCostAmendment::$fields));
                }

                if ($data['type']['slug'] == 'amendment-in-contract-conditions') {
                    if ($data['get_amended_end_date']) {
                        $data['amended_end_date'] = $data['get_amended_end_date'];
                    }
                    AmendmentInContractTerms::where('amendment_id', $amendmentId)->update(array_only($data, AmendmentInContractTerms::$fields));
                }

                foreach ($files as $fieldName => $fileContent) {
                    if ($fileContent !== null) {
                        AttachmentController::saveFile($request, $amendment, $fieldName, $fileContent);
                    }
                }
            } else if (isset($data['is_published']) && $data['is_published']) {
                Amendment::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                            'published_date' => $currentDate
                        ]
                    );
            } else {
                Amendment::where('id', $data['id'])
                    ->update(
                        [
                            'is_approved' => $data['is_approved'],
                            'is_confirmed' => $data['is_confirmed'],
                            'is_published' => $data['is_published'],
                        ]
                    );
            }

            DB::commit();
            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\Models\Amendment $amendment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Amendment $amendment)
    {
        $isDeleted = Amendment::where('id', $amendment->id)->delete();
        if (!$isDeleted) {
            return response()->json([], 404);
        }
        return response()->json([], 204);

    }

    public function getAmendmentTypeData($contractAmendment)
    {

        switch ($contractAmendment['type_id']) {
            case $this->getElementTypeBySlug($this->getAllAmendmentType(), 'cost-amendment')['id']:
                return CostAmendment
                    ::select('is_above_threshold',
                        'amendment_amount',
                        'replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_amount',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case $this->getElementTypeBySlug($this->getAllAmendmentType(), 'time-amendment')['id']:
                return TimeAmendment
                    ::select('replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_end_date',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case $this->getElementTypeBySlug($this->getAllAmendmentType(), 'time-and-cost-amendment')['id']:
                return TimeAndCostAmendment
                    ::select('is_above_threshold',
                        'amendment_amount',
                        'replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_end_date',
                        'amended_performance_security_amount',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case $this->getElementTypeBySlug($this->getAllAmendmentType(), 'amendment-in-contract-conditions')['id']:
                return AmendmentInContractTerms
                    ::select('replanning_start_date',
                        'amended_end_date',
                        'terms_in_contract_amendment',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            default:
                throw new \Exception('Invalid amendment type.');
                break;

        }
    }

    public function getAllAmendmentType()
    {
        $path = 'api/dropDown/amendmentType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }
}
