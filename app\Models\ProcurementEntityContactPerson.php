<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ProcurementEntityContactPerson extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_id',
        'name',
        'last_name',
        'job',
        'email',
        'personal_contact_number',
        'office_contact_number'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
