<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRemarksColumnToCompanyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->string('licence_end_date')->nullable()->change();
            $table->string('joint_venture_company_role_id')->nullable()->change();
            $table->string('address')->nullable()->change();
            $table->string('company_id')->nullable()->change();
            $table->string('percentage_contract_share')->nullable()->change();
            $table->string('remarks')
                ->after('address');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('remarks');
        });
    }
}
