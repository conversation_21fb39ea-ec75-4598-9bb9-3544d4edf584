<?php

namespace NPA\ACPMS\Helpers;


use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use NPA\ACPMS\Mail\PrivateSectorNotification;

class EmailService
{
    public static function send($email, $id)
    {
        try {
            if (!Storage::exists('sendingMailLog.txt')) {
                Storage::put('sendingMailLog.txt', '');
            }
            $log = Storage::get('sendingMailLog.txt');
            $log .= "\n===============================================";
            $log .= "\nSending Email to " . $email . " starts at  :" . Carbon::now();
            $log .= "\n===============================================";
            if ($email) {
                Mail::to($email)
                    ->send(new PrivateSectorNotification($id));
            }
            $log .= "\n===============================================";
            $log .= "\nEmail Sent at" . Carbon::now();
            $log .= "\n===============================================";
        } catch (\Throwable $t) {
            $log .= "\n===============================================";
            $log .= "\nFailed to Send Email :" . $t->getTraceAsString();
            $log .= "\n===============================================";
            Storage::put('sendingMailLog.txt', $log);

        }
    }
}