<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Subcontract;
use NPA\ACPMS\Models\SubcontractApplicable;
use NPA\ACPMS\Models\SubcontractSigned;

class SubcontractController extends Controller
{

    public function index()
    {
        //
    }

    public function create()
    {
        //
    }


    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $data = $request->all();
            DB::beginTransaction();
            $subcontractId = Subcontract::create(array_only($request->all(), Subcontract::$fields))->id;
            if ($data['is_subcontract_applicable']) {
                $data['subcontract_id'] = $subcontractId;
                SubcontractApplicable::create(array_only($data, SubcontractApplicable::$fields))->id;
            }
            DB::commit();
            return response()->json([], 201);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    public function show($contractId)
    {
        $data = array();
        try {
            $subcontract = Subcontract::where('contract_id', $contractId)->first();
            if (!isset($subcontract)) {
                return response()->json($data, 200);
            }
            if (!$subcontract['is_subcontract_applicable']) {
                $data['subcontract'] = $subcontract;
                return response()->json($data, 200);
            }
            $subcontract_applicable = SubcontractApplicable::where('subcontract_id', $subcontract['id'])->first();
            if (!$subcontract_applicable['is_subcontract_signed']) {
                $data['subcontract'] = $subcontract;
                $data['subcontract_applicable'] = $subcontract_applicable;
                return response()->json($data, 200);
            }
            $data['subcontract'] = $subcontract;
            $data['subcontract_applicable'] = $subcontract_applicable;
            return response()->json($data, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function edit(Subcontract $subcontract)
    {
        //
    }


    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        try {
            DB::beginTransaction();
            if ($data['is_subcontract_signed'] === false) {
                $subcontract_applicable_id = SubcontractApplicable::where('subcontract_id', $id)->get()->toArray();
                if ($subcontract_applicable_id !== []) {
                    SubcontractSigned::where('subcontract_applicable_id', $subcontract_applicable_id[0]['id'])->delete();
                }
            }
            Subcontract::where('id', $id)->update(array_only($data, Subcontract::$fields));
            if ($data['is_subcontract_applicable'] === true || $data['is_subcontract_applicable'] === 1) {
                $data['subcontract_id'] = $id;
                $check = SubcontractApplicable::where('subcontract_id', $id)->get()->toArray();
                if ($check !== []) {
                    SubcontractApplicable::where('subcontract_id', $id)->update(array_only($data, SubcontractApplicable::$fields));
                } else {
                    SubcontractApplicable::Create(array_only($data, SubcontractApplicable::$fields));
                }
            } else {
                $check = SubcontractApplicable::where('subcontract_id', $id)->get()->toArray();
                if ($check !== []) {
                    SubcontractApplicable::where('subcontract_id', $id)->delete();
                }
            }
            DB::commit();
            return response()->json(['end'], 204);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    public function destroy(Subcontract $subcontract)
    {
        //
    }
}
