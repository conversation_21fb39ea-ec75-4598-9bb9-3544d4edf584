<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class TimeAmendment extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'amendment_id',
        'is_above_threshold_by_time',
        'current_time_threshold_value',
        'replanning_start_date',
        'amended_end_date',
        'amended_performance_security_end_date',
        'amendment_reasons'
    ];
}
