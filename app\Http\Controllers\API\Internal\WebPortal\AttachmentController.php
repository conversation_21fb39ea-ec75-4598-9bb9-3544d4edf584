<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\WebPortal;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\Contract;

class AttachmentController extends Controller
{
    public function index(Request $request)
    {
        $contract_id = $request->query('contract_id');
        $publishedDate = DB::select('select published_date from contracts where id = ' . $contract_id . ' ');
        $publishedDate = isset($publishedDate[0]) ? $publishedDate[0]->published_date : null;
        $contractDetailsId = DB::select('
            select id from contract_details where contract_id = ' . $contract_id . '
        ');
        $contractDetailsId = isset($contractDetailsId[0]) ? $contractDetailsId[0]->id : null;

        $contractApprovalId = DB::select('
            select id from contract_approvals where contract_id = ' . $contract_id . '
        ');
        $contractApprovalId = isset($contractApprovalId[0]) ? $contractApprovalId[0]->id : null;


        $companiesId = DB::select('
            select id 
            from companies
            where company_general_information_id = (
                select id
                from company_general_informations 
                where contract_id = ' . $contract_id . '
            )
        ');
        $temp = '0';
        for ($i = 0; $i < sizeof($companiesId); $i++) {
            $temp .= ',' . $companiesId[$i]->id;
        }
        $companiesId = $temp;

        $contractPlanningAdvanceId = DB::select('
            select id from contract_planning_advances where contract_id = ' . $contract_id . '
        ');
        $contractPlanningAdvanceId = isset($contractPlanningAdvanceId[0]) ? $contractPlanningAdvanceId[0]->id : null;


        $contractProgressPaymentsId = DB::select('
            select id from contract_progress_payments where contract_id = ' . $contract_id . ' and is_published  = true
        ');

        $temp = '0';
        for ($i = 0; $i < sizeof($contractProgressPaymentsId); $i++) {
            $temp .= ',' . $contractProgressPaymentsId[$i]->id;
        }
        $contractProgressPaymentsId = $temp;


        $contractStatusId = DB::select('
            select id from contract_statuses where contract_id = ' . $contract_id . '
        ');
        $contractStatusId = isset($contractStatusId[0]) ? $contractStatusId[0]->id : null;


        $contractRetentionReturnedId = DB::select('
            select id 
            from contract_retention_returneds
            where contract_retention_applicable_id = (
                select id 
                from contract_retention_applicables
                where contract_retention_id =
                   (
                    select id
                    from contract_retentions 
                    where contract_id = ' . $contract_id . '
                   
                   )
            )
        ');

        $temp = '0';
        for ($i = 0; $i < sizeof($contractRetentionReturnedId); $i++) {
            $temp .= ',' . $contractRetentionReturnedId[$i]->id;
        }
        $contractRetentionReturnedId = $temp;

        $contractProgressDelayPenaltiesId = DB::select('
            select id from contract_progress_delay_penalties where contract_id = ' . $contract_id . '
        ');
        $temp = '0';
        for ($i = 0; $i < sizeof($contractProgressDelayPenaltiesId); $i++) {
            $temp .= ',' . $contractProgressDelayPenaltiesId[$i]->id;
        }
        $contractProgressDelayPenaltiesId = $temp;

        $contractProgressDefectLiabilitiesId = DB::select('
            select id from contract_progress_defect_liabilities where contract_id = ' . $contract_id . '
        ');
        $temp = '0';
        for ($i = 0; $i < sizeof($contractProgressDefectLiabilitiesId); $i++) {
            $temp .= ',' . $contractProgressDefectLiabilitiesId[$i]->id;
        }
        $contractProgressDefectLiabilitiesId = $temp;


        $data = DB::select('
            select *
            from attachments
            where (foreign_key = ' . ($contractDetailsId ? $contractDetailsId : 0) . ' and table_name = "contract_details") or 
                (foreign_key = ' . ($contractStatusId ? $contractStatusId : 0) . ' and table_name = "contract_statuses") or 
                (foreign_key = ' . ($contractApprovalId ? $contractApprovalId : 0) . ' and table_name = "contract_approvals") or 
                (foreign_key in (' . ($companiesId ? $companiesId : 0) . ') and table_name = "companies") or 
                (foreign_key = ' . ($contractPlanningAdvanceId ? $contractPlanningAdvanceId : 0) . ' and table_name = "contract_planning_advances") or 
                (foreign_key in (' . ($contractProgressPaymentsId ? $contractProgressPaymentsId : 0) . ') and table_name = "contract_progress_payments") or 
                (foreign_key in (' . ($contractRetentionReturnedId ? $contractRetentionReturnedId : 0) . ') and table_name = "contract_retention_returneds") or 
                (foreign_key in (' . ($contractProgressDelayPenaltiesId ? $contractProgressDelayPenaltiesId : 0) . ') and table_name = "contract_progress_delay_penalties") or 
                (foreign_key in (' . ($contractProgressDefectLiabilitiesId ? $contractProgressDefectLiabilitiesId : 0) . ') and table_name = "contract_progress_defect_liabilities") or
                (foreign_key in (' . ($contract_id ? $contract_id : 0) . ') and table_name = "contracts")
        
        
        ');
        $data['published_date'] = $publishedDate;
        return response()->json($data, 200);
    }

    private function getAttachments($array, $tableName, $fieldName, $link = null, $contract = null)
    {
        $attachments = [];
        foreach ($array as $element) {
            $result = Attachment::select('id', 'created_at as modified_date', 'stored_path', 'language_id', 'format', 'table_name', 'field_name', 'original_name')
                ->where('foreign_key', $element['id'])
                ->where('table_name', $tableName)
                ->where('field_name', $fieldName)
                ->orderBy('id', 'desc')
                ->first();
            if ($result) {
                if ($fieldName === 'contract_M16_payment_form' || $fieldName === 'physical_progress_report') {
                    $result['link'] = $link ? ($link . '/' . $result['id']) : null;
                    $result['published_date'] = $element['published_date'];
                } else {
                    $result['link'] = $link;
                    $result['published_date'] = isset($contract['published_date']) ? $contract['published_date'] : null;
                }
                if ($link && $contract) {
                    unset($result['stored_path']);
                }
                array_push($attachments, $result);
            }
        }
        return $attachments;
    }

    public function finalAuditDocumentDownload($contract_id, Request $request)
    {
        try {
            $contract = Contract::where('id', $contract_id)
                ->where('is_published', 1)
                ->first();
            $contractStatus = $contract->contract_status;

            $finalAuditDocument = $this->getAttachments([$contractStatus->toArray()], 'contract_statuses', 'final_audit_document');
            if (isset($finalAuditDocument[0]['stored_path'])) {
                return response()->download(storage_path('app' . $finalAuditDocument[0]['stored_path']));
            }
            return response()->json([], 404);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function contractCloseoutCertificateDownload($contract_id, Request $request)
    {
        try {
            $contract = Contract::where('id', $contract_id)
                ->where('is_published', 1)
                ->first();
            $contractStatus = $contract->contract_status;
            $contractCloseoutCertificate = $this->getAttachments([$contractStatus->toArray()], 'contract_statuses', 'contract_closeout_certificate');
            if (isset($contractCloseoutCertificate[0]['stored_path'])) {
                return response()->download(storage_path('app' . $contractCloseoutCertificate[0]['stored_path']));
            }
            return response()->json([], 404);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function subcontractDownload($contract_id, Request $request)
    {
        try {
            $contract = Contract::where('id', $contract_id)
                ->where('is_published', 1)
                ->first();
            $subcontractDetails = $contract->subcontract->subcontract_details;
            $subcontract = $this->getAttachments([$subcontractDetails->toArray()], 'subcontracts', 'subcontract');
            if (isset($subcontract[0]['stored_path'])) {
                return response()->download(storage_path('app' . $subcontract[0]['stored_path']));
            }
            return response()->json([], 404);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function contractM16PaymentFormDownload($contract_id, $id, Request $request)
    {
        try {
            $id = $id;
            $contract = Contract::where('id', $contract_id)
                ->where('is_published', 1)
                ->first();
            $contractProgressPayments = $contract->contract_progress_payments;
            $contractM16PaymentForms = $this->getAttachments($contractProgressPayments->toArray(), 'contract_progress_payments', 'contract_M16_payment_form');
            if ($contractM16PaymentForms !== []) {
                foreach ($contractM16PaymentForms as $contractM16PaymentForm) {
                    if ($contractM16PaymentForm['id'] == $id) {
                        return response()->download(storage_path('app' . $contractM16PaymentForm['stored_path']));
                    }
                }
            }
            return response()->json([], 404);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function physicalProgressReportDownload($contract_id, $id, Request $request)
    {
        try {
            $contract = Contract::where('id', $contract_id)
                ->where('is_published', 1)
                ->first();
            $contractProgressPayments = $contract->contract_progress_payments;
            $contractM16PaymentForms = $this->getAttachments($contractProgressPayments->toArray(), 'contract_progress_payments', 'physical_progress_report');
            if ($contractM16PaymentForms !== []) {
                foreach ($contractM16PaymentForms as $contractM16PaymentForm) {
                    if ($contractM16PaymentForm['id'] == $id) {
                        return response()->download(storage_path('app' . $contractM16PaymentForm['stored_path']));
                    }
                }
            }
            return response()->json([], 404);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }


}
