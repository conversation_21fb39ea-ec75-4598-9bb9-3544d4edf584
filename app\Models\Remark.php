<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class Remark extends Model
{
    public static $fields = [
        'title',
        'contract_id',
        'alert_subcategory_id',
        'url',
    ];

    protected $guarded = ['id'];

    public function user_remarks()
    {
        return $this->hasMany('NPA\ACPMS\Models\UserRemarksStatusLog');
    }

    public function remark_messages()
    {
        return $this->hasMany('NPA\ACPMS\Models\RemarkMessage');
    }

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
