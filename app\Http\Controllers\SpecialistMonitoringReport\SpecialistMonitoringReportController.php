<?php

namespace NPA\ACPMS\Http\Controllers\SpecialistMonitoringReport;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use NPA\ACPMS\Helpers\Calculate;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\AfterDeliveryServicePeriod;
use NPA\ACPMS\Models\Amendment;
use NPA\ACPMS\Models\AmendmentInContractTerms;
use NPA\ACPMS\Models\ChallengesAndRemarks;
use NPA\ACPMS\Models\Company;
use NPA\ACPMS\Models\CompanyGeneralInformation;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\ContractApproval;
use NPA\ACPMS\Models\ContractDetails;
use NPA\ACPMS\Models\ContractDetailsDelayPenalty;
use NPA\ACPMS\Models\ContractPerformanceSecurity;
use NPA\ACPMS\Models\ContractPlanningFinanceAffairsAndPhysicalProgress;
use NPA\ACPMS\Models\ContractProgress;
use NPA\ACPMS\Models\ContractProgressAdvance;
use NPA\ACPMS\Models\ContractProgressDefectLiability;
use NPA\ACPMS\Models\ContractProgressDelayPenalty;
use NPA\ACPMS\Models\ContractProgressPayment;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\ContractStatusLog;
use NPA\ACPMS\Models\CostAmendment;
use NPA\ACPMS\Models\DomesticContractExecutionLocation;
use NPA\ACPMS\Models\LiquidatedDamagesAfterDeliveryService;
use NPA\ACPMS\Models\LiquidatedDamagesPeriod;
use NPA\ACPMS\Models\PARS\ProgressAnalysisReport;
use NPA\ACPMS\Models\ProvisionalSumAndContingency;
use NPA\ACPMS\Models\Subcontract;
use NPA\ACPMS\Models\SubcontractApplicable;
use NPA\ACPMS\Models\SubcontractSigned;
use NPA\ACPMS\Models\TimeAmendment;
use NPA\ACPMS\Models\TimeAndCostAmendment;

class SpecialistMonitoringReportController extends Controller
{
    public function show($id)
    {
        try {
            if (!$id) {
                return Error::composeResponse(new \Exception('Contract ID is required'));
            }
            $specialistMonitoringReport = self::aggregateReportData($id);
            return response()->json($specialistMonitoringReport);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }

    public static function getAmendmentTypeId($slug)
    {
        return self::getElementTypeBySlug(self::getAllAmendmentType(), $slug)['id'];
    }

    public static function getElementTypeBySlug($list, $slug)
    {
        return array_values(array_filter($list,
            function ($var) use ($slug) {
                return $var['slug'] === $slug;
            }))[0]; // this array will always have one element
    }

    public static function getAllAmendmentType()
    {
        $path = 'api/dropDown/amendmentType?path=api/dropDown/';
        $request = Http::get(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . $path, []);
        return json_decode($request->body, true);
    }

    public static function getAmendmentTypeData($contractAmendment)
    {

        switch ($contractAmendment['type_id']) {
            case self::getElementTypeBySlug(self::getAllAmendmentType(), 'cost-amendment')['id']:
                return CostAmendment
                    ::select('is_above_threshold',
                        'amendment_amount',
                        'replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_amount',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;

            case self::getElementTypeBySlug(self::getAllAmendmentType(), 'time-amendment')['id']:
                return TimeAmendment
                    ::select('replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_end_date',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case self::getElementTypeBySlug(self::getAllAmendmentType(), 'time-and-cost-amendment')['id']:
                return TimeAndCostAmendment
                    ::select('is_above_threshold',
                        'amendment_amount',
                        'replanning_start_date',
                        'amended_end_date',
                        'amended_performance_security_end_date',
                        'amended_performance_security_amount',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            case self::getElementTypeBySlug(self::getAllAmendmentType(), 'amendment-in-contract-conditions')['id']:
                return AmendmentInContractTerms
                    ::select('replanning_start_date',
                        'amended_end_date',
                        'terms_in_contract_amendment',
                        'amendment_reasons')
                    ->where('amendment_id', $contractAmendment['id'])
                    ->orderBy('id', 'desc')
                    ->first();
                break;
            default:
                throw new \Exception('Invalid amendment type.');
                break;

        }
    }

    public static function dayDifference($startDate, $endDate)
    {
        if (!$endDate || !$startDate) {
            return null;
        }
        return Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate));
    }

    public static function getEndDate($id, $contractLastStatus, $plannedEndDate)
    {


        if ($contractLastStatus['slug'] == 'contract-completion-defect-liability-period') {
            return $contractLastStatus['date'];
        } else {
            $amendments = Amendment::where('contract_id', $id)->orderBy('id', 'desc')->get();
            if ($amendments) {
                $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
                foreach ($amendments as $amendment) {
                    $typeSlug = '';
                    foreach ($amendmentTypes as $amendmentType) {
                        if ($amendment['type_id'] == $amendmentType['id']) {
                            $typeSlug = $amendmentType['slug'];
                            continue;
                        }
                    }

                    if ($typeSlug == 'time-amendment') {
                        $lastAmendmentEndDate = TimeAmendment::where('amendment_id', $amendment['id'])->first();
                        return $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }
                    if ($typeSlug == 'time-and-cost-amendment') {
                        $lastAmendmentEndDate = TimeAndCostAmendment::where('amendment_id', $amendment['id'])->first();
                        return $lastAmendmentEndDate['amended_end_date'];
                        break;
                    }

                }
            }
            return $plannedEndDate;

        }
    }

    public static function getVendorList($companyGeneralInformation)
    {
        $list = [];
        if (!isset($companyGeneralInformation['id'])) {
            return $list;
        }
        $companies = Company::select('joint_venture_company_role_id', 'license_number')->where('company_general_information_id', $companyGeneralInformation['id'])->get();
        if (count($companies) < 1) {
            return $list;
        }
        $queryString = '?search[license_number][match]=in&';
        foreach ($companies as $company) {
            $queryString = $queryString . 'search[license_number][value][]=' . $company['license_number'] . '&';
        }
        $remoteRequest = Http::get(config('custom.CUSTOM_API_INTERNAL_AVRCS_BASE_URL') . 'api/specific/company' . $queryString, []);
        $remoteRequestData = json_decode($remoteRequest->body, true);
        foreach ($companies as $company) {
            foreach ($remoteRequestData as $element) {
                if ($company['license_number'] == $element['license_number']) {
                    $company['vendor_name'] = $element['full_name'];
                    $company['is_leader'] = $element['is_leader'];
                }
            }
            // $companyRole = null;
            // if (isset($company['joint_venture_company_role_id'])) {
            //     $companyRole = DropDowns::getById(
            //         config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/jointVentureCompanyRole',
            //         $company['joint_venture_company_role_id'])['slug'];
            // }
            // $companyRole === 'leader' || $company['joint_venture_company_role_id'] == null ? $company['is_leader'] = 1 : $company['is_leader'] = 0;
        }
        foreach ($companies as $company) {
            $list[] = array('vendor_name' => $company['vendor_name'],
                'is_leader' => $company['is_leader']);
        }
        return $list;
    }

    public static function getExecutionLocation($contractLocations)
    {

        $LocationList = [];
        if (count($contractLocations) < 1) {
            return $LocationList;
        }
        $districtIds = [];
        foreach ($contractLocations as &$contractLocation) {
            array_push($districtIds, $contractLocation->district_id);
        }
        $locations = DropDowns::getByIds(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL')
            . 'api/specific/geographicalLocation/district', $districtIds);

        foreach ($locations as &$location) {
            foreach ($contractLocations as $contractLocation) {
                if ($location['id'] == $contractLocation->district_id) {
                    if ($contractLocation->climate_situation_id != 0 && $contractLocation->climate_situation_id != null) {
                        $climateSituation = DropDowns::getById(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL')
                            . 'api/dropDown/climateSituation', $contractLocation->climate_situation_id);
                        array_push($LocationList, [
                            'village' => $contractLocation['village'],
                            'climate_situation' => $climateSituation['name_da'],
                            'district' => $location['name_da'],
                            'province' => $location['province']['name_da'],
                            'zone' => $location['province']['zone']['name_da']
                        ]);
                    }
                }
            }
        }
        return $LocationList;
    }

    public static function getDonorList($project)
    {
        $list = [];
        foreach ($project['v2_project_donors'] as $donor) {
            if (isset($donor['donor_id'])) {
                $list[] = array('contract_donor' => DropDowns::getById(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sourceDonor',
                    $donor['donor_id'])['name_da']);
            }
        }
        return $list;
    }

    public static function getBudget($budgetCode)
    {
        $budget = DropDowns::getById(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/code', $budgetCode);
        return [
            'budget_code' => $budget['budget_code'],
            'budget_type' => $budget['budget_code_fund_type']['name_da']
        ];
    }

    public static function getPerformanceSecurities($contractDetails)
    {
        if (!$contractDetails['is_performance_security_applicable']) {
            return null;
        }
        $performanceSecurity = ContractPerformanceSecurity::where('contract_detail_id', $contractDetails['id'])->first();

        $amount = null;
        if (isset($performanceSecurity['amount'])) {
            $amount = $performanceSecurity['amount'];
        }
        $percentage = null;
        if (isset($performanceSecurity['amount']) && isset($contractDetails['actual_value'])) {
            $percentage = ($performanceSecurity['amount'] * 100) / ($contractDetails['actual_value']);
        }

        return [
            'start_date' => $performanceSecurity['start_date'],
            'end_date' => $performanceSecurity['end_date'],
            'amount' => $amount,
            'percentage' => $percentage,
            'extended_end_date' => null,
            'extended_amount' => null
        ];
    }

    public static function prepareContractValues($contractDetails)
    {

        if ($contractDetails['is_provisional_sum_and_contingency_applicable']) {
            $provisionalSumContingency = ProvisionalSumAndContingency::
            where('contract_detail_id', $contractDetails['id'])->first();

            $contractDetails['provisional_sum_and_contingency_value'] = $provisionalSumContingency['provisional_sum_and_contingency'];
        }
        return [
            'estimated_value' => $contractDetails['estimated_value'],
            'actual_value' => $contractDetails['actual_value'],
            'value_difference' => $contractDetails['estimated_value'] - $contractDetails['actual_value'],
            'percentage_difference' => self::getPercentageDifference($contractDetails['estimated_value'], $contractDetails['actual_value']),
            'provisional_sum_and_contingency_value' => $contractDetails['is_provisional_sum_and_contingency_applicable'] ?
                $contractDetails['provisional_sum_and_contingency_value'] : null,
            'is_provisional_sum_and_contingency_applicable' => $contractDetails['is_provisional_sum_and_contingency_applicable'] ? true : false];
    }

    public static function prepareContractDurations($contractDetails, $actualStartDate, $contractLastStatus)
    {
        $actualCloseOutDate = null;
        if ($contractLastStatus['slug'] == 'contract-close-out') {
            $actualCloseOutDate = $contractLastStatus['date'];

        }
        return [
            'planned_start_date' => $contractDetails['planned_start_date'],
            'planned_end_date' => $contractDetails['planned_end_date'],
            'planned_duration_days' => Carbon::parse($contractDetails['planned_start_date'])->diffInDays(Carbon::parse($contractDetails['planned_end_date'])),
            'actual_start_date' => $actualStartDate,
            'actual_completion_date' => self::getEndDate($contractDetails['contract_id'], $contractLastStatus, $contractDetails['planned_end_date']),
            'actual_close_out_date' => $actualCloseOutDate
        ];
    }

    public static function getPercentageDifference($estimatedValue, $actualValue)
    {
        if (!$estimatedValue && !$actualValue) {
            return 0;
        }
        if ($estimatedValue > $actualValue) {
            $planActualDifference = ($estimatedValue - $actualValue);
            $planActualDifferencePercentage = ($planActualDifference / $estimatedValue) * 100;
        } else {
            $planActualDifference = ($estimatedValue - $actualValue);
            $planActualDifferencePercentage = ($planActualDifference / $actualValue) * 100;
        }
        return $planActualDifferencePercentage;
    }

    public static function prepareContractProgress($id, $contractLastStatus)
    {
        $currentDate = Date::gregorianToJalali(Carbon::now()->toDateString());
        $split_date = explode('-', $currentDate);
        $currentYear = $split_date[2];
        $currentMonth = $split_date[1];
        $planning = DB::select('
                      select 
                        sum(amount) as amount , 
                        sum(physical_progress_percentage) as progress 
                      from contract_planning_finance_affairs_and_physical_progresses 
                      where (contract_id = ?) and 
                      (`year` < ? or 
                      (`year` = ? and month_id <= ? ))
                        ', [$id, (int)$currentYear, (int)$currentYear, (int)$currentMonth]);
        $actual = DB::select('
                      select 
                        sum(amount) as amount , 
                        sum(physical_progress_percentage) as progress
                      from contract_progress_payments 
                      where (contract_id = ? ) and 
                      (`year` < ? or 
                      (`year` = ? and month_id <= ? ))
                        ', [$id, (int)$currentYear, (int)$currentYear, (int)$currentMonth]);
        return [
            'contract_status' => $contractLastStatus['name_da'],
            'contract_status_slug' => $contractLastStatus['slug'],
            'planned_payments_till_now' => $planning[0]->amount,
            'actual_payments_till_now' => $actual[0]->amount,
            'planned_physical_progress_till_now' => $planning[0]->progress,
            'actual_physical_progress_till_now' => $actual[0]->progress,
        ];
    }

    public static function getLastStatus($id)
    {
        $contractLastStatusLog = ContractStatusLog::where('contract_id', $id)->orderBy('id', 'desc')->first();
        if (!$contractLastStatusLog) {
            return [];
        }
        $contractLastStatusLog['slug'] = null;
        $contractLastStatusLog['name_da'] = null;
        if (isset($contractLastStatusLog['status_id'])) {
            $status = DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus',
                $contractLastStatusLog['status_id']);
            $contractLastStatusLog['slug'] = $status['slug'];
            $contractLastStatusLog['name_da'] = $status['name_da'];
        }
        return $contractLastStatusLog;
    }

    public static function getAmendmentsData($id)
    {
        $amendmentInContractConditionsStatus = self::getElementTypeBySlug(self::getAllAmendmentType(), 'amendment-in-contract-conditions');
        $amendments1 = Amendment::select('id',
            'type_id',
            'amendment_start_date',
            'peshnehad_decision_number'
        )->where('contract_id', $id)
            ->orderBy('id', 'asc')
            ->get();
        $confirmedStatus = true;
        $amendmentAllData = [];

        $numberOfUnconfirmedAmendments = Amendment::select('id')->where('contract_id', $id)
            ->whereNull('is_confirmed')
            ->orWhere('is_confirmed', 0)
            ->count();
        foreach ($amendments1 as &$amendment) {
            $previousAmendment = Amendment::select('id',
                'type_id',
                're_plan_id',
                'is_confirmed')->where('contract_id', $id)
                ->where('id', '<', $amendment['id'])
                ->orderBy('id', 'desc')->first();

            if ($previousAmendment && $previousAmendment['is_confirmed'] && $previousAmendment['re_plan_id']) {
                $isRePlanConfirmed = ContractRePlanningDateFAAPPVerification::
                where('contract_re_planning_date_f_a_a_p_p_id', $previousAmendment['re_plan_id'])
                    ->where('is_confirmed', 1)->first();
            } else {
                $isRePlanConfirmed = false;
            }
            if (
                (
                    // If this is the first amendment we are taking
                    !$amendment['is_confirmed'] && $confirmedStatus && (
                        count($amendments1) === 1 ||
                        $numberOfUnconfirmedAmendments === count($amendments1)
                    )
                ) ||
                (
                    !$amendment['is_confirmed'] &&
                    $confirmedStatus &&
                    $isRePlanConfirmed
                )
                ||
                (
                    !$amendment['is_confirmed'] &&
                    $previousAmendment['is_confirmed'] &&
                    $previousAmendment['type_id'] === $amendmentInContractConditionsStatus['id']
                )
            ) {
                $amendment['confirmed_status'] = true;
                $confirmedStatus = false;
            }
            array_push($amendmentAllData, array_merge($amendment->toArray(), self::getAmendmentTypeData($amendment)->toArray()));
        }
        return $amendmentAllData;
    }

    public static function prepareContractAmendment($amendmentAllData, $actualStartDate, $ontrctValue)
    {
        $data['time'] = [];
        $data['cost'] = [];
        $data['time_and_cost'] = [];
        $data['condition'] = [];
        $type = null;
        $renewed_performance_total_amount = 0;
        foreach ($amendmentAllData as $amendmentData) {
            unset($amendmentData['id']);
            unset($amendmentData['is_confirmed']);
            unset($amendmentData['re_plan_id']);
            unset($amendmentData['is_above_threshold']);
            unset($amendmentData['replanning_start_date']);
            $is_performance_guaranty_renewal_is_legal = null;

            if (isset($amendmentData['amended_performance_security_amount'])) {
                $renewed_performance_total_amount += $amendmentData['amended_performance_security_amount'];
            }

            if (isset($amendmentData['amended_performance_security_end_date']) && $amendmentData['amended_end_date']) {
                $is_performance_guaranty_renewal_is_legal = (strtotime($amendmentData['amended_performance_security_end_date'])) > (strtotime(date('Y-m-d', strtotime($amendmentData['amended_end_date'] . ' + 27 days')))) ? 1 : 0;
            }

            if (isset($amendmentData['type_id'])) {
                $type = DropDowns::getById(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType',
                    $amendmentData['type_id'])['slug'];

                if ($type === 'time-amendment') {
                    unset($amendmentData['type_id']);
                    $amendmentData['amendment_duration'] = self::dayDifference($amendmentData['amendment_start_date'], $amendmentData['amended_end_date']);
                    $amendmentData['contract_duration_after_amend'] = self::dayDifference($actualStartDate, $amendmentData['amended_end_date']);
                    $amendmentData['is_performance_guaranty_renewal_is_legal'] = $is_performance_guaranty_renewal_is_legal;
                    array_push($data['time'], $amendmentData);
                } else if ($type === 'cost-amendment') {
                    unset($amendmentData['type_id']);
                    unset($amendmentData['amendment_duration']);
                    array_push($data['cost'], $amendmentData);
                } else if ($type === 'time-and-cost-amendment') {
                    unset($amendmentData['type_id']);
                    $amendmentData['amendment_duration'] = self::dayDifference($amendmentData['amendment_start_date'], $amendmentData['amended_end_date']);
                    $amendmentData['contract_duration_after_amend'] = self::dayDifference($actualStartDate, $amendmentData['amended_end_date']);
                    $amendmentData['is_performance_guaranty_renewal_is_legal'] = $is_performance_guaranty_renewal_is_legal;
                    array_push($data['time_and_cost'], $amendmentData);
                } else if ($type === 'amendment-in-contract-conditions') {
                    unset($amendmentData['type_id']);
                    unset($amendmentData['amendment_duration']);
                    array_push($data['condition'], $amendmentData);
                }
            } else {
                $data['time'] = [];
                $data['cost'] = [];
                $data['time_and_cost'] = [];
                $data['condition'] = [];
            }
        }
        $data['cost'] = collect($data['cost'])->sortBy('amendment_start_date')->toArray();
        $contractTotalValueWithoutAmend = $ontrctValue['actual_value'] + ($ontrctValue['provisional_sum_and_contingency_value'] ? $ontrctValue['provisional_sum_and_contingency_value'] : 0);
        foreach ($data['cost'] as &$costAmendment) {
            $contractTotalValueWithoutAmend += $costAmendment['amendment_amount'];
            $costAmendment['contract_value_after_amend'] = $contractTotalValueWithoutAmend;
        }
        $data['renewed_performance_total_amount'] = $renewed_performance_total_amount;
        return $data;
    }

    public static function prepareChallengesAndRemarks($challengesAndRemarks)
    {

        if (count($challengesAndRemarks) < 1) {
            return [];
        }

        $ids = [];
        $category_ids = [];
        $status_ids = [];
        $challengesAndRemark['creator'] = [];

        foreach ($challengesAndRemarks as $challengesAndRemark) {
            array_push($ids, $challengesAndRemark->creator_id);
            array_push($category_ids, $challengesAndRemark->category_id);
            array_push($status_ids, $challengesAndRemark->status_id);
        }

        $roleList = Helper::getRolesByIds($ids);
        $categoryIdsList = DropDowns::getByIds(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/challengesRemarksCategory', $category_ids);
        $statusIdsList = DropDowns::getByIds(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/challengesRemarksStatus', $status_ids);

        $data = [];
        foreach ($challengesAndRemarks as $challengesAndRemark) {
            foreach ($statusIdsList as $status) {
                foreach ($categoryIdsList as $category) {
                    foreach ($roleList as $role) {
                        if ($role['id'] == $challengesAndRemark->creator_id) {
                            array_push($data, [
                                'title' => $challengesAndRemark->title,
                                'creator' => $role['name'],
                                'category' => $category['name_da'],
                                'start_date' => $challengesAndRemark->start_date,
                                'description' => $challengesAndRemark->description,
                                'suggested_solution_procurement_entity' => $challengesAndRemark->suggested_solution_procurement_entity,
                                'suggested_solution_company' => $challengesAndRemark->suggested_solution_company,
                                'applied_solution' => $challengesAndRemark->applied_solution,
                                'updated_timestamp' => $challengesAndRemark->updated_at->format('Y-m-d H:m:s'),
                                'status' => $status['name_da'],
                            ]);
                        }
                    }
                }
            }
        }
        return $data;
    }

    public static function getContractPlanningPhysicalProgressChart($id)
    {

        $plans = [];
        $planPayments['payments'] = ContractPlanningFinanceAffairsAndPhysicalProgress::
        select('physical_progress_percentage', 'year', 'month_id')
            ->where('contract_id', $id)
            ->orderBy('id', 'asc')
            ->get();
        $planPayments['iteration_count'] = null;
        $planPayments['is_rePlan'] = false;
        array_push($plans, $planPayments);

        $actualProgresses = ContractProgressPayment::
        select('physical_progress_percentage', 'year', 'month_id')
            ->where('contract_id', $id)
            ->orderBy('id', 'asc')
            ->get();

        foreach ($plans as &$plan) {
            foreach ($plan['payments'] as &$planPayment) {
                foreach ($actualProgresses as $actualProgress) {
                    if ($planPayment['year'] == $actualProgress['year'] && $planPayment['month_id'] == $actualProgress['month_id']) {
                        $planPayment['planned_physical_progress_percentage'] = $planPayment['physical_progress_percentage'];
                        $planPayment['actual_physical_progress_percentage'] = $actualProgress['physical_progress_percentage'];
                        unset($planPayment['physical_progress_percentage']);

                    }
                }
                if (isset($planPayment['physical_progress_percentage'])) {
                    $planPayment['planned_physical_progress_percentage'] = $planPayment['physical_progress_percentage'];
                    $planPayment['actual_physical_progress_percentage'] = 0;
                    unset($planPayment['physical_progress_percentage']);
                }
            }
        }
        $rePlan['payments'] = DB::select('
            select 
                    crp.year,
                    crp.month_id,
                    crp.physical_progress_percentage as planned_physical_progress_percentage,
                    cpp.physical_progress_percentage as actual_physical_progress_percentage,
                    cr.iteration_count
            from contract_re_planning_finance_affairs_and_physical_progresses as crp
            join contract_re_planning_date_f_a_a_p_ps as cr
              on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id 
            left join contract_progress_payments as cpp
                on cpp.contract_id = cr.contract_id and
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year and 
                    cpp.is_approved = true
            where  cr.contract_id = ?
            order by crp.year, crp.month_id
        ', [$id]);

        return [
            'plan' => isset($plans[0]) ? $plans[0] : [],
            're_plan' => $rePlan
        ];
    }

    public static function getContractPlanningPaymentProgressChart($id)
    {
        $plan['payments'] = DB::select('
            select 
                cp.year,
                cp.month_id,
                ifnull(cp.amount, 0)  as planned_amount,
                ifnull(cpp.amount, 0) as actual_amount
                
            from contract_planning_finance_affairs_and_physical_progresses as cp
            left join contract_progress_payments as cpp
                on  cpp.contract_id = cp.contract_id and 
                    cpp.month_id = cp.month_id and 
                    cpp.year = cp.year and 
                    cpp.is_approved = true	
            join contract_planning_f_a_a_p_p_verifications as cpv
                on cpv.contract_id = cp.contract_id
                and  cpv.is_approved = true 
            join (
                select 
                    csl.id, csl.contract_id, csl.status_id 
                from contract_status_logs as csl
                join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value 
                    on csl.id = max_value.max_id
                ) as cs
                    on cs.contract_id = cp.contract_id
            join contracts as c
                on cp.contract_id = c.id
            where c.id = ?
            order by cp.year, cp.month_id
        ', [$id]);
        $rePlan['payments'] = DB::select('
            select 
                    crp.year,
                    crp.month_id,
                    if(crp.amount is null, null, crp.amount)  as planned_amount,
                    if(cpp.amount is null, null, cpp.amount) as actual_amount,
                    cr.iteration_count
            from contract_re_planning_finance_affairs_and_physical_progresses as crp
            join contract_re_planning_date_f_a_a_p_ps as cr
              on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id 
            left join contract_progress_payments as cpp
                on cpp.contract_id = cr.contract_id and
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year and 
                    cpp.is_approved = true
            join contracts as c
                on cr.contract_id = c.id
            where  cr.contract_id = ?
            order by crp.year, crp.month_id
        ', [$id]);
        return [
            'plan' => $plan,
            're_plan' => $rePlan
        ];
    }

    public static function getSchedulePerformingIndexChart($id)
    {
        $plan = DB::select('
                select
                    cpf.month_id,
                    cpf.year,
                    ifnull(cpp.physical_progress_percentage, 0) as ev,
                    ifnull(cpf.physical_progress_percentage, 0) as pv
                from contract_progress_payments as cpp
                join contract_planning_finance_affairs_and_physical_progresses as cpf
                    on cpf.contract_id = cpp.contract_id
                    and cpf.year = cpp.year
                    and cpf.month_id = cpp.month_id
                join contract_planning_f_a_a_p_p_verifications cpfv
                    on cpfv.contract_id = cpf.contract_id
                    and cpfv.is_approved = true
                where  cpp.contract_id = ?  
                order by cpp.year, cpp.month_id 
            ', [$id]);
        $rePlan = DB::select('
            select 
                    crp.year,
                    crp.month_id,
                    crp.status_id,
                    crp.physical_progress_percentage as pv,
                    cpp.physical_progress_percentage as ev,
                    cr.iteration_count
            from contract_re_planning_finance_affairs_and_physical_progresses as crp
            join contract_re_planning_date_f_a_a_p_ps as cr
              on  crp.contract_re_planning_date_f_a_a_p_p_id = cr.id 
            left join contract_progress_payments as cpp
                on cpp.contract_id = cr.contract_id and
                    cpp.month_id = crp.month_id and 
                    cpp.year = crp.year and 
                    cpp.is_approved = true
            join contracts as c
                on cr.contract_id = c.id
            where  cr.contract_id = ?
            order by cr.iteration_count, crp.year, crp.month_id
            ', [$id]);
        return [
            'plan' => $plan,
            're_plan' => $rePlan
        ];
    }

    public static function getCostPerformanceIndexChart($id)
    {
        return DB::select('
            select 
                cpp.month_id,
                cpp.year,
                ifnull(cpp.amount, 0) as amount,
                cpp.physical_progress_percentage,
                (
                    select 
                        ifnull(cd.actual_value, 0) +  
                        ifnull(amendment.amount, 0) + 
                        ifnull(psc.provisional_sum_and_contingency, 0)  as total_contract_value 
                    from contract_details as cd
                    join contract_details_verifications as cdv
                        on cd.id = cdv.contract_detail_id
                        and cdv.is_approved = true
                    left join (
                        select 
                            (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                            a.contract_id as contract_id
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.is_approved = true
                        group by  a.contract_id 
                    
                    ) as amendment
                          on cd.contract_id = amendment.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    where cd.contract_id = c.id
                ) as total_amount
            from contracts as c
            join contract_progress_payments as cpp
                on cpp.contract_id = c.id and
                cpp.is_approved = true
            where c.id = ?
            order by cpp.year, cpp.month_id
            ', [$id]);
    }

    public static function getCheckDB($id, $performance_security, $contract_actual_end_date, $contractDetails)
    {
        $plannedTotalAmount = ContractPlanningFinanceAffairsAndPhysicalProgress
            ::where('contract_id', $id)->sum('amount');
        $plannedPaymentsSchedule = Contract::select('id')->where('id', $id)->first();
        $SpecialistMonitoringReport['planned_total_amount'] = $plannedTotalAmount;
        $SpecialistMonitoringReport['planned_payments_schedule'] = $plannedPaymentsSchedule;
        $currentDate = Date::gregorianToJalali(Carbon::now()->toDateString());
        $split_date = explode('-', $currentDate);
        $currentYear = $split_date[2];
        $currentMonth = $split_date[1];
        $planTotalAmountAndPercentageTillNow = DB::select('
                       select sum(amount) as total_planned_payment_till_now , sum(physical_progress_percentage) as planned_physical_progress from contract_planning_finance_affairs_and_physical_progresses 
                       where (contract_id = ? ) 
                       and (
                          `year` < ? or (`year` = ? and month_id <= ?))
                        ', [$id, (int)$currentYear, (int)$currentYear, (int)$currentMonth]);
        $planedPayments = $planTotalAmountAndPercentageTillNow[0]->total_planned_payment_till_now;
        $planedProgress = $planTotalAmountAndPercentageTillNow[0]->planned_physical_progress;
        $actualPayments = ContractProgressPayment
            ::where('contract_id', $id)->sum('amount');
        $actualProgress = ContractProgressPayment
            ::where('contract_id', $id)->sum('physical_progress_percentage');
//-----
        $is_performance_guaranty_received = isset($performance_security) ?? (($performance_security['end_date'] === $contract_actual_end_date) && ($performance_security['percentage'] < 10 && $performance_security['percentage'] > 5)) ? 1 : 0;
        $subcontract_id = Subcontract::select('id')->where('contract_id', $id)->get()->toArray();
//-----
        $is_main_contract_have_subcontracts = 0;
        $is_subcontracting_legal = null;
        if ($subcontract_id && $subcontract_id[0]['id']) {
            $subcontract_applicable_id = SubcontractApplicable::select('id')->where('subcontract_id', $subcontract_id[0]['id'])->get()->toArray();
            if ($subcontract_applicable_id != []) {
                $subcontract_signed = SubcontractSigned::select('id', 'has_p_e_approved_in_written', 'percentage')->where('subcontract_applicable_id', $subcontract_applicable_id[0]['id'])->get();
                $subcontracts_total_percentage = 0;
                $subcontracts_p_e_approved_in_written_for_all = true;
                foreach ($subcontract_signed as $subcontract) {
                    $subcontracts_total_percentage += $subcontract->percentage;
                    if (!$subcontract->has_p_e_approved_in_written) {
                        $subcontracts_p_e_approved_in_written_for_all = false;
                    }
                }
                count($subcontract_signed) > 0 ? $is_main_contract_have_subcontracts = 1 : $is_main_contract_have_subcontracts = 0;
                $is_subcontracting_legal = ($subcontracts_total_percentage < 20 && $subcontracts_p_e_approved_in_written_for_all) ? 1 : 0;
            }
        }
//-----
        $contract_statuses = ContractStatus::where('contract_id', $id)->first();
        $contract_completion_document = null;
        $is_contract_termination_doc_issued = null;
        $security_repayment_document = null;
        $performance_guaranty_renewed = null;
        if ($contract_statuses && isset($contract_statuses)) {
            foreach ([$contract_statuses] as $contract_status) {
                $attachments = $contract_status->attachments()->select('field_name')->get();
                foreach ($attachments as $key => $value) {
                    $index = $attachments[$key]['field_name'];
                    $att = [];
                    foreach ($attachments as $k => $v) {
                        if ($v['field_name'] == $index) {
                            array_push($att, $v);
                        }
                    }
                    $contract_status[$attachments[$key]['field_name']] = $att;
                }
            }
            $contract_completion_document = $contract_statuses['contract_completion_document'] ? 1 : 0;
            $is_contract_termination_doc_issued = $contract_statuses['contract_closeout_certificate'] ? 1 : 0;
            $security_repayment_document = $contract_statuses['security_repayment_document'] ? 1 : 0;
            $performance_guaranty_renewed = $contract_statuses['contract_performance_guarantee_repayment'] ? 1 : 0;
        }
        (isset($actualProgress) && $actualProgress !== 0 && isset($planedProgress) && $planedProgress !== 0) ? ($actual_plan_physical_progress_diff = ($actualProgress - $planedProgress) / $planedProgress * 100) : ($actual_plan_physical_progress_diff = 0);
        (isset($actualPayments) && $actualPayments !== 0 && isset($planedPayments) && $planedPayments !== 0) ? ($actual_plan_payment_progress_diff = ($actualPayments - $planedPayments) / $planedPayments * 100) : ($actual_plan_payment_progress_diff = 0);

        $action_plan_attachment = $contractDetails->attachments()
            ->select('field_name', 'table_name')
            ->where('field_name', 'action_plan')
            ->where('table_name', 'contract_details')
            ->get()->count();
        $action_plan_attachment < 1 ? $action_plan = false : $action_plan = true;
        $checkDB = [
            'actual_plan_physical_progress_diff' => $actual_plan_physical_progress_diff,
            'actual_plan_payment_progress_diff' => $actual_plan_payment_progress_diff,
            'is_performance_guaranty_received' => $is_performance_guaranty_received,
            'is_subcontracting_legal' => $is_subcontracting_legal,
            'is_performance_guaranty_renewed' => $performance_guaranty_renewed,
            'is_completion_document_uploaded' => $contract_completion_document,
            'is_contract_termination_doc_issued' => $is_contract_termination_doc_issued,
            'is_supplies_legally_returned' => $security_repayment_document,
            'is_main_contract_have_subcontracts' => $is_main_contract_have_subcontracts,
            'is_action_plan_uploaded' => $action_plan
        ];
        return $checkDB;

    }

    public static function getDelayPenalties($contractDetails, $id)
    {
        $delayPenalties['delay_penalties_total_duration'] = 0;
        $delayPenalties['delay_penalties_total_percentage'] = 0;
        $delayPenalties['delay_penalty_period_name'] = null;
        $delayPenalties['is_delay_penalty_applicable'] = 0;
        if ($contractDetails &&
            $contractDetails['is_delay_penalty_applicable'] &&
            !$contractDetails['is_contract_category_transfers_or_rental_of_vehicles']) {
            $delayPenaltyPercentage = ContractDetailsDelayPenalty::select('percentage', 'period_id')->where('contract_detail_id', $contractDetails['id'])->first();
            $delayPenaltiesDuration = ContractProgressDelayPenalty::where('contract_id', $id)->sum('days_count');
            $delayPenaltiesCount = ContractProgressDelayPenalty::where('contract_id', $id)->count();
            $delayPenaltyTotalPercentage = $delayPenaltiesCount * ($delayPenaltyPercentage ? $delayPenaltyPercentage->percentage : 1);
            $delayPenalties['delay_penalties_total_duration'] = $delayPenaltiesDuration;
            $delayPenalties['delay_penalties_total_percentage'] = $delayPenaltyTotalPercentage;
            $delayPenalties['delay_penalty_period_name'] = null;
            if (isset($delayPenaltyPercentage['period_id'])) {
                $delayPenalties['delay_penalty_period_name'] = DropDowns::getById(
                    config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/delayPenaltyPeriod',
                    $delayPenaltyPercentage['period_id'])['name_da'];
            }
            $delayPenalties['is_delay_penalty_applicable'] = $contractDetails['is_delay_penalty_applicable'];
            return $delayPenalties;
        } else {
            return $delayPenalties;
        }

    }

    public static function getDefectLiabilities($id)
    {
        $defectLiabilityAmount = ContractProgressDefectLiability::where('contract_id', $id)->sum('amount');
        return $defectLiabilityAmount ? $defectLiabilityAmount : 0;
    }

    public static function prepareContractStopsDuration($id)
    {
        $contractStatusLogs = ContractStatusLog::where('contract_id', $id)->orderBy('date', 'asc')->get();
        $statuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $data = [];
        $found = false;
        $tempStatus = [];
        $tempStatusCancelled = [];
        $index = 0;
        foreach ($contractStatusLogs as $contractStatusLog) {
            $index++;
            $isNextStatus = true;
            foreach ($statuses as $status) {
                if ($status['id'] == $contractStatusLog->status_id) {
                    if ($status['slug'] == 'suspended') {
                        $found = true;
                        $isNextStatus = false;
                        $tempStatus = $contractStatusLog;
                    } else if ($status['slug'] == 'cancelled') {
                        $found = true;
                        $isNextStatus = false;
                        $tempStatusCancelled = $contractStatusLog;
                    }
                }
            }
            if ($found && $isNextStatus || ($found && $index == count($contractStatusLogs))) {
                $found = false;
                $start_date = isset($tempStatus->date) ? $tempStatus->date : null;
                $stop_reasons = isset($tempStatus->remarks) ? $tempStatus->remarks : null;
                $end_date = $index == count($contractStatusLogs) ?
                    Carbon::now()->subDays(1)->toDateString() :
                    Carbon::parse($contractStatusLog->date)->subDays(1)->toDateString();

                array_push($data, [
                    'progress_stop_start_date' => $start_date,
                    'progress_stop_end_date' => $end_date,
                    'progress_stop_duration' => self::dayDifference($start_date, $end_date),
                    'progress_stop_reasons' => $stop_reasons,

                ]);
                $data['progress_cancelled_reasons'] = null;
                if (isset($tempStatusCancelled->remarks)) {
                    $data['progress_cancelled_reasons'] = $tempStatusCancelled->remarks;
                }
            }
        }
        return $data;
    }

    public static function aggregateReportData($id)
    {
        $contract = Contract::select(
            'id',
            'contract_number',
            'contract_title',
            'procurement_type_id',
            'exchange_rate',
            'currency_id',
            'project_id',
            'procurement_method_id',
            'sector_id',
            'procurement_entity_id',
            'is_published'
        )->where('id', $id)->where('is_published', true)
            ->first();

        if (!$contract) {
            throw new \Exception('|||NPA-ACPMS-0010|||');
        }
        $contractDetails = ContractDetails::where('contract_id', $id)->first();
        $contractProgress = ContractProgress::where('contract_id', $id)->first();
        $contractApproval = ContractApproval::where('contract_id', $id)->first();
        $contractLastStatus = self::getLastStatus($id);
        $companyGeneralInformation = CompanyGeneralInformation::select('id')->where('contract_id', $id)->first();
        $liquidatedDamagesAfterDeliveryService = LiquidatedDamagesAfterDeliveryService::where('contract_id', $id)->first();
        $domesticContractExecutionLocation = DomesticContractExecutionLocation::where('contract_id', $id)->get();
        $challengesAndRemarks = ChallengesAndRemarks::where('contract_id', $id)->get();
        $liquidatedDamagesPeriod = NULL;
        $afterDeliveryServicePeriod = NULL;
        if ($liquidatedDamagesAfterDeliveryService['is_liquidated_damages_period_applicable']) {
            $liquidatedDamagesPeriod = LiquidatedDamagesPeriod::select('duration_in_month')->where('liquidated_da_after_de_id', $liquidatedDamagesAfterDeliveryService['id'])->first();
        }
        if ($liquidatedDamagesAfterDeliveryService['is_after_delivery_service_period_applicable']) {
            $afterDeliveryServicePeriod = AfterDeliveryServicePeriod::select('duration_in_month')->where('liquidated_da_after_de_id', $liquidatedDamagesAfterDeliveryService['id'])->first();
        }
        // $AETSRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/project-general-info'
        //     , [], ['contract_number' => $contract->contract_number]);
        // if ($AETSRequest->status_code > 400) {
        //     throw new \Exception($AETSRequest->body);
        // }
        $APPMSRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info'
        , [], ['ids' => [$contract->project_id]]);
        if ($APPMSRequest->status_code > 400) {
            throw new \Exception($APPMSRequest->body);
        }
        $contractEndDate = self::getEndDate($id, $contractLastStatus, $contractDetails['planned_end_date']);
        $projects = json_decode($APPMSRequest->body, true);

        if (count($projects) > 0) {
            $project = $projects[0];
            $data['contract_id'] = $id;
            $data['general_information'] = [
                'contract_number' => $contract['contract_number'],
                'contract_name' =>  $contract['contract_title'],
                'award_number' => $contractApproval['award_number'],
                'award_date' => $contractApproval['award_date'],
                'agreement_signature_date' => $contractDetails['agreement_signature_date'],
                'liquidated_damages_period' => $liquidatedDamagesPeriod ? $liquidatedDamagesPeriod['duration_in_month'] : null,
                'after_delivery_service_period' => $afterDeliveryServicePeriod ? $afterDeliveryServicePeriod['duration_in_month'] : null,
                'contract_value_with_amendments' => Calculate::totalAmount($id),
                'contract_durations_with_amendments' => self::dayDifference($contractProgress['actual_start_date'], $contractEndDate),
            ];

            $data['general_information']['procurement_method'] = isset($contract['procurement_method_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementMethod',
                $contract['procurement_method_id'])['name_da'] : null;

            $data['general_information']['currency_name'] = isset($contract['currency_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency',
                $contract['currency_id'])['name_da'] : null;

            $data['general_information']['contract_type'] = isset($projects[0]['contract_type_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractType',
                $projects[0]['contract_type_id'])['name_da'] : null;

            $data['general_information']['procurement_type'] = isset($contract['procurement_type_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType',
                $contract['procurement_type_id'])['name_da'] : null;

            $data['general_information']['sector'] = isset($contract['sector_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/sector',
                $contract['sector_id'])['name_da'] : null;

            $data['general_information']['procurement_entity'] = isset($contract['procurement_entity_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity',
                $contract['procurement_entity_id'])['name_da'] : null;

            $data['general_information']['selection_method'] = isset($project['selection_method_id']) ? DropDowns::getById(
                config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/selectionMethod',
                $project['selection_method_id'])['name_da'] : null;


            $data['vendors'] = self::getVendorList($companyGeneralInformation);
            $data['domestic_contract_execution_locations'] = self::getExecutionLocation($domesticContractExecutionLocation);
            $data['contract_donors'] = self::getDonorList($project);
            $data['budget'] = self::getBudget($project['budget_code_id']);
            $advance = ContractProgressAdvance::select('amount', 'date AS issue_date')->where('contract_id', $id)->first();
            if ($advance) {
                $advance['percentage'] = ($advance['amount'] * 100) / ($contractDetails['actual_value']);
            }
            $data['advance_payment_guaranty'] = $advance ? $advance->toArray() : null;
            $data['advance_payment_guaranty']['is_advanced_payment_guaranty_applicable'] = $contractDetails['is_advance_payment_applicable'];
            if (isset($data['advance_payment_guaranty']) && isset($data['advance_payment_guaranty']['percentage'])) {
                $data['advance_payment_guaranty']['is_advanced_payment_paid'] = $data['advance_payment_guaranty']['percentage'] < 30 ? 1 : 0;
            } else {
                $data['advance_payment_guaranty']['is_advanced_payment_paid'] = null;
            }
            $data['performance_security'] = self::getPerformanceSecurities($contractDetails);
            $data['values'] = self::prepareContractValues($contractDetails);
            $data['durations'] = self::prepareContractDurations($contractDetails, $contractProgress['actual_start_date'], $contractLastStatus);
            $data['progress'] = self::prepareContractProgress($id, $contractLastStatus);
            $amendmentAllData = self::getAmendmentsData($id);
            $data['amendment'] = self::prepareContractAmendment($amendmentAllData, $data['durations']['actual_start_date'], $data['values']);
            $data['challenges_and_remarks'] = self::prepareChallengesAndRemarks($challengesAndRemarks);
            $data['charts'] = [
                'planning_physical_progress_chart' => json_encode(self::getContractPlanningPhysicalProgressChart($id)),
                'planning_payment_progress_chart' => json_encode(self::getContractPlanningPaymentProgressChart($id)),
                'schedule_performing_index_chart' => json_encode(self::getSchedulePerformingIndexChart($id)),
                'cost_performance_index_chart' => json_encode(self::getCostPerformanceIndexChart($id)),
            ];
            $data['check_dbs'] = self::getCheckDB($id, $data['performance_security'], $contractEndDate, $contractDetails);
            $data['check_dbs']['renewed_performance_total_amount'] = $data['amendment']['renewed_performance_total_amount'] ? $data['amendment']['renewed_performance_total_amount'] : 0;
            $data['delay_penalties'] = self::getDelayPenalties($contractDetails, $id);
            $data['defect_liability']['contract_total_defect_liability'] = self::getDefectLiabilities($id);
            $data['defect_liability']['is_defect_liability_applicable'] = $contractDetails['is_defect_liability_applicable'];

            $stop_durations_and_cancelled_reasons = self::prepareContractStopsDuration($id);

            $data['general_information']['progress_cancelled_reasons'] = isset($stop_durations_and_cancelled_reasons['progress_cancelled_reasons']);

            unset($stop_durations_and_cancelled_reasons['progress_cancelled_reasons']);
            $data['stops_duration'] = $stop_durations_and_cancelled_reasons;
            return $data;
        } else {
            throw new \Exception('project not found');
        }

    }

    public function getReport($id)
    {
        try {
            $data = ProgressAnalysisReport::where('id', $id)
                ->with(['general_information' => function ($query) {
                    $query->with(['vendors', 'contract_execution_locations', 'donors', 'budget']);
                },
                    'performance_security',
                    'values',
                    'progress',
                    'durations',
                    'advance_payment_guaranty',
                    'time_amendments',
                    'cost_amendments',
                    'time_and_cost_amendments',
                    'condition_amendments',
                    'challenges_and_remarks',
                    'charts',
                    'check_dbs',
                    'defect_liabilities',
                    'delay_penalties',
                    'progress_stops',
                    'remarks'])->first()->toArray();
            $data['month'] = Date::getJalaliMonthName($data['month']);
            $countContracts = ProgressAnalysisReport::where('contract_id', $data['contract_id'])->count();
            $data['contract_value_with_amendments'] = $countContracts;
            $data['general_information']['contract_value_with_amendments'] = number_format((int)$data['general_information']['contract_value_with_amendments'], 2);
            $data['performance_security']['amount'] = number_format((int)$data['performance_security']['amount'], 2);
            $data['values']['estimated_value'] = number_format((int)$data['values']['estimated_value'], 2);
            $data['values']['actual_value'] = number_format((int)$data['values']['actual_value'], 2);
            $data['values']['value_difference'] = number_format((int)$data['values']['value_difference'], 2);
            $data['values']['percentage_difference'] = round((int)$data['values']['percentage_difference'], 2);
            $data['progress']['planned_payments_till_now'] = round((int)$data['progress']['planned_payments_till_now'], 2);
            $data['progress']['actual_payments_till_now'] = round((int)$data['progress']['actual_payments_till_now'], 2);
            $data['general_information']['isJv'] = count($data['general_information']['vendors']) > 1 ? 1 : 0;

            foreach ($data['cost_amendments'] as &$amendment) {
                $amendment['amendment_amount'] = number_format((int)$amendment['amendment_amount']);
                $amendment['contract_value_after_amend'] = number_format((int)$amendment['contract_value_after_amend']);
            }

            foreach ($data['time_amendments'] as &$amendment) {
                if ($amendment['amendment_start_date']) {
                    $amendment['amendment_start_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amendment_start_date']);
                }
                if ($amendment['amended_end_date']) {
                    $amendment['amended_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_end_date']);
                }
                if ($amendment['amended_performance_security_end_date']) {
                    $amendment['amended_performance_security_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_performance_security_end_date']);
                }
            }

            foreach ($data['time_and_cost_amendments'] as &$amendment) {
                if ($amendment['amendment_start_date']) {
                    $amendment['amendment_start_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amendment_start_date']);
                }
                if ($amendment['amended_end_date']) {
                    $amendment['amended_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_end_date']);
                }
                if ($amendment['amended_performance_security_end_date']) {
                    $amendment['amended_performance_security_end_date'] = Date::gregorianToJalaliInSlashFormat($amendment['amended_performance_security_end_date']);
                }
                $amendment['amended_performance_security_amount'] = number_format($amendment['amended_performance_security_amount'], 2);
                $amendment['amendment_amount'] = number_format($amendment['amendment_amount'], 2);
            }
            foreach ($data['cost_amendments'] as &$amendment) {
                if ($amendment['amended_performance_security_amount']) {
                    $amendment['amended_performance_security_amount'] = number_format($amendment['amended_performance_security_amount'], 2);
                }
            }
            foreach ($data['progress_stops'] as &$progressStop) {
                if ($progressStop['progress_stop_start_date']) {
                    $progressStop['progress_stop_start_date'] = Date::gregorianToJalaliInSlashFormat($progressStop['progress_stop_start_date']);
                }
                if ($progressStop['progress_stop_end_date']) {
                    $progressStop['progress_stop_end_date'] = Date::gregorianToJalaliInSlashFormat($progressStop['progress_stop_end_date']);
                }
            }

            $data['general_information']['award_till_signature_date_duration'] =
                self::dayDifference($data['general_information']['award_date'], $data['general_information']['agreement_signature_date']);
            $data['general_information']['signature_till_actual_date_duration'] =
                self::dayDifference($data['general_information']['agreement_signature_date'], $data['durations']['actual_start_date']);
            // fixing the charts data start

            $progress = json_decode($data['charts']['planning_physical_progress_chart'], true);
            $payments = json_decode($data['charts']['planning_payment_progress_chart'], true);
            foreach ($payments['plan']['payments'] as &$payment) {
                $payment['actual_amount'] = $payment['actual_amount'] / 1000000;
                $payment['planned_amount'] = $payment['planned_amount'] / 1000000;
            }
            foreach ($payments['re_plan']['payments'] as &$payment) {
                $payment['actual_amount'] = $payment['actual_amount'] / 1000000;
                $payment['planned_amount'] = $payment['planned_amount'] / 1000000;
            }
            $spi = json_decode($data['charts']['schedule_performing_index_chart'], true);
            $cpi = json_decode($data['charts']['cost_performance_index_chart'], true);
            $chart = [
                'progress' => [
                    'plan' => self::normalizeProgressData($progress['plan']['payments'], 'physical_progress_percentage'),
                    'plan_length' => count($progress['plan']['payments']) + 1
                ],
                'payments' => [
                    'plan' => self::normalizeProgressData($payments['plan']['payments'], 'amount'),
                    'plan_length' => count($payments['plan']['payments']) + 1
                ],
                'spi' => [
                    'plan' => self::normalizeSpiData($spi['plan']),
                    'plan_length' => count($payments['plan']['payments']) + 1
                ],
                'cpi' => []
            ];
            $progressRePlans = [];
            $paymentRePlans = [];
            $spiRePlans = [];
            foreach ($progress['re_plan']['payments'] as $payment) {
                if (!isset($progressRePlans['re_plan_' . $payment['iteration_count']])) {
                    $progressRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($progressRePlans['re_plan_' . $payment['iteration_count']], $payment);

            }
            foreach ($progressRePlans as $key => $value) {
                $chart['progress'][$key] = self::normalizeProgressData($value, 'physical_progress_percentage');
                $chart['progress'][$key . '_length'] = count($value) + 1;
            }


            foreach ($payments['re_plan']['payments'] as $payment) {
                if (!isset($paymentRePlans['re_plan_' . $payment['iteration_count']])) {
                    $paymentRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($paymentRePlans['re_plan_' . $payment['iteration_count']], $payment);

            }
            foreach ($paymentRePlans as $key => $value) {
                $chart['payments'][$key] = self::normalizeProgressData($value, 'amount');
                $chart['payments'][$key . '_length'] = count($value) + 1;
            }

            foreach ($spi['re_plan'] as &$payment) {
                if ($payment['iteration_count'] === 1) {
                    if ($payment['status_id'] === 2) {
                        foreach ($spi['plan'] as $planPayment) {
                            if ($payment['month_id'] === $planPayment['month_id'] && $payment['year'] === $planPayment['year']) {
                                $payment['pv'] = $planPayment['pv'];
                                $payment['ev'] = $planPayment['ev'];
                                $payment['update'] = true;
                            }
                        }
                    }
                }
                if ($payment['iteration_count'] > 1) {
                    if ($payment['status_id'] === 2) {
                        foreach ($spi['re_plan'] as $innerPayment) {
                            if ($payment['iteration_count'] === $innerPayment['iteration_count'] + 1
                                && $payment['month_id'] === $innerPayment['month_id']
                                && $payment['year'] === $innerPayment['year']) {
                                $payment['pv'] = $innerPayment['pv'];
                                $payment['ev'] = $innerPayment['ev'];
                                $payment['update_' . $payment['iteration_count']] = true;
                            }
                        }

                    }

                }
            }
            $lastRePlanKey = null;
            foreach ($spi['re_plan'] as $payment) {
                if (!isset($spiRePlans['re_plan_' . $payment['iteration_count']])) {
                    $spiRePlans['re_plan_' . $payment['iteration_count']] = [];
                }
                array_push($spiRePlans['re_plan_' . $payment['iteration_count']], $payment);
                $lastRePlanKey = 're_plan_' . $payment['iteration_count'];

            }
            foreach ($spiRePlans as $key => $value) {
                $chart['spi'][$key] = self::normalizeSpiData($value);
                $chart['spi'][$key . '_length'] = count($value) + 1;


            }
            if ($lastRePlanKey) {
                $chart['spi']['plan'] = $chart['spi'][$lastRePlanKey];
                $chart['spi']['plan_length'] = count($chart['spi'][$lastRePlanKey]) + 1;

            }


            $cpiData = [];
            $i = -1;
            foreach ($cpi as $payment) {
                $ev = ($payment['physical_progress_percentage'] / 100) * $payment['total_amount'];
                array_push($cpiData, [
                    "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                    "total_ac" => $i < 0 ? $payment['amount'] : $cpiData[$i]['total_ac'] + $payment['amount'],
                    "total_ev" => $i < 0 ? $ev : $cpiData[$i]['total_ev'] + $ev,
                    "index" => $i < 0 ? ($ev ? $payment['amount'] / $ev : 0) :
                        ($cpiData[$i]['total_ev'] ? $cpiData[$i]['total_ac'] / $cpiData[$i]['total_ev'] : 0)
                ]);
                $i++;
            }
            $chart['cpi'] = $cpiData;
            $chart['cpi_length'] = count($cpiData) + 1;
            $chart['length'] = count($cpiData);
            $data['chart'] = $chart;
            unset($data['charts']);
            $data['is_performance_security_applicable_text'] = $data['remarks']['is_performance_security_applicable'] ? 'تضمین اجرای قرارداد قابل اجرا می باشد.' : 'تضمین اجرای قرارداد قابل اجرا نمی باشد.';
            $data['is_performance_security_legally_text'] = '';
            if ($data['remarks']['is_performance_security_applicable']) {
                $data['is_performance_security_legally_text'] = $data['remarks']['is_performance_security_legally'] ?
                    'تضمین اجرای قراراد مطابق شرایط قرارداد و حکم هفتاد و هشتم طرزالعمل تدارکات ارائه گردیده است.' : 'تضمین اجرای قراراد مطابق شرایط قرارداد و حکم هفتاد و هشتم طرزالعمل تدارکات ارائه نگردیده است.';
            }
            $data['is_advance_payment_applicable_text'] = $data['remarks']['is_advance_payment_applicable'] ? 'تضمین پیش پرداخت قابل اجرا می باشد.' : 'تضمین پیش پرداخت قابل اجرا نمی باشد.';
            $data['is_advance_payment_legally_text'] = '';
            if ($data['remarks']['is_advance_payment_applicable']) {
                $data['is_advance_payment_legally_text'] = $data['remarks']['is_advance_payment_legally'] ?
                    'پیش پرداخت در مطابقت با شرایط قرارداد و حکم یکصد و یکم طرز العمل تدارکات اجرا گردیده است' : 'پیش پرداخت در مطابقت با شرایط قرارداد و حکم یکصد و یکم طرز العمل تدارکات اجرا نگردیده است';
            }
            // fixing the charts data ends
            count($data['time_amendments']) > 0 ? $data['time_amendments_length_text'] = '  تعداد تعدیلات زمانی: ' . count($data['time_amendments']) :
                $data['time_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل زمانی قرارداد در سیستم گزارش داده نشده است.';

            count($data['cost_amendments']) > 0 ? $data['cost_amendments_length_text'] = '  تعداد تعدیلات پولی: ' . count($data['cost_amendments']) :
                $data['cost_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل پولی قرارداد در سیستم گزارش داده نشده است.';

            count($data['condition_amendments']) > 0 ? $data['condition_amendments_length_text'] = '  تعداد تعدیلات در شرایط قرارداد: ' . count($data['condition_amendments']) :
                $data['condition_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل در شرایط قرارداد در سیستم گزارش داده نشده است.';

            count($data['time_and_cost_amendments']) > 0 ? $data['time_and_cost_amendments_length_text'] = '  تعداد تعدیلات پولی و زمانی: ' . count($data['time_and_cost_amendments']) :
                $data['time_and_cost_amendments_length_text'] = 'الی اکنون معلومات و اسناد مبنی بر تعدیل  پولی و زمانی قرارداد در سیستم گزارش داده نشده است.';

            $statues = ['cancelled', 'contract-completion-defect-liability-period', 'contract-close-out'];
            $data['subcontracting_text'] = 'الی اکنون اسناد مبنی بر موجودیت قرارداد فرعی در سیستم گزارش داده نشده است.';
            $data['completion_text_1'] = '';
            $data['close_out_text_1'] = '';
            $data['cancelled_text_1'] = '';
            if (in_array($data['progress']['contract_status_slug'], $statues)) {
                if ($data['progress']['contract_status_slug'] == 'cancelled') {
                    $data['cancelled_text_1'] = ' آیا فسخ قرارداد به اثر تخطی قراردادی صورت گرفته است؟ ' . ($data['remarks']['is_termination_cause_of_contractor_violation'] == 1 ? 'بلی' : ($data['remarks']['is_termination_cause_of_contractor_violation'] == 1 ? 'نخیر' : 'ثبت نشده'));
                    $data['cancelled_text_2'] = ' استرداد تضمین اجرای قرارداد مطابق فقره 7 حکم هفتاد وهشتم طرزالعمل تدارکات صورت  ' . ($data['remarks']['is_termination_cause_of_contractor_violation'] ? '' : 'ن') . 'گرفته است.';

                }
                if ($data['progress']['contract_status_slug'] == 'contract-completion-defect-liability-period') {
                    $data['completion_text_1'] = ' سند تکمیل فزیکی قرارداد به قراردادی ارائه ' . ($data['check_dbs']['is_completion_document_uploaded'] ? '' : 'ن') . 'گردیده است.';
                    $data['completion_text_2'] = ' استرداد تضمین اجرای قرارداد مطابق فقره 4 حکم هفتاد هشتم طرزالعمل تدارکات صورت ' . ($data['check_dbs']['is_performance_guaranty_renewed'] ? '' : 'ن') . 'گرفته است.';
                }
                if ($data['progress']['contract_status_slug'] == 'contract-close-out') {
                    $data['close_out_text_1'] = 'سند تصدیقنامه ختم قرارداد به قراردادی صادر  ' . ($data['check_dbs']['is_contract_termination_doc_issued'] ? '' : 'ن') . 'گردیده است.';
                    $data['close_out_text_2'] = '   استرداد تامینات مطابق فقره 4 حکم هشتاد ام طرزالعمل تدارکات به قراردادی صورت  ' . ($data['check_dbs']['is_supplies_legally_returned'] ? '' : 'ن') . 'گرفته است.';
                }
            }
            $data['time_cost_merged'] = array_merge($data['time_amendments'], $data['cost_amendments']);

            foreach ($data['time_cost_merged'] as &$amendment) {
                if (isset($amendment['amended_performance_security_end_date']) && $amendment['amended_performance_security_end_date'] == null) {
                    $amendment['amended_performance_security_end_date'] = 0;
                }
                $amendment['amended_performance_security_end_date'] = 0;
                if (isset($amendment['amended_performance_security_amount']) && $amendment['amended_performance_security_amount'] == null) {
                }
            }
            $data['delay_penalties_text'] = 'جریمه تاخیر در مقابل (' . $data['delay_penalties']['delay_penalties_total_duration'] . ') روز تاخیر در مطابقت با شرایط قرارداد ' .
                '(' . $data['delay_penalties']['delay_penalties_total_percentage'] . ') فیصد قیمت مجموعی قرارداد وضع گردیده است.';
            if (!$data['delay_penalties']['is_delay_penalty_applicable']) {
                $data['delay_penalties_text'] = 'قابل تطبیق نمی باشد.';
            }
            if ($data['check_dbs']['is_main_contract_have_subcontracts']) {
                $data['subcontracting_text'] = 'قرارداد  فرعی در مطابقت با شرایط قرارداد و حکم یکصد و پنجم طرزالعمل تدارکات عقد ' . ($data['check_dbs']['is_subcontracting_legal'] ? '' : 'ن') . 'گردیده است.';
            }

            // changes the date to jalali
            if ($data['general_information']['award_date']) {
                $data['general_information']['award_date'] = Date::gregorianToJalaliInSlashFormat($data['general_information']['award_date']);
            }
            if ($data['general_information']['agreement_signature_date']) {
                $data['general_information']['agreement_signature_date'] = Date::gregorianToJalaliInSlashFormat($data['general_information']['agreement_signature_date']);
            }
            if ($data['performance_security']['start_date']) {
                $data['performance_security']['start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['performance_security']['start_date']);
            }
            if ($data['performance_security']['end_date']) {
                $data['performance_security']['end_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['performance_security']['end_date']);
            }

            if ($data['durations']['planned_start_date']) {
                $data['durations']['planned_start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['planned_start_date']);
            }
            if ($data['durations']['planned_end_date']) {
                $data['durations']['planned_end_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['planned_end_date']);
            }
            if ($data['durations']['actual_close_out_date']) {
                $data['durations']['actual_close_out_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_close_out_date']);
            }
            if ($data['durations']['actual_start_date']) {
                $data['durations']['actual_start_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_start_date']);
            }
            if ($data['durations']['actual_completion_date']) {
                $data['durations']['actual_completion_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['durations']['actual_completion_date']);
            }
            if ($data['advance_payment_guaranty']['issue_date']) {
                $data['advance_payment_guaranty']['issue_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['advance_payment_guaranty']['issue_date']);
            }
            if ($data['advance_payment_guaranty']['expiration_date']) {
                $data['advance_payment_guaranty']['expiration_date'] =
                    Date::gregorianToJalaliInSlashFormat($data['advance_payment_guaranty']['expiration_date']);
            }
            $data['advance_payment_guaranties'] = [];
            if (isset($data['advance_payment_guaranty']['amount']) && $data['advance_payment_guaranty']['amount'] !== null) {
                $data['advance_payment_guaranty']['amount'] = number_format((int)$data['advance_payment_guaranty']['amount'], 2);
                $data['advance_payment_guaranty']['expiration_date'] = Date::gregorianToJalaliInSlashFormat($data['remarks']['advance_payment_guaranty_expiration_date']);
                $data['advance_payment_guaranties'] = [$data['advance_payment_guaranty']];

            }
            $data['challenges_and_remark_pe_text'] = 'از جانب اداره محترم کدام چالش در رابطه به پیشرفت قرارداد در سیستم نظارت از پیشرفت قراردادها  (ACPMS) گزارش داده نشده است. ';
            $data['challenges_and_remark_vendor_text'] = 'از جانب شرکت محترم قراردادی کدام چالش در رابطه به پیشرفت قرارداد در سیستم نظارت از پیشرفت قراردادها  (ACPMS) گزارش داده نشده است.';
            foreach ($data['challenges_and_remarks'] as &$challenges_and_remark) {
                if ($challenges_and_remark['creator'] == 'contract-manager') {
                    $challenges_and_remark['is_pe'] = 1;
                    $data['challenges_and_remark_pe_text'] = '';
                } else {
                    $challenges_and_remark['is_pe'] = 0;
                    $data['challenges_and_remark_vendor_text'] = '';
                }
                if ($challenges_and_remark['start_date']) {
                    $challenges_and_remark['start_date'] =
                        Date::gregorianToJalaliInSlashFormat($challenges_and_remark['start_date']);
                }
                if ($challenges_and_remark['created_at']) {
                    $challenges_and_remark['created_at'] =
                        Date::gregorianToJalaliInSlashFormat($challenges_and_remark['created_at']);
                }
            }
            $progressPercentage = $data['progress']['planned_physical_progress_till_now'] - $data['progress']['actual_physical_progress_till_now'];
            $data['progress']['physical_difference'] = $progressPercentage == 0 ? $progressPercentage : ($progressPercentage > 0 ? ('' . $progressPercentage . ' عقبمانی ') : ('' . $progressPercentage . ' پیشرفت '));
            $data['progress']['payment_difference'] = number_format($data['progress']['planned_payments_till_now'] - $data['progress']['actual_payments_till_now'], 2);
            $data['progress']['planned_physical'] = $data['progress']['planned_physical_progress_till_now'];
            $data['progress']['actual_physical'] = $data['progress']['actual_physical_progress_till_now'];
            $data['durations']['actual_start_date_not_reversed'] = $data['durations']['actual_start_date'];
            $data['durations']['actual_start_date'] = self::reversDate($data['durations']['actual_start_date']);
            $data['general_information']['agreement_signature_date_reverse'] = self::reversDate($data['general_information']['agreement_signature_date']);
            $data['values']['provisional_sum_and_contingency_value'] = number_format((int)$data['values']['provisional_sum_and_contingency_value'], 2);
            $data['values']['provisional_sum_and_contingency_value'] = $data['values']['provisional_sum_and_contingency_value'] . ' ' . $data['general_information']['currency_name'];
            $data['all_remarks'] = DB::select('
                select
                   par.id,
                   pcar.implemented_suggestions,
                   pcar.not_implemented_suggestions,
                   pcar.old_suggestion,
                   pcar.implemented_suggestions_vendor,
                   pcar.not_implemented_suggestions_vendor,
                   pcar.old_suggestions_vendor
                from pars_remarks as pcar
                join progress_analysis_reports as par
                  on par.id = pcar.p_a_r_id
                where par.contract_id = ' . $data['contract_id'] . ' and par.id <= ' . $data['id'] . ' and par.id not in (
                    select min(id)
                    from progress_analysis_reports
                    where contract_id = ' . $data['contract_id'] . '
                ) order by id asc
        ');
            $data['progress']['planned_payment'] = number_format($data['progress']['planned_payments_till_now'], 2);
            $data['progress']['actual_payment'] = number_format($data['progress']['actual_payments_till_now'], 2);

//            return response()->json($data, 200);
            $request = Http::post(config('custom.CUSTOM_API_INTERNAL_ARG_BASE_URL') . 'api/acpms/specialist-report'
                , []
                , $data);

            if ($request->status_code >= 300) {
                throw new Error('|||NPA-USM-0004|||', $request->body);
            }
            return response($request->body, 200);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);

        }


    }

    public static function reversDate($date)
    {

        $array = explode('/', $date);
        if (count($array) === 3) {
            return $array[2] . '/' . $array[1] . '/' . $array[0];
        }
        return null;
    }

    public static function normalizeProgressData($payments, $index)
    {
        $chartData = [];
        $i = -1;
        $planIndex = 'planned_' . $index;
        $actualIndex = 'actual_' . $index;
        foreach ($payments as $payment) {

            array_push($chartData, [
                "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                "plan" => $payment[$planIndex],
                "actual" => $payment[$actualIndex],
                "total_plan" => $i < 0 ? $payment[$planIndex] :
                    $chartData[$i]['total_plan'] + $payment[$planIndex],
                "total_actual" => $i < 0 ? $payment[$actualIndex] :
                    $chartData[$i]['total_actual'] + $payment[$actualIndex],
            ]);
            $i++;
        }
        return $chartData;
    }

    public static function normalizeSpiData($payments)
    {
        $data = [];
        $i = -1;
        foreach ($payments as $payment) {
            if ($payment['ev'] !== null) {
                array_push($data, [
                    "month" => Date::getJalaliMonthName($payment['month_id']) . $payment['year'],
                    "total_ev" => $i < 0 ? $payment['ev'] : $data[$i]['total_ev'] + $payment['ev'],
                    "total_pv" => $i < 0 ? $payment['pv'] : $data[$i]['total_pv'] + $payment['pv'],
                    "index" => $i < 0 ? ($payment['pv'] ? $payment['ev'] / $payment['pv'] : 0) :
                        ($payment['ev'] === null ? null : ($data[$i]['total_pv'] ? $data[$i]['total_ev'] / $data[$i]['total_pv'] : 0))
                ]);

            }
            $i++;
        }
        return $data;
    }

}
