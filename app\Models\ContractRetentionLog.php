<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRetentionLog extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'is_applicable',
        'contract_id',
        'contract_retention_id',
        'contract_status_log_id'
    ];

    public function contract_retention()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractRetention');
    }

    public function contract_status_log()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatusLog');
    }
    public function contract_retention_applicable_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionApplicableLog');
    }
    public function contract_retention_return_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionReturnedLog');
    }
    public function contract_retention_not_return_log()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractRetentionNotReturnedLog');
    }
}
