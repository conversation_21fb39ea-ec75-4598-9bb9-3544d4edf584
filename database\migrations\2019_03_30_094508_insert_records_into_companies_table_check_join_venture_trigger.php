<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class InsertRecordsIntoCompaniesTableCheckJoinVentureTrigger extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `inset_into_companies_trigger_check_if_join_venture`');
        DB::connection()->getpdo()->exec("
        create trigger inset_into_companies_trigger_check_if_join_venture
        before insert on companies
        for each row
        exit_label: begin
            declare is_join_venture integer default null;
            declare counter integer default null;
            select is_joint_venture into is_join_venture from company_general_informations as cgi
            join companies as c on cgi.id = c.company_general_information_id
            where cgi.id = new.company_general_information_id
            limit 1;
            select count(*) into counter from companies as c
            where c.company_general_information_id = new.company_general_information_id;
            if (is_join_venture = 0) and counter >=1 then
                  signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0008|||';
            else
                  leave exit_label;
            end if;
            set counter= null;
        end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `inset_into_companies_trigger_check_if_join_venture`');
    }
}
