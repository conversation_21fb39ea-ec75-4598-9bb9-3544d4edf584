<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddReportsAndProcurementEntityProfileToContext extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
//            DB::table('contexts')->insert(
//                array(
//                    ['name' => 'reports','role_id' => 1],
//                    ['name' => 'procurement-entity-profile','role_id' => 1],
//                    ['name' => 'reports','role_id' => 2],
//                    ['name' => 'procurement-entity-profile','role_id' => 2],
//                    ['name' => 'reports','role_id' => 5],
//                    ['name' => 'procurement-entity-profile','role_id' => 5],
//                )
//            );

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
