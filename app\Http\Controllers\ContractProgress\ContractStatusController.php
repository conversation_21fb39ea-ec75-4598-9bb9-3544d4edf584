<?php

namespace NPA\ACPMS\Http\Controllers\ContractProgress;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\ContractStatus;
use NPA\ACPMS\Models\ContractStatusNotSigned;
use NPA\ACPMS\Models\ContractStatusSuspend;

class ContractStatusController extends Controller
{
    private $files = [
        'contract_termination_approval',
        'contract_termination_request',
        'contract_closeout_report',
        'security_repayment_document',
        'contract_closeout_certificate',
        'handover_report',
        'handover_certificate',
        'final_audit_document',
        'contract_completion_report',
        'contract_completion_document',
        'contract_performance_guarantee_repayment',
        'contract_completion_certification',
        'temporary_completion_report',
        'temporary_completion_certificate',
        'suspension_letter',
    ];

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            return response()->json(ContractStatus::get()->all());
        } catch (\Throwable $t) {
            return Error::composeResponse(response(), $t);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Requestesponse
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);
        $data['status_id'] = $data['contract_status']['id'];
        try {
            DB::beginTransaction();
            $contractStatus = ContractStatus::create(array_only($data, ContractStatus::$fields));
            if ($data['contract_status']['slug'] === "suspended") {
                $data['contract_status_id'] = $contractStatus->id;
                ContractStatusSuspend::create(array_only($data, ContractStatusSuspend::$fields));
            } else if ($data['contract_status']['slug'] === "not-signed") {
                $data['contract_status_id'] = $contractStatus->id;
                ContractStatusNotSigned::create(array_only($data, ContractStatusNotSigned::$fields));
            }
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractStatus, $fieldName, $fileContent);
                }
//                if (is_array($files[$fieldName])) {
//                    if (array_key_exists('value', $files[$fieldName])) {
//                        AttachmentController::saveFile($request, $contractStatus, $fieldName, $fileContent);
//                    }
//                }
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $contractStatus->id
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $contract_statuses = ContractStatus::where('contract_id', $id)->first();
            if (!$contract_statuses) {
                return response()->json([], 404);
            }
            if ($contract_statuses && isset($contract_statuses)) {
                $contract_statuses['notSigned'] = ContractStatusNotSigned::where('contract_status_id', $contract_statuses['id'])->first();
                $contract_statuses['suspended'] = ContractStatusSuspend::where('contract_status_id', $contract_statuses['id'])->first();

                foreach ([$contract_statuses] as $contract_status) {
                    $attachments = $contract_status->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
                    foreach ($attachments as $key => $value) {
                        $index = $attachments[$key]['field_name'];
                        $att = [];
                        foreach ($attachments as $k => $v) {
                            if ($v['field_name'] == $index) {
                                array_push($att, $v);
                            }
                        }
                        $contract_status[$attachments[$key]['field_name']] = $att;
                    }
                }
            }
            return response()->json($contract_statuses);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['status_id'] = $data['contract_status']['id'];
        try {
            DB::beginTransaction();
            $contractStatus = ContractStatus::where('id', $id)->first();
            $contractStatus->update(array_only($data, ContractStatus::$fields));
            if ($data['contract_status']['slug'] === "suspended") {
                $contractStatusSuspendExist = ContractStatusSuspend::where('contract_status_id', $id)->first();
                $data['contract_status_id'] = $id;
                if ($contractStatusSuspendExist && ($contractStatusSuspendExist->count()) > 0) {
                    ContractStatusSuspend::where('id', $contractStatusSuspendExist['id'])->update(array_only($data, ContractStatusSuspend::$fields));
                } else {
                    ContractStatusSuspend::create(array_only($data, ContractStatusSuspend::$fields));
                }
            } else if ($data['contract_status']['slug'] === "not-signed") {
                $contractStatusNotSignedExist = ContractStatusNotSigned::where('contract_status_id', $id)->first();
                $data['contract_status_id'] = $id;
                if ($contractStatusNotSignedExist && ($contractStatusNotSignedExist->count()) > 0) {
                    ContractStatusNotSigned::where('id', $contractStatusNotSignedExist['id'])->update(array_only($data, ContractStatusNotSigned::$fields));
                } else {
                    ContractStatusNotSigned::create(array_only($data, ContractStatusNotSigned::$fields));
                }
            }
            $files = array_only($data, $this->files);
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $contractStatus, $fieldName, $fileContent);
                }
//                if (is_array($files[$fieldName])) {
//                    if (array_key_exists('value', $files[$fieldName])) {
//                        AttachmentController::saveFile($request, $contractStatus, $fieldName, $fileContent);
//                    }
//                }
            }
            DB::commit();
            return response()->json([], 201, [
                'location' => $contractStatus['id']
            ]);
        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public
    function destroy($id)
    {
        try {

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
