<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsChallengesAndRemarksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_challenges_and_remarks', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->string('title')->nullable();
            $table->string('creator')->nullable();
            $table->string('category')->nullable();
            $table->date('start_date')->nullable();
            $table->text('description')->nullable();
            $table->text('suggested_solution_procurement_entity')->nullable();
            $table->text('suggested_solution_company')->nullable();
            $table->text('applied_solution')->nullable();
            $table->dateTime('updated_timestamp')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_challenges_and_remarks');
    }
}
