<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;


use Carbon\Carbon;
use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\ContractRePlanningDateFAAPPVerification;

class ContractRePlanningDateFAAPPVerificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['contract_re_planning_date_f_a_a_p_p_id'] = $data['parent_id'];
        unset($data['parent_id']);
        if (isset($data['has_requested_to_unpublish'])) {
            unset($data['has_requested_to_unpublish']);
        }
        try {
            $contractRePlanningDateFAAPPVerification = ContractRePlanningDateFAAPPVerification
                ::Create(array_only($data, ContractRePlanningDateFAAPPVerification::$fields));
            return response()->json($contractRePlanningDateFAAPPVerification, 201, [
                'location' => $contractRePlanningDateFAAPPVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function show($contractRePlanningDateFAAPPId)
    {
        $contractRePlanningDateFAAPPVerification = ContractRePlanningDateFAAPPVerification::where('contract_re_planning_date_f_a_a_p_p_id', $contractRePlanningDateFAAPPId)->first();
        return response()->json($contractRePlanningDateFAAPPVerification, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->all();
        $data['contract_re_planning_date_f_a_a_p_p_id'] = $data['parent_id'];
        unset($data['parent_id']);
        unset($data['has_requested_to_unpublish']);
        $currentDate = Carbon::now()->toDateTimeString();
        try {
            if (isset($data['is_published']) && $data['is_published']) {
                $data['published_date'] = $currentDate;
                $contractRePlanningDateFAAPPVerification =
                    ContractRePlanningDateFAAPPVerification::where('contract_re_planning_date_f_a_a_p_p_id', $data['contract_re_planning_date_f_a_a_p_p_id'])->first();
                $contractRePlanningDateFAAPPVerification->update(array_only($data, ContractRePlanningDateFAAPPVerification::$fields));
            } else {
                $contractRePlanningDateFAAPPVerification =
                    ContractRePlanningDateFAAPPVerification::where('contract_re_planning_date_f_a_a_p_p_id', $data['contract_re_planning_date_f_a_a_p_p_id'])->first();
                $contractRePlanningDateFAAPPVerification->update(array_only($data, ContractRePlanningDateFAAPPVerification::$fields));
            }
            return response()->json([], 204, [
                'location' => $contractRePlanningDateFAAPPVerification['id']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractRePlanningDateFAAPPVerification $contractRePlanningDateFAAPPVerification)
    {
        //
    }
}
