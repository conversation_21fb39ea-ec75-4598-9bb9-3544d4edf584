<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FixingDateFunctions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared('
            create function `__mymod`(`a` int, `b` int) 
            returns bigint(20)
            deterministic
            begin
                return (a - b * floor(a / b));
            end
        ');
        DB::unprepared('
            create function `_jdmarray2`(`m` smallint) 
            returns smallint(2)
            deterministic
            begin
                case m
                    when 1 then return 31;
                    when 2 then return 31;
                    when 3 then return 31;
                    when 4 then return 31;
                    when 5 then return 31;
                    when 6 then return 31;
                    when 7 then return 30;
                    when 8 then return 30;
                    when 9 then return 30;
                    when 10 then return 30;
                    when 11 then return 30;
                    when 12 then return 29;
                end case;
            end
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::unprepared('drop function if exists __mymod');
        DB::unprepared('drop function if exists _jdmarray2');
    }
}
