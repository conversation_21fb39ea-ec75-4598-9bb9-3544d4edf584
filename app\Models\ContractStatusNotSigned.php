<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatusNotSigned extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'not_signed_reason',
        'contract_status_id',
    ];

    public function contract_status()
    {
        return $this->belongsTo('NPA\ACPMS\Models\ContractStatus');
    }

    public function contract_status_not_signed()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusNotSignedLog');
    }
}
