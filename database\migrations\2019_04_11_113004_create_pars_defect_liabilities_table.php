<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateParsDefectLiabilitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pars_defect_liabilities', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('p_a_r_id');
            $table->foreign('p_a_r_id')
                ->references('id')
                ->on('progress_analysis_reports')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table->Integer('contract_total_defect_liability')->nullable();
            $table->boolean('is_defect_liability_applicable')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pars_defect_liabilities');
    }
}
