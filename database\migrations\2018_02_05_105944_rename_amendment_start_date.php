<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameAmendmentStartDate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('amendment_in_contract_terms', function (Blueprint $table) {
            $table->renameColumn('amendment_start_date', 'replanning_start_date');
        });
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->renameColumn('amendment_start_date', 'replanning_start_date');
        });
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->renameColumn('amendment_start_date', 'replanning_start_date');
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->renameColumn('amendment_start_date', 'replanning_start_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('amendment_in_contract_terms', function (Blueprint $table) {
            $table->renameColumn('replanning_start_date', 'amendment_start_date');
        });
        Schema::table('cost_amendments', function (Blueprint $table) {
            $table->renameColumn('replanning_start_date', 'amendment_start_date');
        });
        Schema::table('time_amendments', function (Blueprint $table) {
            $table->renameColumn('replanning_start_date', 'amendment_start_date');
        });
        Schema::table('time_and_cost_amendments', function (Blueprint $table) {
            $table->renameColumn('replanning_start_date', 'amendment_start_date');
        });
    }
}
