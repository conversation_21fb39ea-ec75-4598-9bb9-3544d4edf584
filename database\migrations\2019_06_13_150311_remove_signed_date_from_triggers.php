<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class RemoveSignedDateFromTriggers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getpdo()->exec('drop trigger if exists `update_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger update_contracts_trigger_check_date_format
        before update on contracts
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
        DB::connection()->getpdo()->exec('drop trigger if exists `insert_contracts_trigger_check_date_format`');
        DB::connection()->getpdo()->exec("
        create trigger insert_contracts_trigger_check_date_format
        before insert on contracts
        for each row
        begin
            if (YEAR(new.published_date) < 1900) or
               (MONTH(new.published_date) = 00) or 
               (DAY(new.published_date) = 00)
               THEN
                   signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0009|||';
            end if;
        end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
