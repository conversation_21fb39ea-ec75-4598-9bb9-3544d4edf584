<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUniqueKeyToPaymentAfterDue extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->unique([
                'contract_id'
            ]);
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contract_progress_payment_after_due_dates', function (Blueprint $table) {
            $table->dropUnique('contract_progress_payment_after_due_dates_contract_id_unique');
        });
    }
}
