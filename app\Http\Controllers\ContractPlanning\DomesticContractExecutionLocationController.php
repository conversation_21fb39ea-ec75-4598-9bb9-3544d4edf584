<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use Illuminate\Http\Request;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Models\Contract;
use NPA\ACPMS\Models\DomesticContractExecutionLocation;

class DomesticContractExecutionLocationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        if (!$contractId) {
            return Error::composeResponse(new \Exception('Contract ID is required'));
        }
        $data = DomesticContractExecutionLocation::where('contract_id', $contractId)->get();
        $districtIds = [];
        foreach ($data as &$contractLocation) {
            array_push($districtIds, $contractLocation->district_id);
        }
        if ($districtIds) {
            $districts = DropDowns::getByIds(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/geographicalLocation/district', $districtIds);
            foreach ($data as &$contractLocation) {
                foreach ($districts as $district) {
                    if ($contractLocation['district_id'] === $district['id']) {
                        unset($district['province']);
                        $contractLocation->district = $district;
                    }
                }
            }
        }
        return response()->json($data, 200);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {

            $query = $request->all();
            $query['district_id'] = $query['district']['id'];
            unset($query['district']);

            unset($query['province']);
            unset($query['zone']);

            $query['climate_situation_id'] = $query['climate_situation']['id'];
            unset($query['climate_situation']);

            $createdId = Contract::find($request->input('contract_id'))
                ->domestic_contract_execution_location()
                ->create(array_only($query, DomesticContractExecutionLocation::$fields))->id;

            return response()->json([], 201, [
                'location' => $createdId
            ]);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\Models\DomesticContractExecutionLocation $domesticContractExecutionLocation
     * @return \Illuminate\Http\Response
     */
    public function show(DomesticContractExecutionLocation $domesticContractExecutionLocation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \NPA\ACPMS\Models\DomesticContractExecutionLocation $domesticContractExecutionLocation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $domesticContractExecutionLocation)
    {
        $query = $request->all();
        $query['district_id'] = $query['district']['id'];
        unset($query['district']);

        unset($query['province']);
        unset($query['zone']);

        $query['climate_situation_id'] = $query['climate_situation']['id'];
        unset($query['climate_situation']);

        try {
            $rowsCount = DomesticContractExecutionLocation::where('id', $domesticContractExecutionLocation)
                ->update(array_only($query, DomesticContractExecutionLocation::$fields));
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response()->json([], 201);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $result = DomesticContractExecutionLocation::where('id', $id)->delete();
            if (!$result) {
                throw Error::exceptionNotFound();
            }
            return response(null, 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}
