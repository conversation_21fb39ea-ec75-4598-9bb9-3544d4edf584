<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCurrencyAndExchangeRateColumnToContractPerformanceSecurities extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contract_performance_securities', function (Blueprint $table) {
            $table->integer('currency_id')->nullable()->after('amount');
        });
        Schema::table('contract_performance_securities', function (Blueprint $table) {
            $table->integer('exchange_rate')->nullable()->after('currency_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('contract_performance_securities', 'currency_id')) {
            Schema::table('contract_performance_securities', function (Blueprint $table) {
                $table->dropColumn('currency_id');
            });
        }
        if (Schema::hasColumn('contract_performance_securities', 'exchange_rate')) {
            Schema::table('contract_performance_securities', function (Blueprint $table) {
                $table->dropColumn('exchange_rate');
            });
        }
    }
}
