<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;


class CreateTempDistrictsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('temp_districts', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order')
                ->nullable();
            $table->string('name_da');
            $table->string('name_pa');
            $table->string('name_en');
            $table->boolean('is_center')
                ->default('0');
            $table->unsignedInteger('temp_province_id');
            $table->foreign('temp_province_id')
                ->references('id')->on('temp_provinces')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('slug')
                ->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('temp_districts');
    }
}
