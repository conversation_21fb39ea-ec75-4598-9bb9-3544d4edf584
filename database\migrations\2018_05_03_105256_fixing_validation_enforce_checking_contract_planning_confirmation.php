<?php

use Illuminate\Database\Migrations\Migration;

class FixingValidationEnforceCheckingContractPlanningConfirmation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection()->getPdo()->exec('drop procedure if exists `validation_enforce_checking_contract_planning_confirmation`');
        DB::connection()->getPdo()->exec("
        create procedure validation_enforce_checking_contract_planning_confirmation(
             in contractId int)
        begin
             declare is_confirmed boolean default null;
            
             select count(*)
             into is_confirmed
             from contracts as c
             where c.id = contractId and c.is_confirmed=1;
             
             if is_confirmed != 0 then
                signal sqlstate  '45000' set message_text = '|||NPA-ACPMS-0003|||';
             end if;
        end
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::connection()->getPdo()->exec('DROP procedure validation_enforce_checking_contract_planning_confirmation');
    }
}
