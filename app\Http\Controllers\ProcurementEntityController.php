<?php

namespace NPA\ACPMS\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Models\Contract;

class ProcurementEntityController extends Controller
{
    public function reportProfile(Request $request)
    {
        try {
            $procurementEntityId = $request->query('procurement_entity_id');
            if (!$procurementEntityId) {
                return 'hi';
                throw new Exception('procurement_entity_id is required');
            }

            return $this->loadProfile($procurementEntityId);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function reportProfileDownloadPdf(Request $request)
    {
        try {
            $procurementEntityId = $request->get('procurement_entity_id');
            if (!$procurementEntityId) {
                throw new Exception('procurement_entity_id is required');
            }
            $request = Http::post(
                config('custom.CUSTOM_API_INTERNAL_RM_BASE_URL') . 'api/report',
                [],
                [
                    'template' => [
                        'shortid' => config('custom.CUSTOM_RM_SHORT_ID_PDF')
                    ],
                    'data' => [
                        'htmlContents' => View::make('reports/procurement-entity-profile',
                            $this->loadProfile($procurementEntityId)
                        )->render()
                    ]
                ]
            );
            if ($request->status_code > 300) {

                //TODO: Handle
                throw new \Exception($request->body);
            }
            return response($request->body, 200, [
                'Content-Type' => $request->headers['content-type']
            ]);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    private function loadProfile($procurementEntityId)
    {
        $procurementTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/procurementType');
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $budgetCodes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/code');
        $budgetTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/fundsType');
        $procurementEntities = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity');

        $budgetIfOperationals = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/budgetCode/optionalNonOptional');
        $procurement_entity_name = '';
        for ($i = 0; $i < sizeof($procurementEntities); $i++) {
            if ($procurementEntities[$i]['id'] == $procurementEntityId)
                $procurement_entity_name = $procurementEntities[$i]['name_da'];
        }

        $data['procurement_entity'] = $procurement_entity_name;
        $data['contracts'] = DB::select('
                select 
                    count(c.id) -
                    count(case when (dpi.index_value >= 1 and spi.index_value >= 1) then 1 end) as behind_plan,
                    count(case when (dpi.index_value >= 1 and spi.index_value >= 1) then 1 end) as according_to_plan 
                from contracts as c
                left join(
                    select 
                        cd.contract_id,
                        sum(ifnull(cpp_join.amount, 0)) /
                        if(
                            sum(ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) * 
                            (sum(cpp_join.percentage) / 100)
                            ,
                            sum(ifnull(cd.actual_value, 0) + ifnull(psc.provisional_sum_and_contingency, 0) + ifnull(amendment.amount, 0)) * 
                            (sum(cpp_join.percentage) / 100)
                            , 1
                        )as index_value
                    from contract_details as cd
                    join contract_details_verifications cdv
                        on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                    left join (
                                select 
                                    (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                                from amendments as a
                                left join cost_amendments as ca 
                                    on ca.amendment_id = a.id
                                left join time_and_cost_amendments as tca
                                    on tca.amendment_id = a.id
                                where a.is_approved = true
                                group by  a.contract_id 
                            
                    ) as amendment
                        on cd.contract_id = amendment.contract_id
                    left join provisional_sum_and_contingencies as psc
                        on psc.contract_detail_id = cd.id	  
                    join (
                                select
                                    cpp.contract_id,
                                    sum(cpp.amount) as amount,
                                    sum(cpp.physical_progress_percentage) as percentage
                                    
                                from contract_progress_payments as cpp
                                where cpp.is_approved = true
                                group by cpp.contract_id
                
                    ) as cpp_join
                        on cd.contract_id = cpp_join.contract_id
                    group by cd.contract_id
                ) as dpi
                    on dpi.contract_id = c.id
                left join(
                     select id, ifnull(replan.index_value,plan.index_value) as index_value
                    from contracts 
                    join (
                        select 
                            cpf.contract_id,
                            ifnull(sum(ifnull(cpp.physical_progress_percentage, 0)) / sum(ifnull(cpf.physical_progress_percentage, 0)), 0) as index_value
                        from contract_planning_finance_affairs_and_physical_progresses as cpf
                        left join contract_progress_payments as cpp
                            on cpf.contract_id = cpp.contract_id
                            and cpf.year = cpp.year
                            and cpf.month_id = cpp.month_id
                            and cpp.is_approved = true
                        group by cpf.contract_id
                    ) as plan
                        on plan.contract_id = id
                
                
                
                    left join(
                        select 
                            crd.contract_id,
                            ifnull(sum(ifnull(cpp.physical_progress_percentage, 0)) / sum(ifnull(crppp.physical_progress_percentage, 0)), 0) as index_value
                        from contract_re_planning_finance_affairs_and_physical_progresses as crppp
                        join(
                                select inner_crd.contract_id, max(inner_crd.id) as max_id 
                                from contract_re_planning_date_f_a_a_p_ps as inner_crd
                                join contract_re_planning_date_f_a_a_p_p_verifications as crdv
                                    on inner_crd.id = crdv.contract_re_planning_date_f_a_a_p_p_id
                                    and crdv.is_approved = true
                                group by inner_crd.contract_id
                        ) as crd
                            on crd.max_id = crppp.contract_re_planning_date_f_a_a_p_p_id
                        left join contract_progress_payments as cpp
                            on crd.contract_id = cpp.contract_id
                            and crppp.year = cpp.year
                            and crppp.month_id = cpp.month_id
                            and cpp.is_approved = true
                        group by crd.contract_id
                    ) as replan
                        on replan.contract_id = id
                ) as spi
                    on spi.id = c.id
                where c.procurement_entity_id = ?
        ', [$procurementEntityId]);
        $data['total_contracts_count'] = DB::select('
                select 
                  count(c.id) as total_contracts_count 
                from contracts as c
                join contract_general_information_verifications as cgiv
                  on c.id = cgiv.contract_id
                where cgiv.is_approved = true
                and c.procurement_entity_id = ? 
            ', [$procurementEntityId]);
        $data['total_contracts_count'] =
            is_array($data['total_contracts_count']) ?
                $data['total_contracts_count'][0]->total_contracts_count :
                $data['total_contracts_count'];
        $contracts_count_based_procurement_type = DB::select('
                select 
                  count(c.id) as contracts_count, c.procurement_type_id
                from contracts as c
                join contract_general_information_verifications as cgiv
                  on c.id = cgiv.contract_id
                where cgiv.is_approved = true
                and c.procurement_entity_id = ?
                group by c.procurement_type_id;
            ', [$procurementEntityId]);

        for ($i = 0; $i < sizeOf($contracts_count_based_procurement_type); $i++) {
            $contracts_count_based_procurement_type[$i]->procurement_type =
                DropDowns::get(null, $contracts_count_based_procurement_type[$i]->procurement_type_id, $procurementTypes);
        }
        $data['contracts_count_based_procurement_types'] = $contracts_count_based_procurement_type;
        $grandTotalValues = DB::select('
                select	
                    c.id as contract_id,
                    c.project_id,
                    (
                        ifnull(cd.actual_value,0) +
                        ifnull(psc.provisional_sum_and_contingency,0) + ifnull
                        (
                            (
                                select 
                                    sum(
                                        ifnull(ca.amendment_amount, 0) + 
                                        ifnull(tca.amendment_amount, 0)
                                        ) as sumofAll
                                from  amendments as a
                                    left join cost_amendments as ca 
                                        on a.id = ca.amendment_id
                                    left join time_and_cost_amendments as tca
                                        on a.id = tca.amendment_id
                                where c.id = a.contract_id and a.is_approved = true
                            )
                        ,0)
                    )* if(c.exchange_rate, c.exchange_rate, 1) as contract_grand_total_value
                from contracts as c
                join contract_general_information_verifications as cgiv
                    on c.id = cgiv.contract_id and cgiv.is_approved = true
                join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications cdv
                    on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                    on cd.id = psc.contract_detail_id
                where c.procurement_entity_id = ? ;
         ', [$procurementEntityId]);
        $data['contract_grand_total_value'] = 0;
        foreach ($grandTotalValues as $gtv) {
            $data['contract_grand_total_value'] += $gtv->contract_grand_total_value;
        }
        $contracts_value_based_procurement_type = DB::select('
                select	
                  sum(
                        (
                            ifnull(cd.actual_value,0) +
                            ifnull(psc.provisional_sum_and_contingency,0) + ifnull
                            (
                                (
                                    select 
                                        sum(
                                            ifnull(ca.amendment_amount, 0) + 
                                            ifnull(tca.amendment_amount, 0)
                                            ) as sumofAll
                                    from  amendments as a
                                      left join cost_amendments as ca 
                                          on a.id = ca.amendment_id
                                      left join time_and_cost_amendments as tca
                                          on a.id = tca.amendment_id
                                    where c.id = a.contract_id and a.is_approved = true
                                )
                            ,0)
                        )* if(c.exchange_rate, c.exchange_rate, 1)) 
                        as contracts_total_value, c.procurement_type_id
                from contracts as c
                join contract_general_information_verifications as cgiv
                    on c.id = cgiv.contract_id and cgiv.is_approved = true
                join contract_details as cd
                    on c.id = cd.contract_id
                join contract_details_verifications cdv
                    on cd.id = cdv.contract_detail_id and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                    on cd.id = psc.contract_detail_id
                where c.procurement_entity_id = ?
                group by c.procurement_type_id;
         ', [$procurementEntityId]);
        for ($i = 0; $i < sizeOf($contracts_value_based_procurement_type); $i++) {
            $contracts_value_based_procurement_type[$i]->procurement_type =
                DropDowns::get(null, $contracts_value_based_procurement_type[$i]->procurement_type_id, $procurementTypes);
        }
        $data['contracts_value_based_procurement_types'] = $contracts_value_based_procurement_type;
        $data['contracts_actual_physical_progress'] = DB::select('
                select
                    round(sum(
                            (ifnull(cpp.total_physical_progress, 0) / 100) * 
                            (
                                ifnull(cd.actual_value, 0) + 
                                ifnull(psc.provisional_sum_and_contingency, 0) +
                                ifnull(amendment.amount, 0) * if(c.exchange_rate, c.exchange_rate, 1) 
                            ) 
                        ),4)  as actual_progress_value
                from contracts as c
                left join(
                            select 
                                inner_cpp.contract_id,
                                sum(inner_cpp.physical_progress_percentage) as total_physical_progress
                            from contract_progress_payments as inner_cpp
                            where inner_cpp.is_approved = true
                            group by inner_cpp.contract_id 
                        ) as cpp
                    on cpp.contract_id = c.id
                left join (
                             select 
                                    (sum(ifnull(ca.amendment_amount, 0)) +
                                    sum(ifnull(tca.amendment_amount,0))) as amount,
                                    a.contract_id as contract_id
                             from amendments as a
                             left join cost_amendments as ca 
                                 on ca.amendment_id = a.id
                             left join time_and_cost_amendments as tca
                                 on tca.amendment_id = a.id
                             where a.is_approved = true
                             group by  a.contract_id 
                         ) as amendment
                    on c.id = amendment.contract_id
                join contract_general_information_verifications as cgiv
                    on c.id = cgiv.contract_id and cgiv.is_approved = true
                join contract_details as cd
                    on cd.contract_id = c.id
                join contract_details_verifications as cdv
                    on cdv.contract_detail_id = cd.id
                    and cdv.is_approved = true
                left join provisional_sum_and_contingencies as psc
                    on psc.contract_detail_id = cd.id
                where c.procurement_entity_id = ? ;
        ', [$procurementEntityId]);
        $data['contracts_actual_physical_progress'] =
            is_array($data['contracts_actual_physical_progress']) ?
                $data['contracts_actual_physical_progress'][0]->actual_progress_value :
                $data['contracts_actual_physical_progress'];
        $data['contracts_actual_payments'] = DB::select('
                select
                    sum(
                            ifnull(cpp.amount, 0) * 
                            if(c.exchange_rate, c.exchange_rate, 1)
                        ) as paid_payments 
                from contracts as c
                join contract_general_information_verifications as cgiv
                    on c.id = cgiv.contract_id and cgiv.is_approved = true 
                join contract_progress_payments as cpp
                    on c.id = cpp.contract_id
                where c.procurement_entity_id = ? ;
           ', [$procurementEntityId]);
        $data['contracts_actual_payments'] =
            is_array($data['contracts_actual_payments']) ?
                $data['contracts_actual_payments'][0]->paid_payments :
                $data['contracts_actual_payments'];
        $data['difference_of_estimated_and_actual_amount'] = DB::select('
                select 
                    (sum(estimated_amount) - sum(actual_amount))
                    as difference_of_estimated_and_actual_amount
                from
                    (
                        select
                                ifnull((
                                    select 
                                        sum(
                                               ifnull(cp.amount, 0) * 
                                               if(c.exchange_rate, c.exchange_rate, 1)
                                            ) as estimated_amount
                                    from contract_planning_finance_affairs_and_physical_progresses as cp
                                    join contract_planning_f_a_a_p_p_verifications as cpv
                                        on cp.contract_id = cpv.contract_id and cpv.is_approved = true
                                    where c.id = cp.contract_id
                                ), 0) as estimated_amount
                                ,ifnull((
                                    select 
                                       sum(
                                               ifnull(cpp.amount, 0) *
                                               if(c.exchange_rate, c.exchange_rate, 1)
                                           ) as actual_amount
                                    from contract_progress_payments as cpp
                                    where c.id = cpp.contract_id and cpp.is_approved = true
                                ), 0) as actual_amount
                        from contracts as c
                        join contract_general_information_verifications as cgiv
                            on c.id = cgiv.contract_id and cgiv.is_approved = true
                        where c.procurement_entity_id = ?
                    ) as t;
        ', [$procurementEntityId]);
        $data['difference_of_estimated_and_actual_amount'] =
            is_array($data['difference_of_estimated_and_actual_amount']) ?
                $data['difference_of_estimated_and_actual_amount'][0]->difference_of_estimated_and_actual_amount :
                $data['difference_of_estimated_and_actual_amount'];
        $data['difference_of_estimated_and_actual_progress_percentage'] = DB::select('
            select
                round(
                    (sum(t.estimated_physical_progress_percentage) / count(t.contract_id)) -
                    (sum(t.actual_physical_progress_percentage) / count(t.contract_id)), 
                    4
                ) as difference_of_estimated_and_actual_physical_progress_percentage
            from
                (
                    select
                        c.id as contract_id,
                        c.procurement_entity_id,
                        ifnull((
                            select
                                sum(
                                       ifnull(cp.physical_progress_percentage, 0)
                                    ) as estimated_physical_progress_percentage
                            from contract_planning_finance_affairs_and_physical_progresses as cp
                            join contract_planning_f_a_a_p_p_verifications as cpv
                                on cp.contract_id = cpv.contract_id and cpv.is_approved = true
                            where c.id = cp.contract_id
                            group by cp.contract_id
                        ), 0) as estimated_physical_progress_percentage
                        ,ifnull((
                            select
                                sum(
                                       ifnull(cpp.physical_progress_percentage, 0)
                                   ) as actual_physical_progress_percentage
                            from contract_progress_payments as cpp
                            where c.id = cpp.contract_id and cpp.is_approved = true
                            group by cpp.contract_id
                        ), 0) as actual_physical_progress_percentage
                    from contracts as c
                    join contract_general_information_verifications as cgiv
                        on c.id = cgiv.contract_id and cgiv.is_approved = true
                    where c.procurement_entity_id = ?
                    group by c.procurement_entity_id, c.id
                ) as t
            group by t.procurement_entity_id;
        ', [$procurementEntityId]);
        $data['contracts_amendments_count_value'] = DB:: select('
            select
                  ifnull((
                        select
                              (
                                sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                              ) * if(c.exchange_rate, c.exchange_rate, 1) as total_amendment
                            from contracts as c
                            join contract_general_information_verifications as cgiv
                                on c.id = cgiv.contract_id and cgiv.is_approved = true
                            join amendments as a
                                on a.contract_id = c.id and a.is_approved = true
                            left join cost_amendments as ca
                                on ca.amendment_id = a.id
                            left join time_and_cost_amendments as tca
                                on tca.amendment_id = a.id
                            where c.procurement_entity_id = ?
                            group by c.exchange_rate
                  ),0) as total_amendment_value,
                  ifnull((
                        select
                              count(a.id) as amendment_count
                            from contracts as c
                            join contract_general_information_verifications as cgiv
                                on c.id = cgiv.contract_id and cgiv.is_approved = true
                            join amendments as a
                                on a.contract_id = c.id and a.is_approved = true
                            where c.procurement_entity_id = ?
                            group by a.id
                  ),0) as amendment_count
        ', [$procurementEntityId, $procurementEntityId]);
        $data['contracts_count_based_location'] = DB::select('
                select 
                    z.id as zone_id,
                    p.id as province_id, 
                  	d.id as district_id,
                    z.name_da as zone_name,
                    p.name_da as province_name,
                    d.name_da as district_name,
                    dc.village,
                    count(distinct(c.id)) as contracts_count
                from contracts as c 
                join domestic_contract_execution_locations as dc 
                    on dc.contract_id = c.id 
                join temp_districts as d
                    on d.id = dc.district_id
                join temp_provinces as p
                    on p.id = d.temp_province_id
                join temp_zones as z
                	on z.id = p.temp_zone_id
                where c.procurement_entity_id = ?
                group by z.id,p.id,d.id,
                         z.name_da,p.name_da,d.name_da,dc.village
        ', [$procurementEntityId]);
        $data['amendments_not_above_threshold'] = DB::select('
                select 
                    (
                        sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                    ) * if(c.exchange_rate, c.exchange_rate, 1) as total_amendment_not_above_threshold,
                    count(a.id) as amendments_count_not_above_threshold
                    from contracts as c
                    join amendments as a
                        on c.id = a.contract_id
                    left join cost_amendments as ca
                        on a.id = ca.amendment_id
                    left join time_amendments as ta
                        on a.id = ta.amendment_id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    and (ca.is_above_threshold = false or
                         ta.is_above_threshold_by_time = false or
                         tca.is_above_threshold = false)
                    and c.procurement_entity_id = ?
                    group by a.id,c.exchange_rate
        ', [$procurementEntityId]);
        $data['amendments_above_threshold'] = DB::select('
                select 
                    (
                        sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                    ) * if(c.exchange_rate, c.exchange_rate, 1) as total_amendment_above_threshold,
                    count(a.id) as amendments_count_above_threshold
                    from contracts as c
                    join amendments as a
                        on c.id = a.contract_id
                    left join cost_amendments as ca
                        on a.id = ca.amendment_id
                    left join time_amendments as ta
                        on a.id = ta.amendment_id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where a.is_approved = true
                    and (ca.is_above_threshold = true or
                         ta.is_above_threshold_by_time = true or
                         tca.is_above_threshold = true)
                    and c.procurement_entity_id = ?
                    group by a.id, c.exchange_rate
        ', [$procurementEntityId]);
        $contracts_amendments = DB::select('
                   select
                      (
                        sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))
                      ) * if(c.exchange_rate, c.exchange_rate, 1) as total_amendment,
                        c.procurement_type_id as procurement_type_id,
                        a.type_id as amendment_type,
                        count(a.id) as amendment_count
                    from contracts as c
                    join contract_general_information_verifications as cgiv
                        on c.id = cgiv.contract_id and cgiv.is_approved = true 
                    join amendments as a 
                        on a.contract_id = c.id and a.is_approved = true
                    left join cost_amendments as ca 
                        on ca.amendment_id = a.id
                    left join time_and_cost_amendments as tca
                        on tca.amendment_id = a.id
                    where c.procurement_entity_id = ?
                    group by c.procurement_type_id, 
                            a.type_id,
                            c.exchange_rate
        ', [$procurementEntityId]);
        for ($i = 0; $i < sizeOf($contracts_amendments); $i++) {
            $contracts_amendments[$i]->procurement_type =
                DropDowns::get(null, $contracts_amendments[$i]->procurement_type_id, $procurementTypes);
            $contracts_amendments[$i]->amendment_type =
                DropDowns::get(null, $contracts_amendments[$i]->amendment_type, $amendmentTypes);
        }
        $data['contracts_amendments'] = $contracts_amendments;
        $contract_count_and_value_based_status = DB::select('
            select 
                cs.status_id as status_id,
                c.procurement_type_id,
                count(distinct cs.contract_id) as status_count,
                sum((ifnull(cd.actual_value, 0)  + ifnull((
                        select sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0)) 
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.contract_id =cs.contract_id
                        
                    ), 0) + ifnull(psc.provisional_sum_and_contingency, 0)) *
                     if(c.exchange_rate, c.exchange_rate, 1) ) as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            where c.procurement_entity_id = ?
            group by cs.status_id,
            c.procurement_type_id
        ', [$procurementEntityId]);
        for ($i = 0; $i < sizeOf($contract_count_and_value_based_status); $i++) {
            $contract_count_and_value_based_status[$i]->procurement_type =
                DropDowns::get(null, $contract_count_and_value_based_status[$i]->procurement_type_id, $procurementTypes);
            $contract_count_and_value_based_status[$i]->contract_status =
                DropDowns::get(null, $contract_count_and_value_based_status[$i]->status_id, $contractStatuses);
        }
        $data['contracts_count_and_value_based_status'] = $contract_count_and_value_based_status;
        $total_contract_count_and_value_based_status = DB::select('
                select 
                cs.status_id as status_id,
                count(distinct cs.contract_id) as status_count,
                sum((ifnull(cd.actual_value, 0)  + ifnull((
                        select sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0)) 
                        from amendments as a
                        left join cost_amendments as ca 
                            on ca.amendment_id = a.id
                        left join time_and_cost_amendments as tca
                            on tca.amendment_id = a.id
                        where a.contract_id =cs.contract_id
                        
                    ), 0) + ifnull(psc.provisional_sum_and_contingency, 0)) *
                     if(c.exchange_rate, c.exchange_rate, 1) ) as status_value
            from (
                    select 
                        csl.id, csl.contract_id, csl.status_id 
                    from contract_status_logs as csl
                    join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value    	 
                    on csl.id = max_value.max_id
                ) as cs
            join contracts as c
                on c.id = cs.contract_id
            join contract_details as cd
                on cd.contract_id = c.id
            join contract_details_verifications as cdv
                on cdv.contract_detail_id = cd.id 
                and cdv.is_approved = true
            left join provisional_sum_and_contingencies as psc
                	on psc.contract_detail_id = cd.id
            where c.procurement_entity_id = ?
            group by cs.status_id
                ', [$procurementEntityId]);
        for ($i = 0; $i < sizeOf($total_contract_count_and_value_based_status); $i++) {
            $total_contract_count_and_value_based_status[$i]->contract_status =
                DropDowns::get(null, $total_contract_count_and_value_based_status[$i]->status_id, $contractStatuses);
        }
        $data['total_contract_count_and_value_based_status'] = $total_contract_count_and_value_based_status;
        $data['contracts_upload_information'] = DB::select('
          select
              (
                    select
                        round
                        (
                            contracts_planning_upload_count * 100 /
                            (contract_count * 10), 2
                        )
                        as contract_planning_percentage
                    from
                    (
                    select
                     (
                        select
                                    count(c.id) as contract_count
                                from contracts as c
                                join contract_general_information_verifications as cgiv
                                    on c.id = cgiv.contract_id and cgiv.is_approved = true
                                where c.procurement_entity_id = ?
                      ) as contract_count
                    ,(
                    select
                                    count(att.foreign_key) as count
                                from attachments as att
                                where (att.foreign_key in
                                        (
                                            select
                                                cd.id as contract_details_id
                                            from contracts as c
                                            join contract_details as cd
                                                on cd.contract_id  = c.id
                                            join contract_details_verifications as cdv
                                                on cdv.contract_detail_id = cd.id
                                                and cdv.is_approved = true
                                                and c.procurement_entity_id = ?
                                        )
                                        and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                                     ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                                     ,\'contract_performance_guarantee\')
                                )
                          ) as contracts_planning_upload_count
                        )as t
              ) as contract_planning_percentage,
                (
                  select
                    ifnull
                        (
                            round
                                (
                                    (contracts_payment_upload_count + advance_payment_contracts_upload_count) * 100 /
                                    (contracts_payments_count + advance_payment_contract_count)
                                , 2),0
                        )as payment_percentage
                from
                    (
                        select
                            (
                                select
                                    count(cpp.id) as contracts_payments_count
                                from contract_progress_payments as cpp
                                join contracts as c
                                    on c.id = cpp.contract_id
                                where cpp.is_approved = true
                                    and c.procurement_entity_id = ?
                            ) as contracts_payments_count
                            ,(
                                select
                                        count(att.foreign_key) as count
                                    from attachments as att
                                    where
                                    (
                                        att.foreign_key in
                                        (
                                            select
                                                cpp.id as contract_progress_payment_id
                                            from contracts as c
                                            join contract_progress_payments as cpp
                                                on cpp.contract_id  = c.id and cpp.is_approved = true
                                            where c.procurement_entity_id = ?
                                        )
                                        and att.field_name in (\'contract_M16_payment_form\')
                                    )

                            ) as contracts_payment_upload_count
                            ,(
                                select
                                    count(cpa.id) as advance_payment_contract_count
                                from contract_progress_advances as cpa
                                join contracts as c
                                    on c.id = cpa.contract_id
                                where cpa.is_approved = true
                                    and c.procurement_entity_id = ?
                            ) as advance_payment_contract_count
                            ,(
                                select
                                        count(att.foreign_key) as count
                                    from attachments as att
                                    where
                                    (
                                        att.foreign_key in
                                        (
                                            select
                                                cpa.id as contract_progress_advances_id
                                            from contracts as c
                                            join contract_progress_advances as cpa
                                                on cpa.contract_id  = c.id and cpa.is_approved = true
                                            where c.procurement_entity_id = ?
                                        )
                                        and att.field_name in (\'advance_payment_guarantee\')
                                    )

                            ) as advance_payment_contracts_upload_count
                        ) as t
                    ) as payment_percentage,
                    (select
                        ifnull
                          (
                            round
                                (
                                    (cost_amendments_upload_count +
                                    time_amendments_upload_count +
                                    time_and_cost_amendments_upload_count +
                                    amendment_in_contract_terms_upload_count) * 100 /
                                    (amendments_contracts_count * 4)
                                , 2),0
                          ) as amendment_percentage
                    from
                        (
                            select
                                (
                                    select
                                        count(a.id) as amendments_contracts_count
                                    from amendments as a
                                    join contracts as c
                                        on c.id = a.contract_id
                                    where a.is_approved = true
                                        and c.procurement_entity_id = ?
                                ) as amendments_contracts_count
                                ,(
                                    select
                                            count(att.foreign_key) as count
                                        from attachments as att
                                        where
                                        (
                                            att.foreign_key in
                                            (
                                                select
                                                    a.id as amendments_id
                                                from contracts as c
                                                join amendments as a
                                                    on a.contract_id  = c.id and a.is_approved = true
                                                join cost_amendments as ca
                                                    on ca.amendment_id = a.id
                                                where c.procurement_entity_id = ?
                                            )
                                            and att.field_name in (\'request_approval_amendment\',
                                            \'amendment_approval_form\'
                                            \'contract_amendment_agreement\',
                                            \'performance_guarantee_amendment\',
                                            \'npc_decision_form\'
                                            )
                                        )
                    
                                ) as cost_amendments_upload_count
                                ,(
                                        select
                                            count(att.foreign_key) as count
                                        from attachments as att
                                        where
                                        (
                                            att.foreign_key in
                                            (
                                                select
                                                    a.id as amendments_id
                                                from contracts as c
                                                join amendments as a
                                                    on a.contract_id  = c.id and a.is_approved = true
                                                join time_amendments as ta
                                                    on ta.amendment_id = a.id
                                                where c.procurement_entity_id = ?
                                            )
                                            and att.field_name in (\'request_approval_amendment\',
                                            \'amendment_approval_form\'
                                            \'contract_amendment_agreement\',
                                            \'performance_guarantee_amendment\',
                                            \'npc_decision_form\'
                                            )
                                        )
                    
                                ) as time_amendments_upload_count
                                ,(
                                        select
                                            count(att.foreign_key) as count
                                        from attachments as att
                                        where
                                        (
                                            att.foreign_key in
                                            (
                                                select
                                                    a.id as amendments_id
                                                from contracts as c
                                                join amendments as a
                                                    on a.contract_id  = c.id and a.is_approved = true
                                                join time_and_cost_amendments as tca
                                                    on tca.amendment_id = a.id
                                                where c.procurement_entity_id = ?
                                            )
                                            and att.field_name in (\'request_approval_amendment\',
                                            \'amendment_approval_form\'
                                            \'contract_amendment_agreement\',
                                            \'performance_guarantee_amendment\',
                                            \'npc_decision_form\'
                                            )
                                        )
                    
                                ) as time_and_cost_amendments_upload_count
                                ,(
                                        select
                                            count(att.foreign_key) as count
                                        from attachments as att
                                        where
                                        (
                                            att.foreign_key in
                                            (
                                                select
                                                    a.id as amendments_id
                                                from contracts as c
                                                join amendments as a
                                                    on a.contract_id  = c.id and a.is_approved = true
                                                join amendment_in_contract_terms as act
                                                    on act.amendment_id = a.id
                                                where c.procurement_entity_id = ?
                                            )
                                            and att.field_name in (\'request_approval_amendment\',
                                            \'amendment_approval_form\'
                                            \'contract_amendment_agreement\',
                                            \'performance_guarantee_amendment\',
                                            \'npc_decision_form\'
                                            )
                                        )
                    
                                ) as amendment_in_contract_terms_upload_count
                            ) as t
		) as amendment_percentage
            ', [$procurementEntityId, $procurementEntityId, $procurementEntityId, $procurementEntityId,
            $procurementEntityId, $procurementEntityId, $procurementEntityId, $procurementEntityId,
            $procurementEntityId, $procurementEntityId, $procurementEntityId]);
        $data['miscellaneous'] = DB::select('
                select
                  (
                        select
                          count(distinct c.id) as inserted_contracts
                        from contracts as c
                        join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                        where cgiv.is_approved = true
                          and c.procurement_entity_id = ?
                   ) as inserted_contracts,
                   (
                        select
                          count(c.id) as contracts_published_to_website
                        from contracts as c
                        join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                        where cgiv.is_approved = true
                            and c.is_published = true
                            and c.procurement_entity_id = ?
                   ) as contracts_published_to_website,
                   (
                        select
                          count(distinct c.id) as above_threshold_contracts
                        from contracts as c
                        join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                        where cgiv.is_approved = true
                            and c.is_above_threshold = true
                            and c.procurement_entity_id = ?
                    ) as above_threshold_contracts
                    ,(
                        select
                          count(distinct c.id) as below_threshold_contracts
                        from contracts as c
                        join contract_general_information_verifications as cgiv
                          on c.id = cgiv.contract_id
                        where cgiv.is_approved = true
                            and c.is_above_threshold = false
                            and c.procurement_entity_id = ?
                    ) as below_threshold_contracts,
                    (
                    select
                        round
                        (
                            contracts_upload_documents * 100 /
                            (contract_count * 15) , 2
                        )as contracts_upload_percentage
                    from(
                    select
                    (
                    select
                                        count(c.id ) as contract_count
                                    from contracts as c
                                    join contract_general_information_verifications as cgiv
                                        on c.id = cgiv.contract_id
                                    where cgiv.is_approved = true
                                        and c.procurement_entity_id = ?
                        ) as contract_count,
                        (

                    select
                                    count(distinct att.foreign_key) as count
                                from attachments as att
                                where (att.foreign_key in (
                                        select
                                            cd.id as contract_details_id
                                        from contracts as c
                                        join contract_details as cd
                                            on cd.contract_id  = c.id
                                        join contract_details_verifications as cdv
                                            on cdv.contract_detail_id = cd.id
                                            and cdv.is_approved = true
                                            and c.procurement_entity_id = ?
                                                        )
                                    and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                                     ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                                     ,\'contract_performance_guarantee\')
                                     ) or (att.foreign_key in (
                                            select
                                                cpa.id as cpa_id
                                            from contracts as c
                                            join contract_progress_advances as cpa
                                                on cpa.contract_id = c.id
                                                        )
                                    and att.field_name = \'advance_payment_guarantee\'
                                            )
                                    or (att.foreign_key in (
                                            select
                                                cs.id as cs_id
                                            from contracts as c
                                            join contract_statuses as cs
                                                on cs.contract_id = c.id
                                            join contract_status_verifications as csv
                                                on csv.contract_status_id = cs.id
                                                and csv.is_approved = true
                                                and c.procurement_entity_id = ?
                                                        )
                                    and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                                        )

                                    or (att.foreign_key in (
                                            select
                                                comp.id as comp_id
                                            from contracts as c
                                            join company_general_informations as cgi
                                                on cgi.contract_id = c.id
                                                and cgi.is_approved = true
                                            join companies as comp
                                                on comp.company_general_information_id = cgi.id
                                                and cgi.is_approved = true
                                                and c.procurement_entity_id = ?
                                                    )
                                    and att.field_name = \'joint_venture_licence\'
                                        )
                                    or (att.foreign_key in (
                                                select
                                                    c.id as c_id
                                                from contracts as c
                                                join company_general_informations as cgi
                                                    on cgi.contract_id = c.id
                                                    and cgi.is_approved = true
                                                    and c.procurement_entity_id = ?
                                    )
                                    and att.field_name = \'planned_payments_schedule\'
                                        )
                            ) as contracts_upload_documents
                        ) as t
                      ) as contracts_upload_percentage,
                      (
                    select
                        round
                        (
                            contracts_upload_documents * 100 /
                            (contract_count * 15) , 2
                        )as published_contracts_upload_percentage
                    from(
                    select
                    (
                    select
                                        count(c.id ) as contract_count
                                    from contracts as c
                                    join contract_general_information_verifications as cgiv
                                        on c.id = cgiv.contract_id
                                    where cgiv.is_approved = true
                                        and c.is_published = true
                                        and c.procurement_entity_id = ?
                        ) as contract_count,
                        (

                    select
                                    count(distinct att.foreign_key) as count
                                from attachments as att
                                where (att.foreign_key in (
                                        select
                                            cd.id as contract_details_id
                                        from contracts as c
                                        join contract_details as cd
                                            on cd.contract_id  = c.id
                                        join contract_details_verifications as cdv
                                            on cdv.contract_detail_id = cd.id
                                            and cdv.is_approved = true
                                            and c.is_published = true
                                            and c.procurement_entity_id = ?
                                                        )
                                    and att.field_name in (\'agreement\', \'contract_general_conditions\', \'contract_special_conditions\', \'technical_specification\'
                                     ,\'quantities_table_price_bill\', \'action_plan\', \'work_start_letter\',\'technical_maps\', \'no_objection_later_of_donor\'
                                     ,\'contract_performance_guarantee\')
                                     ) or (att.foreign_key in (
                                            select
                                                cpa.id as cpa_id
                                            from contracts as c
                                            join contract_progress_advances as cpa
                                                on cpa.contract_id = c.id
                                            where c.is_published = true
                                            and c.procurement_entity_id = ?
                                                        )
                                    and att.field_name = \'advance_payment_guarantee\'
                                            )
                                    or (att.foreign_key in (
                                            select
                                                cs.id as cs_id
                                            from contracts as c
                                            join contract_statuses as cs
                                                on cs.contract_id = c.id
                                            join contract_status_verifications as csv
                                                on csv.contract_status_id = cs.id
                                                and csv.is_approved = true
                                                and c.is_published = true
                                                and c.procurement_entity_id = ?
                                                        )
                                    and att.field_name in  (\'contract_closeout_report\', \'handover_report\')
                                        )

                                    or (att.foreign_key in (
                                            select
                                                comp.id as comp_id
                                            from contracts as c
                                            join company_general_informations as cgi
                                                on cgi.contract_id = c.id
                                                and cgi.is_approved = true
                                            join companies as comp
                                                on comp.company_general_information_id = cgi.id
                                                and cgi.is_approved = true
                                                and c.is_published = true
                                                and c.procurement_entity_id = ?
                                                    )
                                    and att.field_name = \'joint_venture_licence\'
                                        )
                                    or (att.foreign_key in (
                                                select
                                                    c.id as c_id
                                                from contracts as c
                                                join company_general_informations as cgi
                                                    on cgi.contract_id = c.id
                                                    and cgi.is_approved = true
                                                    and c.is_published = true
                                                    and c.procurement_entity_id = ?
                                    )
                                    and att.field_name = \'planned_payments_schedule\'
                                        )
                            ) as contracts_upload_documents
                        ) as t
                      ) as published_contracts_upload_percentage
        ', [$procurementEntityId, $procurementEntityId, $procurementEntityId, $procurementEntityId,
            $procurementEntityId, $procurementEntityId, $procurementEntityId, $procurementEntityId,
            $procurementEntityId, $procurementEntityId, $procurementEntityId, $procurementEntityId,
            $procurementEntityId, $procurementEntityId, $procurementEntityId]);
        $data['current_date'] = DB::select
        ('
                 select jdate(curdate()) as currentDate
            ');
        $data['current_date'] =
            is_array($data['current_date']) ?
                $data['current_date'][0]->currentDate :
                $data['current_date'];
        $contracts = Contract::where('procurement_entity_id', 6)->get();

        $projectIds = [];
        foreach ($contracts as $contract) {
            $projectIds[] = $contract['project_id'];
        }
        $request = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [], [
            'ids' => $projectIds
        ]);

        if ($request->status_code > 300) {
            throw new Exception('Error connecting to APPMS');
        }
        $projects = json_decode($request->body, true);

        $budgetCodeAggregates = [];
        foreach ($budgetTypes as $bt) {
            foreach ($budgetIfOperationals as $bio) {
                $slug = $bt['slug'] . ' ' . $bio['slug'];
                $budgetCodeAggregates[$slug] = [
                    'name_da' => $bt['name_da'] . ' ' . $bio['name_da'],
                    'grand_total_value' => 0
                ];
            }
        }

        $budgetRawCalculations = [];
        foreach ($projects as $p) {
            foreach ($budgetCodes as $bc) {
                if ($p['budget_code_id'] === $bc['id']) {
                    $budgetRawCalculations[] = [
                        'project_id' => $p['project_id'],
                        'procurement_type_id' => $p['procurement_type_id'],
                        'budget_code_fund_type' => $bc['budget_code_fund_type'],
                        'budget_code_optional_non_optional' => $bc['budget_code_optional_non_optional'],
                        'contract_grand_total_value' => 0
                    ];
                }
            }
        }

        foreach ($budgetRawCalculations as $k => $bc) {
            for ($i = 0; $i < sizeof($grandTotalValues); $i++) {
                if ($bc['project_id'] === $grandTotalValues[$i]->project_id) {
                    $budgetRawCalculations [$k]['contract_grand_total_value'] = $grandTotalValues[$i]->contract_grand_total_value;
                }
            }
        }
        $budgetCalculations = [];
        foreach ($procurementTypes as $pt) {
            $budgetCalculations[$pt['id']] = [
                'id' => $pt['id'],
                'slug' => $pt['slug'],
                'name_da' => $pt['name_da'],
                'budget_data' => null
            ];
        }
        foreach ($budgetRawCalculations as $brc) {
            if (empty($budgetCalculations[$brc['procurement_type_id']]['budget_data'])) {
                $budgetCalculations[$brc['procurement_type_id']]['budget_data'] = $budgetCodeAggregates;
            }
            $budgetCalculations[$brc['procurement_type_id']]['budget_data'][$brc['budget_code_fund_type']['slug'] . '-' . $brc['budget_code_optional_non_optional']['slug']] = +$brc['contract_grand_total_value'];
        }
        $tempHorizontalTitles = [];
        $tempRawData = [];
        $tempVerticalTitles = [];
        foreach ($budgetCalculations as $bc) {
            $tempHorizontalTitles [] = $bc['name_da'];
            $tempRawData[] = [
                $bc['budget_data']['operating optional']['grand_total_value'],
                $bc['budget_data']['operating non-optional']['grand_total_value'],
                $bc['budget_data']['development optional']['grand_total_value'],
                $bc['budget_data']['development non-optional']['grand_total_value'],
            ];
            $tempVerticalTitles[0] = $bc['budget_data']['operating optional']['name_da'];
            $tempVerticalTitles[1] = $bc['budget_data']['operating non-optional']['name_da'];
            $tempVerticalTitles[2] = $bc['budget_data']['development optional']['name_da'];
            $tempVerticalTitles[3] = $bc['budget_data']['development non-optional']['name_da'];
        }
        $tempData = [];
        for ($i = 0; $i < count($tempRawData); $i++) {
            $tempData[$i] = [];
            for ($j = 0; $j < count($tempRawData[$i]); $j++) {
                $tempData[$i][$j] = $tempRawData[$j][$i];
            }
        }
        $data['budget_information'] = [
            'vertical_titles' => $tempVerticalTitles,
            'horizontal_titles' => $tempHorizontalTitles,
            'data' => $tempData
        ];


        return $data;

    }
}
