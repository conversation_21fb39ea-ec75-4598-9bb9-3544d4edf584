<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractStatus extends Model
{
    public static $fields = [
        'status_id',
        'date',
        'remarks',
        'contract_id'
    ];
    protected $guarded = ['id'];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }

    public function contractor_performance_assessment()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractorPerformanceAssessment');
    }

    public function cancelled_contract()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusCancelled');
    }

    public function suspended_contract()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusSuspend');
    }

    public function not_signed_contract()
    {
        return $this->hasOne('NPA\ACPMS\Models\ContractStatusNotSigned');
    }

    public function contract_status_verifications()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusVerification');
    }

    public function attachments()
    {
        return $this->hasMany('NPA\ACPMS\Models\Attachment', 'foreign_key');
    }

    public function contract_status_log()
    {
        return $this->hasMany('NPA\ACPMS\Models\ContractStatusLog');
    }

}
