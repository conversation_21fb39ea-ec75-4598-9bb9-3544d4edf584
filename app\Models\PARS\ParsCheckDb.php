<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsCheckDb extends Model
{
    public static $fields = [
        'p_a_r_id',
        'actual_plan_physical_progress_diff',
        'actual_plan_payment_progress_diff',
        'is_performance_guaranty_received',
        'is_subcontracting_legal',
        'is_performance_guaranty_renewed',
        'is_completion_document_uploaded',
        'is_contract_termination_doc_issued',
        'is_supplied_legally_returned',
        'is_main_contract_have_subcontracts',
        'is_action_plan_uploaded',
        'renewed_performance_total_amount'
    ];

    protected $guarded = ['id'];


}
