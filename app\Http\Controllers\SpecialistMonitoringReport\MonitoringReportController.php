<?php

namespace NPA\ACPMS\Http\Controllers\SpecialistMonitoringReport;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use NPA\ACPMS\Helpers\Date;
use NPA\ACPMS\Helpers\Error;
use NPA\ACPMS\Helpers\Helper;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Helpers\SpecialistMonitoringGetReport;
use NPA\ACPMS\Http\Controllers\AttachmentController;
use NPA\ACPMS\Http\Controllers\Controller;
use NPA\ACPMS\Mail\SpecialistMonitoringReport;
use NPA\ACPMS\Models\Attachment;
use NPA\ACPMS\Models\MonitoringReport;
use NPA\ACPMS\Models\PARS\ParsAdvancePaymentGuarantie;
use NPA\ACPMS\Models\PARS\ParsChallengesAndRemark;
use NPA\ACPMS\Models\PARS\ParsChart;
use NPA\ACPMS\Models\PARS\ParsCheckDb;
use NPA\ACPMS\Models\PARS\ParsConditionAmendment;
use NPA\ACPMS\Models\PARS\ParsContractDonor;
use NPA\ACPMS\Models\PARS\ParsContractBudgetCode;
use NPA\ACPMS\Models\PARS\ParsContractDuration;
use NPA\ACPMS\Models\PARS\ParsContractExecutionLocation;
use NPA\ACPMS\Models\PARS\ParsContractPerformanceSecurity;
use NPA\ACPMS\Models\PARS\ParsContractProgress;
use NPA\ACPMS\Models\PARS\ParsContractProgressStop;
use NPA\ACPMS\Models\PARS\ParsContractValue;
use NPA\ACPMS\Models\PARS\ParsCostAmendment;
use NPA\ACPMS\Models\PARS\ParsDefectLiability;
use NPA\ACPMS\Models\PARS\ParsDelayPenaltie;
use NPA\ACPMS\Models\PARS\ParsGeneralInformation;
use NPA\ACPMS\Models\PARS\ParsRemark;
use NPA\ACPMS\Models\PARS\ParsTimeAmendment;
use NPA\ACPMS\Models\PARS\ParsCostAndTimeAmendment;
use NPA\ACPMS\Models\PARS\ParsVendor;
use NPA\ACPMS\Models\PARS\ProgressAnalysisReport;

class MonitoringReportController extends Controller
{
    private $files = [
        'monitoring_reporting',
    ];


    public function index(Request $request)
    {
        $contractId = $request->query('contract_id');
        $monitoringReports = MonitoringReport::where('contract_id', $contractId)->get();
        if (count($monitoringReports) < 1) {
            return response()->json([], 404);
        }
        foreach ($monitoringReports as $monitoringReport){
            $attachments = $monitoringReport->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index) {
                        array_push($att, $v);
                    }
                }
                $monitoringReport[$attachments[$key]['field_name']] = $att;
            }
        }
        return response()->json($monitoringReports, 200);
    }


    public function store(Request $request)
    {
        $data = $request->all();
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();
            $monitoringData = MonitoringReport::create(array_only($data, MonitoringReport::$fields));
            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $monitoringData, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 201,
                [
                    'location' => $monitoringData['id'],
                ]
            );

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }

    }

    public function showOne($monitoringReportId)
    {
        try {
            $monitoringData = MonitoringReport::where('id', $monitoringReportId)->first();
            if (!$monitoringData) {
                return response()->json([], 404);
            }
            $attachments = $monitoringData->attachments()->select('id', 'original_name', 'assigned_name', 'field_name', 'table_name', 'created_at')->get();
            foreach ($attachments as $key => $value) {
                $index = $attachments[$key]['field_name'];
                $att = [];
                foreach ($attachments as $k => $v) {
                    if ($v['field_name'] == $index && $v['table_name'] == 'monitoring_reports') {
                        array_push($att, $v);
                    }
                }
                $monitoringData[$attachments[$key]['field_name']] = $att;
            }
            return response()->json($monitoringData, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }


    public function update(Request $request, $id)
    {

        $data = $request->all();
        $files = array_only($data, $this->files);
        try {
            DB::beginTransaction();

            $monitoringReport = MonitoringReport::where('id', $id)->first();
//            $data['contract_detail_id'] = $monitoringReport->id;

            MonitoringReport::where('id', $id)->update(array_only($data, MonitoringReport::$fields));

            foreach ($files as $fieldName => $fileContent) {
                if ($fileContent !== null) {
                    AttachmentController::saveFile($request, $monitoringReport, $fieldName, $fileContent);
                }
            }
            DB::commit();
            return response()->json([], 204);

        } catch (\Throwable $t) {
            DB::rollBack();
            return Error::composeResponse($t);
        }
    }

    public function destroy($monitoringReportId)
    {
        try {
            $rowsCount = MonitoringReport::destroy($monitoringReportId);
            if ($rowsCount < 1) {
                throw Error::exceptionNotFound();
            }
            return response(null, 204);
        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }

    }
}
