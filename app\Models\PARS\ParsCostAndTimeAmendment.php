<?php

namespace NPA\ACPMS\Models\PARS;

use Illuminate\Database\Eloquent\Model;

class   ParsCostAndTimeAmendment extends Model
{
    public static $fields = [
        'p_a_r_id',
        'peshnehad_decision_number',
        'amendment_approval_date',
        'amendment_reasons',
        'amendment_start_date',
        'amended_end_date',
        'amendment_duration',
        'contract_duration_after_amend',
        'amended_performance_security_end_date',
        'is_performance_guaranty_renewal_is_legal',
        'amended_performance_security_amount',
        'amendment_amount',
        'contract_value_after_amend'

    ];

    protected $guarded = ['id'];
}
