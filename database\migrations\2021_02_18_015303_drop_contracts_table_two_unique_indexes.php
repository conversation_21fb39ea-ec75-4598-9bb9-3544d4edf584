<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DropContractsTableTwoUniqueIndexes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
      Schema::table('contracts', function (Blueprint $table) {
        $table->dropUnique('contracts_npa_identification_number_unique');
        $table->dropUnique('contracts_project_id_unique');
      });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
      Schema::table('contracts', function (Blueprint $table) {
        $table->unique('contracts_npa_identification_number_unique');
        $table->unique('contracts_project_id_unique');
      });
    }
}
