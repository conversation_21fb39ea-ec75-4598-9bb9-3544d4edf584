<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use NPA\CommonDataManager\Helpers\Query;
use NPA\CommonDataManager\Models\CommonValue;

class CreateTempZonesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('temp_zones', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('order')
                ->nullable();
            $table->string('name_da')
                ->unique();
            $table->string('name_pa')
                ->unique();
            $table->string('name_en')
                ->unique();
            $table->string('slug')
                ->unique();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('temp_zones');
    }
}
