<?php
/**
 * Created by PhpStorm.
 * User: Nikzad
 * Date: 11/28/2017
 * Time: 4:25 AM
 */

namespace NPA\ACPMS\Helpers;


use Illuminate\Support\Facades\Log;
use Throwable;

class Error extends \Exception
{
    private $npaCode;

    public function __construct($code = '|||NPA-GENERIC-0000|||', $message = "", Throwable $previous = null)
    {
        $this->npaCode = $code;
        parent::__construct($message, 500, $previous);
    }

    public function getNpaCode()
    {
        return $this->npaCode;
    }

    static function exceptionNotFound()
    {
        return new \Exception('Not found');
    }

    static function exceptionInsufficientParameters()
    {
        return new \Exception('Insufficient parameters');
    }

    static function responseNotFound(\Throwable $t)
    {
        return response()->json([
            'code' => 3,
            'message' => 'یافت نشد',
            'details' => $t->getMessage()
        ], 404);
    }

    static function responseInsufficientParameters(\Throwable $t)
    {
        return response()->json([
            'code' => 4,
            'slug' => 'insufficient-parameters',
            'message' => '',
            'details' => $t->getMessage()
        ], 409);
    }

    static function composeResponse(\Throwable $t, $code = null, $extraInfo = '')
    {

        $appEnv = env('APP_ENV');
        Log::error('>>>>>>>>>>>>>>>>>>>>>>>>>\n' . $t->getMessage() . '\n' . $t->getTraceAsString());
        Log::error('\n==========================\n' . $extraInfo . '\n==========================\n');
        if ($code) {
//            dd('here');
            return response()->json([
                'code' => $code,
                'slug' => self::getSlug($code),
                'message' => 'خطا در ارتباط با دیگر سرورها',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $t->getMessage(),
                    'stackTrace' => $t->getTrace()
                ]
            ], 409);
        }

        if (str_contains($t->getMessage(), 'Not found')) {
            return self::responseNotFound($t);
        } else if (str_contains($t->getMessage(), 'Integrity constraint violation')) {
            return response()->json([
                'code' => 2,
                'slug' => 'duplicate',
                'message' => '',
                'message' => 'خطا در ارتباط با دیگر سرورها',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $t->getMessage(),
                    'stackTrace' => $t->getTrace()
                ]
            ], 409);
        } else if (str_contains($t->getMessage(), 'Insufficient parameters')) {
            self::responseInsufficientParameters($t);
        } else if (str_contains($t->getMessage(), 'input out of range')) {
            return response()->json([
                'code' => 3,
                'slug' => 'input out of range',
                'message' => '',
                'message' => 'خطا در ارتباط با دیگر سرورها',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $t->getMessage(),
                    'stackTrace' => $t->getTrace()
                ]
            ], 409);
        } else {

            return response()->json([
                'code' => 1,
                'slug' => 'generic-error',
                'message' => '',
                'message' => 'خطا در ارتباط با دیگر سرورها',
                'details' => $appEnv !== 'local' ? '' : [
                    'message' => $t->getMessage(),
                    'stackTrace' => $t->getTrace()
                ]
            ], 400);
        }
    }

    private static function getSlug($code)
    {
        return ([
            '|||NPA-ACPMS-0001|||' => 'acpms-contract-planning-all-tables-not-confirmed-for-contract-manager',
            '|||NPA-ACPMS-0002|||' => 'acpms-contract-planning-generally-not-confirmed-for-award-authority',
            '|||NPA-ACPMS-0003|||' => 'acpms-contract-planning-enforce-generally-not-confirmed-for-contract-manager',
            '|||NPA-ACPMS-0004|||' => 'acpms-contract-planning-validation-before-un-confirm',
            '|||NPA-ACPMS-0005|||' => 'acpms-contract-planning-validation-before-un-approve',
            '|||NPA-ACPMS-0007|||' => 'acpms-contract-planning-check-general-approve',
            '|||NPA-ACPMS-0009|||' => 'acpms-not-allow-invalid-date-format',
            '|||NPA-ACPMS-PLANNING-0001|||' => 'acpms-contract-planning-contract_approval_verifications',
            '|||NPA-ACPMS-PLANNING-0002|||' => 'acpms-contract-planning-p_e_contact_person_verifications',
            '|||NPA-ACPMS-PLANNING-0003|||' => 'acpms-contract-planning-contract_execution_verifications',
            '|||NPA-ACPMS-PLANNING-0004|||' => 'acpms-contract-planning-contract_details_verifications',
            '|||NPA-ACPMS-PLANNING-0005|||' => 'acpms-contract-planning-company_general_information',
            '|||NPA-ACPMS-PLANNING-0006|||' => 'acpms-contract-planning-subcontract_verifications',
            '|||NPA-ACPMS-PLANNING-0007|||' => 'acpms-contract-planning-contract_planning_f_a_a_p_p_verifications',
            '|||NPA-ACPMS-PLANNING-0008|||' => 'acpms-contract-planning-liquidated_damages_delivery_verifications',
            '|||NPA-ACPMS-PLANNING-0009|||' => 'acpms-contract-planning-company_general_information-companies',
            '|||NPA-ACPMS-PLANNING-0010|||' => 'acpms-contract-planning-company_general_information-companies_company_bank_information',
            '|||NPA-ACPMS-0006|||' => 'not-authorized-to-change-threshold-and-npa_identification_number',
            '|||NPA-ACPMS-0008|||' => 'acpms-companies-before-insert',
            '|||NPA-ACPMS-0010|||' => 'contract-not-published',
            '|||NPA-ACPMS-0011|||' => 'remarks-not-inserted',
            '|||NPA-ACPMS-0012|||' => 'cannot-publish-in-pending-status',
            500 => 'generic-integration-error'
        ])[$code];
    }

    public static function extractCustomErrorCode(Throwable $t)
    {
        $matches = '';
        preg_match('/\|\|\|.*\|\|\|/', $t->getMessage(), $matches);
        return is_array($matches) && array_key_exists(0, $matches) ? $matches[0] : null;

    }

}

