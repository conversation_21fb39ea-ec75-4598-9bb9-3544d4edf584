<?php

namespace NPA\ACPMS\Http\Controllers\API\Internal\SIGTAS;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NPA\ACPMS\Helpers\DropDowns;
use NPA\ACPMS\Helpers\Http;
use NPA\ACPMS\Http\Controllers\Controller;

class SigtasController extends Controller
{
    public function contracts(Request $request)
    {
        $date = $request->query('date');
        $startDate = null;
        $endDate = null;
        $condition = '';
        $update = false;
        $isUpdated = '1>2';
        if ($date) {
            if (!preg_match("/^[0-9]{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2][0-9]|3(0|1))$/", $date)) {
                return response()->json(['error' => 'invalid date'], 400);
            }
            $condition .= ' and (
                    DATE_FORMAT(c.created_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    or
                    DATE_FORMAT(a.created_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    or
                    DATE_FORMAT(c.updated_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    or
                    DATE_FORMAT(a.updated_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    )
            ';

            $isUpdated = 'DATE_FORMAT(c.updated_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    or
                    DATE_FORMAT(a.updated_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")';

            $startDate = $date . " 00:00:00";
            $endDate = $date . " 23:59:59";
        }

        $procurementEntities = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/sectorAndProcurementEntity/procurementEntity');
        $contractStatuses = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/contractStatus');
        $currencies = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/currency');

        $contracts = DB::select('
                    select
                        c.id,
                        c.currency_id,
                        c.exchange_rate,
                        c.contract_number as contract_no,
                        c.project_id,
                        com.license_number as license_number,
                        c.procurement_entity_id ,
                        ifnull(cp.actual_start_date, cd.planned_start_date) as start_date,
                        cd.planned_end_date as end_date,
                        cd.actual_value as total_amount,
                        ap.actual_amount as paid_amount,
                        csl_max.status_id as status_id,
                        fcel.foreign_country_location as country,
                        fcel.foreign_city_location as city,
                        if(' . $isUpdated . ', true, false) as updated
                    from contracts as c
                    left join contract_details as cd
                      on cd.contract_id = c.id
                    left join contract_progresses as cp
                      on c.id = cp.contract_id
                    left join amendments as a
                      on c.id = a.contract_id and
                      a.is_published = true
                    left join (
                        select sum(amount) as actual_amount, contract_id
                        from contract_progress_payments
                        where is_published = true
                        group by contract_id
                    ) as ap
                      on ap.contract_id = c.id
                    join (
                        select
                            csl.id, csl.contract_id, csl.status_id
                        from contract_status_logs as csl
                        join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                        on csl.id = max_value.max_id
                    ) as csl_max
                        on csl_max.contract_id = c.id

                    left join foreign_contract_execution_locations as fcel
                      on fcel.contract_id = c.id
                    left join (
                        select max(company.license_number) as license_number, cgi.contract_id
                        from company_general_informations as cgi
                        left join companies as company
                          on company.company_general_information_id = cgi.id
                        where company.joint_venture_company_role_id is null or company.joint_venture_company_role_id = 1
                        group by cgi.contract_id
                    ) as com
                      on com.contract_id = c.id
                    where c.is_published = true ' . $condition . '


            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);

        $projectIds = [];
        $companyLicenses = [];
        foreach ($contracts as $contract) {
            $projectIds[] = $contract->project_id;
            if ($contract->license_number) {
                $companyLicenses[] = $contract->license_number;
            }
        }
        $projectsRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_AETS_BASE_URL') . 'api/internal/cpms/general-info', [],
            ['ids' => $projectIds]
        );
        if ($projectsRequest->status_code >= 300) {
            throw new Exception('Problem getting data from APPMS');
        }
        $projects = json_decode($projectsRequest->body, true);
        $companies = [];
        if (count($companyLicenses) !== 0) {
            $companyRequest = Http::post(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/specific/company/searchByLicenses', [],
                ['licenses' => $companyLicenses]
            );

            if ($companyRequest->status_code >= 300) {
                throw new Exception('Problem getting data from CDM');
            }
            $decodedData = json_decode($companyRequest->body, true);
            $companies = $decodedData ? $decodedData : [];
        }

        foreach ($contracts as &$contract) {
            $contract->subject = null;
            foreach ($projects as $project) {
                if ($contract->project_id == $project['project_id']) {
                    $contract->subject = $project['project_name_da'];
                }
            }
            $contract->contractor_tin = null;
            $contract->contractor_name = null;
            foreach ($companies as $company) {
                if ($contract->license_number == $company['licence_number']) {
                    $contract->contractor_tin = $company['tin'];
                    $contract->contractor_name = $company['name_da'];
                }
            }
            $contract->license_no = $contract->license_number;
            $contract->phone_no = null;
            $contract->contractee_tin = null;
            $contract->contractee_name = null;
            foreach ($procurementEntities as $entity) {
                if ($contract->procurement_entity_id == $entity['id']) {
                    $contract->contractee_name = $entity['name_da'];
                }
            }
            if ($contract->city) {
                $contract->contract_location = [
                    [
                        ['city' => $contract->city],
                        ['country' => $contract->country],
                    ],
                ];
            }
            $location = DB::select('
                    select
                      tp.name_da as province, td.name_da as district, domistic.village, "Afghanistan" as country
                    from  domestic_contract_execution_locations as domistic
                    join temp_districts as td
                    on td.id = domistic.district_id
                    join temp_provinces as tp
                    on tp.id = td.temp_province_id
                    where domistic.contract_id = ?
            ', [$contract->id]);
            if (count($location) != 0) {
                $contract->contract_location = $location;
            }
            $contract->contract_Status = null;
            foreach ($contractStatuses as $status) {
                if ($contract->status_id == $status['id']) {
                    $contract->contract_Status = $status['name_da'];
                }
            }
            $contract->currency = null;
            foreach ($currencies as $currency) {
                if ($contract->currency_id == $currency['id']) {
                    $contract->currency = $currency['name_da'];
                }
            }
            unset($contract->id);
            unset($contract->project_id);
            unset($contract->license_number);
            unset($contract->procurement_entity_id);
            unset($contract->status_id);
            unset($contract->city);
            unset($contract->country);
            unset($contract->currency_id);
        }

        return response()->json($contracts, 200);

    }

    public function amendments(Request $request)
    {

        $date = $request->query('date');
        $startDate = null;
        $endDate = null;
        $condition = '';
        if ($date) {
            if (!preg_match("/^[0-9]{4}-(0?[1-9]|1[0-2])-(0?[1-9]|[1-2][0-9]|3(0|1))$/", $date)) {
                return response()->json(['error' => 'invalid date'], 400);
            }
            $condition .= ' and (
                    DATE_FORMAT(a.created_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    or
                    DATE_FORMAT(a.updated_at, "%Y-%m-%d %H:%i:%s") between
                    DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    and DATE_FORMAT(?, "%Y-%m-%d %H:%i:%s")
                    )
            ';

            $startDate = $date . " 00:00:00";
            $endDate = $date . " 23:59:59";
        }
        $amendmentTypes = DropDowns::getAllValues(config('custom.CUSTOM_API_INTERNAL_CDM_BASE_URL') . 'api/dropDown/amendmentType');
        $amendments = DB::select('
                    select
                        c.contract_number as contract_no,
                        a.type_id,
                        ifnull(tca.amendment_amount, ca.amendment_amount) as amount,
                        ifnull(tca.replanning_start_date, ta.replanning_start_date) as start_date,
                        ifnull(tca.amended_end_date, ta.amended_end_date) as end_date

                    from amendments as a
                    left join cost_amendments as ca
                      on a.id = ca.amendment_id
                    left join time_amendments as ta
                      on a.id = ta.amendment_id
                    left join time_and_cost_amendments as tca
                      on a.id = tca.amendment_id
                    join contracts as c
                      on c.id = a.contract_id
                    where c.is_published  = true and a.is_published = true and a.type_id != 4 ' . $condition . '
            ', [$startDate, $endDate, $startDate, $endDate]);
        foreach ($amendments as $amendment) {
            foreach ($amendmentTypes as $type) {
                if ($amendment->type_id == $type['id']) {
                    $amendment->amendment_type = $type['name_da'];
                }
            }
            unset($amendment->type_id);
        }

        return response()->json($amendments, 200);

    }

    public function contractsv2(Request $request)
    {

        $date = $request->query('date');
        $amount = $request->query('amount');
        $phone_number = $request->query('phone_number');
        $contractor_tin = $request->query('contractor_tin');
        $contractee_tin = $request->query('contractee_tin');
        $contract_number = $request->query('contract_number');

        // $startDate = null;
        // $endDate = null;
        // $condition = '';
        // $update = false;
        // $isUpdated = '1>2';

        $conditions = "";
        if ($date != '') {
            $conditions .= " AND cd.planned_start_date='" . $date . "' ";
        }

        if ($amount != '') {
            $conditions .= " AND ( (ifnull(cd.actual_value, 0)  + ifnull(amendment.amount, 0)) * if(c.exchange_rate, c.exchange_rate, 1) )='" . $amount . "' ";
        }

        //we do not have company phone numbers
        // if ($phone_number != '') {
        //     $conditions .= " AND phone_number=" . $phone_number;
        // }

        if ($contractor_tin != '') {
            $conditions .= " AND c3.tin='" . $contractor_tin . "' ";
        }

        if ($contract_number != '') {
            $conditions .= " AND c.contract_number='" . $contract_number . "' ";
        }

        if ($contractee_tin != '') {
            $conditions .= " AND c4.ocds_id='" . $contractee_tin . "' ";
        }

        // dd($conditions);
        $q = "SELECT
            c.contract_number AS 'Contract_No',
            c.exchange_rate as 'EXCHANGE_RATE',
            cd.planned_start_date as 'START_DATE',
            cd.planned_end_date as 'END_DATE',
            ( (ifnull(cd.actual_value, 0)  + ifnull(amendment.amount, 0)) * if(c.exchange_rate, c.exchange_rate, 1) ) as 'TOTAL_AMOUNT',
            IFNULL(ap2.project_name_da,
            c5.name_da) AS 'SUBJECT/CONTRACT_NAME',
            c3.tin as 'CONTRACTOR_TIN',
            c3.name_da AS 'CONTRACTOR_NAME',
            c3.licence_number AS 'LICENSE_NO',
            c4.name_da as 'CONTRACTEE_NAME',
            cs1.name_da as 'CONTRACT_STATUS',
            cs2.name_da as 'CURRENCY',
            c4.ocds_id AS 'CONTRACTEE_TIN',
            cd.agreement_signature_date AS 'Contract Signing Date'
          FROM
            contracts c
          JOIN contract_details cd ON
            c.id = cd.contract_id

          left join (
          select
          (sum(ifnull(ca.amendment_amount, 0)) + sum(ifnull(tca.amendment_amount,0))) as amount,
                                  a.contract_id as contract_id
                          from amendments as a
                          left join cost_amendments as ca
                              on ca.amendment_id = a.id
                          left join time_and_cost_amendments as tca
                              on tca.amendment_id = a.id
                          where a.is_approved = true
                          group by  a.contract_id

          ) as amendment on c.id=amendment.contract_id

          left join (
                              select
                                  csl.id, csl.contract_id, csl.status_id
                              from contract_status_logs as csl
                              join ( SELECT contract_id, max(id) as max_id FROM contract_status_logs group by contract_id) as max_value
                                  on csl.id = max_value.max_id
                          ) as cs
                          on cs.contract_id = c.id
          LEFT JOIN appms_v2.v2_projects ap2 ON
            c.contract_number = ap2.contract_number
          left JOIN company_general_informations cgi ON
            c.id = cgi.contract_id
          left JOIN companies c2 ON
            c2.company_general_information_id = cgi.id
          left JOIN cdm.companies c3 ON
            c2.license_number = c3.licence_number
          left JOIN cdm.procurement_entities c4 ON
            c.procurement_entity_id = c4.id

            left join cdm.currencies cs2 on c.currency_id=cs2.id

          LEFT JOIN appms.projects c5 ON
            c.contract_number = c5.contract_number
            left join cdm.contract_statuses cs1 on cs.status_id=cs1.id
          WHERE
            c.is_published = 1
            AND c.has_requested_to_unpublish = 0
            " . $conditions . "

          ORDER BY
            cd.agreement_signature_date ASC";

        $contracts = \DB::select($q);
        for ($i = 0; $i < count($contracts); $i++) {
            $q = "

            select

            c.contract_number as 'CONCTRACT_NO',
            tp.name_da as 'PROVINCE',
            td.name_da as 'DISTRICT',
            dcel.village as 'VILLAGE'
            from contracts c
            left join domestic_contract_execution_locations dcel  on c.id=dcel.contract_id
            left join temp_districts td  on dcel.district_id =td.id
            left join temp_provinces tp  on   td.temp_province_id =tp.id

            #left join foreign_contract_execution_locations fcel  on c.id=fcel.contract_id

            where 	c.is_published = 1
                AND c.has_requested_to_unpublish = 0 AND c.contract_number=\"" . $contracts[$i]->Contract_No . "\"";
            $locations = \DB::select($q);
            $contracts[$i]->location = $locations;
        }
        dd($contracts);
        // dd(\DB::select($q));

        return response()->json($contracts, 200);

    }
}
