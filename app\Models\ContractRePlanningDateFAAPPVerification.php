<?php

namespace NPA\ACPMS\Models;

use Illuminate\Database\Eloquent\Model;

class ContractRePlanningDateFAAPPVerification extends Model
{
    protected $guarded = ['id'];

    public static $fields = [
        'contract_re_planning_date_f_a_a_p_p_id',
        'is_approved',
        'is_confirmed',
        'is_published',
        'published_date'
    ];

    public function contract()
    {
        return $this->belongsTo('NPA\ACPMS\Models\Contract');
    }
}
