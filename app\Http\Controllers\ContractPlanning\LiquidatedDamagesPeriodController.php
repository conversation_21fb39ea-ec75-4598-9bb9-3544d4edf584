<?php

namespace NPA\ACPMS\Http\Controllers\ContractPlanning;

use NPA\ACPMS\Http\Controllers\Controller;
use Illuminate\Http\Request;
use NPA\ACPMS\Models\LiquidatedDamagesPeriod;

class LiquidatedDamagesPeriodController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \NPA\ACPMS\LiquidatedDamagesPeriod  $liquidatedDamagesPeriod
     * @return \Illuminate\Http\Response
     */
    public function show(LiquidatedDamagesPeriod $liquidatedDamagesPeriod)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \NPA\ACPMS\LiquidatedDamagesPeriod  $liquidatedDamagesPeriod
     * @return \Illuminate\Http\Response
     */
    public function edit(LiquidatedDamagesPeriod $liquidatedDamagesPeriod)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \NPA\ACPMS\LiquidatedDamagesPeriod  $liquidatedDamagesPeriod
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, LiquidatedDamagesPeriod $liquidatedDamagesPeriod)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \NPA\ACPMS\LiquidatedDamagesPeriod  $liquidatedDamagesPeriod
     * @return \Illuminate\Http\Response
     */
    public function destroy(LiquidatedDamagesPeriod $liquidatedDamagesPeriod)
    {
        //
    }
}
