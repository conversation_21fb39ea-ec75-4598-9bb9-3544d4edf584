<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRemarkMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('remark_messages', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('remark_id');
            $table->foreign('remark_id')
                ->references('id')
                ->on('remarks')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->unsignedInteger('alert_id');
            $table->foreign('alert_id')
                ->references('id')
                ->on('alerts')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->text('contents');

            $table->unsignedInteger('creator_user_id');
            $table->foreign('creator_user_id')
                ->references('id')
                ->on('users')
                ->onDelete('cascade')
                ->onUpdate('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('remark_messages');
    }
}
